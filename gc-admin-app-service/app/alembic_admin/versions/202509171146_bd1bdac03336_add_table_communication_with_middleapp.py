"""add table communication with middleapp

Revision ID: bd1bdac03336
Revises: 67a1d5c81fe2
Create Date: 2025-09-17 11:46:08.479869

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "bd1bdac03336"
down_revision: Union[str, None] = "67a1d5c81fe2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "m_medical_devices",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "device_type",
            sa.String(),
            nullable=False,
            comment="Enums: MedicalDeviceType, BFA, EMG,....",
        ),
        sa.Column("name", sa.String(), nullable=False, comment="Name medical device"),
        sa.Column(
            "description",
            sa.String(),
            nullable=True,
            comment="Description Medical Device",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("device_type"),
    )

    op.create_table(
        "process_file_logs",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "source",
            sa.String(),
            nullable=False,
            comment="Enums: SourceProcessFileLog (MIDDLEWARE_MEDICAL_APP, ...)",
        ),
        sa.Column(
            "file_path", sa.String(), nullable=False, comment="S3 path or local path"
        ),
        sa.Column(
            "file_type",
            sa.String(),
            nullable=False,
            comment="Enums: FileTypeProcessFileEnum",
        ),
        sa.Column(
            "status",
            sa.String(),
            nullable=False,
            comment="Enums: StatusProcessFileEnum",
        ),
        sa.Column(
            "result",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="JSONB result payload (parsed rows, keys, etc.)",
        ),
        sa.Column(
            "error_message",
            sa.String(),
            nullable=True,
            comment="Error message if FAILED",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )

    op.create_table(
        "patient_medical_devices",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "tenant_uuid",
            sa.UUID(),
            nullable=False,
            comment="Foreign key to the TenantClinic table",
        ),
        sa.Column(
            "clinic_no",
            sa.String(),
            nullable=False,
            comment="Identifier for the business establishment/clinic",
        ),
        sa.Column(
            "patient_user_id",
            sa.Integer(),
            nullable=False,
            comment="ID of the patient in the internal system (if available)",
        ),
        sa.Column(
            "device_type",
            sa.String(),
            nullable=False,
            comment="Enums: MedicalDeviceType, BFA, EMG,....",
        ),
        sa.Column(
            "device_data_id",
            sa.String(),
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column(
            "external_patient_no",
            sa.String(),
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "examination_date",
            sa.Date(),
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "raw_data",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="Data medical device",
        ),
        sa.Column(
            "image_file_name", sa.String(), nullable=True, comment="File image name"
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["tenant_uuid"],
            ["tenant_clinics.tenant_uuid"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "clinic_no",
            "patient_user_id",
            "device_type",
            "device_data_id",
            name="uq_patient_medical_device_per_device",
        ),
    )

    # add master medical device
    medical_devices = [
        {
            "device_type": "BFA",
            "name": "Bite Force Analyzer",
            "description": (
                "Measure the overall and side-specific bite force of patients to assess chewing function. "
                "Provides maximum and average bite force values for different jaw regions."
            ),
        },
        {
            "device_type": "EMG",
            "name": "Wearable Electromyograph",
            "description": (
                "Record electrical activity of facial and jaw muscles. "
                "Monitor clenching, evaluate muscle activity intensity and duration, "
                "support diagnosis of bruxism and temporomandibular disorders, "
                "and track treatment or stress-related effects."
            ),
        },
        {
            "device_type": "MVT",
            "name": "Motion Trainer",
            "description": (
                "Train and evaluate mandibular movement. "
                "Track jaw movements in multiple directions (sagittal, horizontal, vertical). "
                "Helps detect abnormalities in jaw motion."
            ),
        },
        {
            "device_type": "BTE",
            "name": "Bite Eye",
            "description": (
                "Evaluate contact areas between teeth during occlusion. "
                "Provides multi-level pressure measurements at contact points, "
                "supports occlusal balance assessment after orthodontic or restorative treatments."
            ),
        },
    ]

    op.bulk_insert(
        sa.table(
            "m_medical_devices",
            sa.column("device_type", sa.String),
            sa.column("name", sa.String),
            sa.column("description", sa.String),
        ),
        medical_devices,
    )


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("patient_medical_devices")
    op.drop_table("process_file_logs")
    op.drop_table("m_medical_devices")
    op.execute(
        "DELETE FROM m_medical_devices WHERE device_type IN ('BFA', 'EMG', 'MVT', 'BTE');"
    )
    # ### end Alembic commands ###
