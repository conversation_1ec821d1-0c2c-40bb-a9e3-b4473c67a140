"""Add field is_active to model m_pricing

Revision ID: be6133c37371
Revises: bed863eccddd
Create Date: 2025-09-03 16:53:46.950112

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "be6133c37371"  # pragma: allowlist secret
down_revision: Union[str, None] = "bed863eccddd"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "m_pricing_storages",
        sa.Column(
            "is_active", sa.<PERSON>(), nullable=False, server_default=sa.text("TRUE")
        ),
    )
    op.add_column(
        "tenant_extra_storages",
        sa.Column(
            "status",
            sa.Integer(),
            nullable=False,
            server_default="3",
            comment="Enums TenantExtraStorageStatus",
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tenant_extra_storages", "status")
    op.drop_column("m_pricing_storages", "is_active")
    # ### end Alembic commands ###
