"""Add fields for Tenant Clinics Model

Revision ID: 590488c56790
Revises: f77111480777
Create Date: 2025-09-11 16:34:12.172857

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "590488c56790"
down_revision: Union[str, None] = "f77111480777"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "tenant_clinics", sa.Column("phone_number", sa.String(), nullable=True)
    )
    op.add_column("tenant_clinics", sa.Column("email", sa.String(), nullable=True))
    op.add_column("tenant_clinics", sa.Column("address_1", sa.String(), nullable=True))
    op.add_column("tenant_clinics", sa.Column("address_2", sa.String(), nullable=True))
    op.add_column("tenant_clinics", sa.Column("address_3", sa.String(), nullable=True))
    op.add_column("tenant_clinics", sa.Column("latitude", sa.String(), nullable=True))
    op.add_column("tenant_clinics", sa.Column("longitude", sa.String(), nullable=True))
    op.add_column("tenant_clinics", sa.Column("logo_url", sa.String(), nullable=True))
    op.add_column(
        "tenant_clinics", sa.Column("opening_hours", sa.JSON(), nullable=True)
    )

    op.alter_column("tenant_clinics", "tenant_name", new_column_name="clinic_name")
    op.alter_column("tenant_clinics", "tenant_slug", new_column_name="clinic_no")

    op.drop_constraint(
        "tenant_clinics_tenant_name_key", "tenant_clinics", type_="unique"
    )
    op.drop_constraint(
        "tenant_clinics_tenant_slug_key", "tenant_clinics", type_="unique"
    )
    op.create_unique_constraint(
        "tenant_clinics_clinic_name_key", "tenant_clinics", ["clinic_name"]
    )
    op.create_unique_constraint(
        "tenant_clinics_clinic_no_key", "tenant_clinics", ["clinic_no"]
    )

    op.drop_column("tenant_clinics", "plan_id")
    op.drop_column("tenant_clinics", "business_number")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "tenant_clinics",
        sa.Column(
            "business_number",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Identifier for the business establishment/clinic",
        ),
    )
    op.add_column(
        "tenant_clinics",
        sa.Column("plan_id", sa.INTEGER(), autoincrement=False, nullable=True),
    )

    op.alter_column("tenant_clinics", "clinic_name", new_column_name="tenant_name")
    op.alter_column("tenant_clinics", "clinic_no", new_column_name="tenant_slug")

    op.drop_constraint(
        "tenant_clinics_clinic_name_key", "tenant_clinics", type_="unique"
    )
    op.drop_constraint("tenant_clinics_clinic_no_key", "tenant_clinics", type_="unique")
    op.create_unique_constraint(
        "tenant_clinics_tenant_slug_key", "tenant_clinics", ["tenant_slug"]
    )
    op.create_unique_constraint(
        "tenant_clinics_tenant_name_key", "tenant_clinics", ["tenant_name"]
    )

    op.drop_column("tenant_clinics", "opening_hours")
    op.drop_column("tenant_clinics", "logo_url")
    op.drop_column("tenant_clinics", "longitude")
    op.drop_column("tenant_clinics", "latitude")
    op.drop_column("tenant_clinics", "address_3")
    op.drop_column("tenant_clinics", "address_2")
    op.drop_column("tenant_clinics", "address_1")
    op.drop_column("tenant_clinics", "email")
    op.drop_column("tenant_clinics", "phone_number")
    # ### end Alembic commands ###
