"""remove flow medical device

Revision ID: f7b07cde2f9c
Revises: 590488c56790
Create Date: 2025-09-12 15:41:36.950461

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "f7b07cde2f9c"  # pragma: allowlist secret
down_revision: Union[str, None] = "590488c56790"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("medical_device_bte")
    op.drop_table("medical_device_emg")
    op.drop_table("medical_device_mvt")
    op.drop_table("medical_device_sync_logs")
    op.drop_index("ix_import_file_logs_source", table_name="import_file_logs")
    op.drop_table("import_file_logs")
    op.drop_table("medical_device_bfa")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "medical_device_bfa",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "business_number",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Identifier for the business establishment/clinic",
        ),
        sa.Column(
            "external_patient_no",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "device_data_id",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column(
            "tenant_uuid",
            sa.UUID(),
            autoincrement=False,
            nullable=False,
            comment="Foreign key to the TenantClinic table",
        ),
        sa.Column(
            "patient_user_id",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="ID of the patient in the internal system (if available)",
        ),
        sa.Column(
            "examination_date",
            sa.DATE(),
            autoincrement=False,
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "auto_cleaning",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Auto-cleaning mode (ON/OFF), Enums: BfaDeviceAutoCleaning",
        ),
        sa.Column(
            "area_total",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Total occlusion contact area (mm^2)",
        ),
        sa.Column(
            "area_left",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Left occlusion contact area (mm^2)",
        ),
        sa.Column(
            "area_right",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Right occlusion contact area (mm^2)",
        ),
        sa.Column(
            "ave",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Average pressure - Total (MPa)",
        ),
        sa.Column(
            "ave_left",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Average pressure - Left side (MPa)",
        ),
        sa.Column(
            "ave_right",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Average pressure - Right side (MPa)",
        ),
        sa.Column(
            "max_total",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Maximum pressure - Total (MPa)",
        ),
        sa.Column(
            "max_left",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Maximum pressure - Left side (MPa)",
        ),
        sa.Column(
            "max_right",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Maximum pressure - Right side (MPa)",
        ),
        sa.Column(
            "force_total",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Total bite force (N)",
        ),
        sa.Column(
            "force_left",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Left bite force (N)",
        ),
        sa.Column(
            "force_right",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Right bite force (N)",
        ),
        sa.Column(
            "comment",
            sa.TEXT(),
            autoincrement=False,
            nullable=True,
            comment="Additional notes or comments",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "deleted_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
        sa.ForeignKeyConstraint(
            ["tenant_uuid"],
            ["tenant_clinics.tenant_uuid"],
            name="medical_device_bfa_tenant_uuid_fkey",
        ),
        sa.PrimaryKeyConstraint("id", name="medical_device_bfa_pkey"),
        sa.UniqueConstraint(
            "business_number",
            "external_patient_no",
            "device_data_id",
            name="_bfa_business_patient_device",
        ),
    )
    op.create_table(
        "import_file_logs",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "source",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="The source channel that sent the file, e.g., 'MIDDLEWARE_MEDICAL_APP'.",
        ),
        sa.Column(
            "json_file_path",
            sa.TEXT(),
            autoincrement=False,
            nullable=False,
            comment="The full S3 path to the ingested file. Unique if not null.",
        ),
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
        sa.Column(
            "status",
            sa.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="The status of the file reception. Enums: SourceFileStatus",
        ),
        sa.Column(
            "error_message",
            sa.TEXT(),
            autoincrement=False,
            nullable=True,
            comment="Stores the reason for a reception failure.",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "deleted_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.PrimaryKeyConstraint("id", name="import_file_logs_pkey"),
        sa.UniqueConstraint(
            "json_file_path", name="import_file_logs_json_file_path_key"
        ),
    )
    op.create_index(
        "ix_import_file_logs_source", "import_file_logs", ["source"], unique=False
    )
    op.create_table(
        "medical_device_sync_logs",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "business_number",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Business number of the imported data",
        ),
        sa.Column(
            "external_patient_no",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Patient number of the imported data",
        ),
        sa.Column(
            "device_data_id",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Device data ID of the imported data",
        ),
        sa.Column(
            "device_type",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Device type (BFA, EMG, MVT, BTE),Enums: MedicalDeviceType ",
        ),
        sa.Column(
            "examination_date",
            sa.DATE(),
            autoincrement=False,
            nullable=True,
            comment="Examination date of the imported data",
        ),
        sa.Column(
            "central_sync_status",
            sa.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="Synchronization status to Central DB (SUCCESS, FAILED), Enums: MedicalDeviceSyncStatus",
        ),
        sa.Column(
            "tenant_sync_status",
            sa.INTEGER(),
            autoincrement=False,
            nullable=False,
            comment="Synchronization status to Tenant DB (SUCCESS, FAILED), Enums: MedicalDeviceSyncStatus",
        ),
        sa.Column(
            "error_message",
            sa.TEXT(),
            autoincrement=False,
            nullable=True,
            comment="Detailed error message if any process fails",
        ),
        sa.Column(
            "raw_data_payload",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
            comment="Original JSON payload, used for debugging or reprocessing",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "deleted_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "json_file_path",
            sa.TEXT(),
            autoincrement=False,
            nullable=True,
            comment="The full S3 path to the ingested file. Unique if not null.",
        ),
        sa.PrimaryKeyConstraint("id", name="medical_device_sync_logs_pkey"),
    )
    op.create_table(
        "medical_device_mvt",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "business_number",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Identifier for the business establishment/clinic",
        ),
        sa.Column(
            "external_patient_no",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "device_data_id",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column(
            "tenant_uuid",
            sa.UUID(),
            autoincrement=False,
            nullable=False,
            comment="Foreign key to the TenantClinic table",
        ),
        sa.Column(
            "patient_user_id",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="ID of the patient in the internal system (if available)",
        ),
        sa.Column(
            "examination_date",
            sa.DATE(),
            autoincrement=False,
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "deleted_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
        sa.ForeignKeyConstraint(
            ["tenant_uuid"],
            ["tenant_clinics.tenant_uuid"],
            name="medical_device_mvt_tenant_uuid_fkey",
        ),
        sa.PrimaryKeyConstraint("id", name="medical_device_mvt_pkey"),
        sa.UniqueConstraint(
            "business_number",
            "external_patient_no",
            "device_data_id",
            name="_mvt_business_patient_device",
        ),
    )
    op.create_table(
        "medical_device_emg",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "business_number",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Identifier for the business establishment/clinic",
        ),
        sa.Column(
            "external_patient_no",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "device_data_id",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column(
            "tenant_uuid",
            sa.UUID(),
            autoincrement=False,
            nullable=False,
            comment="Foreign key to the TenantClinic table",
        ),
        sa.Column(
            "patient_user_id",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="ID of the patient in the internal system (if available)",
        ),
        sa.Column(
            "examination_date",
            sa.DATE(),
            autoincrement=False,
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "start_time",
            postgresql.TIME(),
            autoincrement=False,
            nullable=False,
            comment="Measurement start time",
        ),
        sa.Column(
            "end_time",
            postgresql.TIME(),
            autoincrement=False,
            nullable=False,
            comment="Measurement end time",
        ),
        sa.Column(
            "total_time",
            postgresql.TIME(),
            autoincrement=False,
            nullable=False,
            comment="Total analysis time (string format)",
        ),
        sa.Column(
            "max_peak",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Maximum clenching peak value (mV)",
        ),
        sa.Column(
            "base_peak",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Baseline section peak value (mV)",
        ),
        sa.Column(
            "total_clenching",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Total number of clenches",
        ),
        sa.Column(
            "clenching_per_hour",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Clenching frequency per hour",
        ),
        sa.Column(
            "burst_total",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Total number of bursts",
        ),
        sa.Column(
            "burst_total_dur",
            postgresql.TIME(),
            autoincrement=False,
            nullable=False,
            comment="Total duration of bursts (string format)",
        ),
        sa.Column(
            "burst_total_ave",
            postgresql.TIME(),
            autoincrement=False,
            nullable=False,
            comment="Average duration of bursts (string format)",
        ),
        sa.Column(
            "burst_per_hour",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Number of bursts per hour",
        ),
        sa.Column(
            "burst_total_duration_per_hour",
            postgresql.TIME(),
            autoincrement=False,
            nullable=False,
            comment="Total duration of bursts per hour (string format)",
        ),
        sa.Column(
            "clenching_strength_ave",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Average clenching intensity",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "deleted_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
        sa.ForeignKeyConstraint(
            ["tenant_uuid"],
            ["tenant_clinics.tenant_uuid"],
            name="medical_device_emg_tenant_uuid_fkey",
        ),
        sa.PrimaryKeyConstraint("id", name="medical_device_emg_pkey"),
        sa.UniqueConstraint(
            "business_number",
            "external_patient_no",
            "device_data_id",
            name="_emg_business_patient_device",
        ),
    )
    op.create_table(
        "medical_device_bte",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "business_number",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Identifier for the business establishment/clinic",
        ),
        sa.Column(
            "external_patient_no",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "device_data_id",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column(
            "tenant_uuid",
            sa.UUID(),
            autoincrement=False,
            nullable=False,
            comment="Foreign key to the TenantClinic table",
        ),
        sa.Column(
            "patient_user_id",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="ID of the patient in the internal system (if available)",
        ),
        sa.Column(
            "examination_date",
            sa.DATE(),
            autoincrement=False,
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "bite_array",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=False,
            comment="Data array containing detailed information of the bites",
        ),
        sa.Column(
            "comment",
            sa.TEXT(),
            autoincrement=False,
            nullable=True,
            comment="Additional notes or comments",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "deleted_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
        sa.ForeignKeyConstraint(
            ["tenant_uuid"],
            ["tenant_clinics.tenant_uuid"],
            name="medical_device_bte_tenant_uuid_fkey",
        ),
        sa.PrimaryKeyConstraint("id", name="medical_device_bte_pkey"),
        sa.UniqueConstraint(
            "business_number",
            "external_patient_no",
            "device_data_id",
            name="_bte_business_patient_device",
        ),
    )
    # ### end Alembic commands ###
