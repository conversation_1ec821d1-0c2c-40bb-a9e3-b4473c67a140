"""add_column_to_table_tenant_clinics

Revision ID: 370d1221aa1c
Revises: be6133c37371
Create Date: 2025-09-04 11:07:09.635722

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "370d1221aa1c"
down_revision: Union[str, None] = "be6133c37371"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "tenant_clinics", sa.Column("step_data_errors", sa.JSON(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tenant_clinics", "step_data_errors")
    # ### end Alembic commands ###
