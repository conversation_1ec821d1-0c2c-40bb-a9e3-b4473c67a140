"""Init System User Model

Revision ID: f931d2993d68
Revises: 370d1221aa1c
Create Date: 2025-09-04 15:58:59.149810

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "f931d2993d68"  # pragma: allowlist secret
down_revision: Union[str, None] = "370d1221aa1c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "system_users",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("username", sa.String(), nullable=False),
        sa.Column("password", sa.String(), nullable=False),
        sa.Column("is_active", sa.<PERSON>(), nullable=False),
        sa.Column("last_login", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("otp_lock_expires_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("username"),
    )
    op.create_table(
        "system_user_profiles",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("system_user_id", sa.Integer(), nullable=False),
        sa.Column("email", sa.String(), nullable=False),
        sa.Column("email_hash", sa.String(), nullable=False),
        sa.Column("first_name", sa.String(), nullable=False),
        sa.Column("last_name", sa.String(), nullable=False),
        sa.Column("first_name_kana", sa.String(), nullable=True),
        sa.Column("last_name_kana", sa.String(), nullable=True),
        sa.Column("date_of_birth", sa.String(), nullable=True, comment="Date of birth"),
        sa.Column("phone", sa.String(), nullable=True),
        sa.Column("phone_hash", sa.String(), nullable=True),
        sa.Column("country_code", sa.String(), nullable=True),
        sa.Column("postal_code", sa.String(), nullable=True),
        sa.Column("address_1", sa.String(), nullable=True),
        sa.Column("address_2", sa.String(), nullable=True),
        sa.Column("address_3", sa.String(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["system_user_id"],
            ["system_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("email"),
        sa.UniqueConstraint("email_hash"),
        sa.UniqueConstraint("phone"),
        sa.UniqueConstraint("phone_hash"),
    )
    op.add_column(
        "oauth_tokens", sa.Column("system_user_id", sa.Integer(), nullable=True)
    )
    op.create_foreign_key(
        "fk_oauth_tokens_system_user_id",
        "oauth_tokens",
        "system_users",
        ["system_user_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "fk_oauth_tokens_system_user_id", "oauth_tokens", type_="foreignkey"
    )
    op.drop_column("oauth_tokens", "system_user_id")
    op.drop_table("system_user_profiles")
    op.drop_table("system_users")
    # ### end Alembic commands ###
