"""update column type

Revision ID: 9712a5cab10e
Revises: bd1bdac03336
Create Date: 2025-09-18 17:25:51.567555

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "9712a5cab10e"  # pragma: allowlist secret
down_revision: Union[str, None] = "bd1bdac03336"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "process_file_logs",
        "file_type",
        existing_type=sa.VARCHAR(),
        type_=sa.Integer(),
        postgresql_using="file_type::integer",
        existing_comment="Enums: FileTypeProcessFileEnum",
        existing_nullable=False,
    )
    op.alter_column(
        "process_file_logs",
        "status",
        existing_type=sa.VARCHAR(),
        type_=sa.Integer(),
        postgresql_using="status::integer",
        existing_comment="Enums: StatusProcessFileEnum",
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "process_file_logs",
        "status",
        existing_type=sa.Integer(),
        type_=sa.VARCHAR(),
        postgresql_using="status::varchar",
        existing_comment="Enums: StatusProcessFileEnum",
        existing_nullable=False,
    )
    op.alter_column(
        "process_file_logs",
        "file_type",
        existing_type=sa.Integer(),
        type_=sa.VARCHAR(),
        postgresql_using="file_type::varchar",
        existing_comment="Enums: FileTypeProcessFileEnum",
        existing_nullable=False,
    )
    # ### end Alembic commands ###
