"""add login audit table

Revision ID: 67a1d5c81fe2
Revises: f7b07cde2f9c
Create Date: 2025-09-14 19:22:38.475134

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "67a1d5c81fe2"  # pragma: allowlist secret
down_revision: Union[str, None] = "f7b07cde2f9c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "login_audit",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("client_id", sa.String(length=48), nullable=True),
        sa.Column("global_user_id", sa.Integer(), nullable=True),
        sa.Column("clinic_patient_id", sa.Integer(), nullable=True),
        sa.Column("clinic_doctor_id", sa.Integer(), nullable=True),
        sa.Column("system_user_id", sa.Integer(), nullable=True),
        sa.Column("tenant_uuid", sa.TEXT(), nullable=True),
        sa.Column("ip_address", sa.String(length=50), nullable=True),
        sa.Column("user_agent", sa.TEXT(), nullable=True),
        sa.Column(
            "login_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("status", sa.Integer(), nullable=False),
        sa.Column("failure_reason", sa.TEXT(), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("login_audit")
    # ### end Alembic commands ###
