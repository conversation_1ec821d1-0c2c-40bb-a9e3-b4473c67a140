"""add unique global users and user mappings

Revision ID: 0196b8b6661c
Revises: 9712a5cab10e
Create Date: 2025-09-24 18:23:32.961368

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0196b8b6661c"
down_revision: Union[str, None] = "9712a5cab10e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_global_users_username"), table_name="global_users")
    op.create_index(
        op.f("ix_global_users_username"), "global_users", ["username"], unique=False
    )
    op.drop_index(op.f("ix_global_users_username_hash"), table_name="global_users")
    op.create_index(
        op.f("ix_global_users_username_hash"),
        "global_users",
        ["username_hash"],
        unique=True,
    )
    op.create_unique_constraint(
        "uq_user_mappings_patient_user_id_tenant_uuid",
        "tenant_user_mappings",
        ["patient_user_id", "tenant_uuid"],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "uq_user_mappings_patient_user_id_tenant_uuid",
        "tenant_user_mappings",
        type_="unique",
    )
    op.drop_index(op.f("ix_global_users_username_hash"), table_name="global_users")
    op.create_index(
        op.f("ix_global_users_username_hash"),
        "global_users",
        ["username_hash"],
        unique=False,
    )
    op.drop_index(op.f("ix_global_users_username"), table_name="global_users")
    op.create_index(
        op.f("ix_global_users_username"), "global_users", ["username"], unique=True
    )
    # ### end Alembic commands ###
