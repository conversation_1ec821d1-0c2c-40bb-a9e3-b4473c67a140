"""remove flow medical device

Revision ID: 59c2dd36dd78
Revises: c54e3224f77c
Create Date: 2025-09-12 15:49:03.147828

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "59c2dd36dd78"
down_revision: Union[str, None] = "c54e3224f77c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("medical_device_bfa")
    op.drop_table("medical_device_bte")
    op.drop_table("medical_device_mvt")
    op.drop_table("medical_device_emg")

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "medical_device_emg",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "external_patient_no",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "device_data_id",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column("patient_user_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "examination_date",
            sa.DATE(),
            autoincrement=False,
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "start_time",
            postgresql.TIME(),
            autoincrement=False,
            nullable=False,
            comment="Measurement start time",
        ),
        sa.Column(
            "end_time",
            postgresql.TIME(),
            autoincrement=False,
            nullable=False,
            comment="Measurement end time",
        ),
        sa.Column(
            "total_time",
            postgresql.TIME(),
            autoincrement=False,
            nullable=False,
            comment="Total analysis time (string format)",
        ),
        sa.Column(
            "max_peak",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Maximum clenching peak value (mV)",
        ),
        sa.Column(
            "base_peak",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Baseline section peak value (mV)",
        ),
        sa.Column(
            "total_clenching",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Total number of clenches",
        ),
        sa.Column(
            "clenching_per_hour",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Clenching frequency per hour",
        ),
        sa.Column(
            "burst_total",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Total number of bursts",
        ),
        sa.Column(
            "burst_total_dur",
            postgresql.TIME(),
            autoincrement=False,
            nullable=False,
            comment="Total duration of bursts (string format)",
        ),
        sa.Column(
            "burst_total_ave",
            postgresql.TIME(),
            autoincrement=False,
            nullable=False,
            comment="Average duration of bursts (string format)",
        ),
        sa.Column(
            "burst_per_hour",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Number of bursts per hour",
        ),
        sa.Column(
            "burst_total_duration_per_hour",
            postgresql.TIME(),
            autoincrement=False,
            nullable=False,
            comment="Total duration of bursts per hour (string format)",
        ),
        sa.Column(
            "clenching_strength_ave",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Average clenching intensity",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "deleted_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
            name="medical_device_emg_patient_user_id_fkey",
        ),
        sa.PrimaryKeyConstraint("id", name="medical_device_emg_pkey"),
        sa.UniqueConstraint(
            "external_patient_no", "device_data_id", name="_emg_business_patient_device"
        ),
    )
    op.create_table(
        "medical_device_mvt",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "external_patient_no",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "device_data_id",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column("patient_user_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "examination_date",
            sa.DATE(),
            autoincrement=False,
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "deleted_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
            name="medical_device_mvt_patient_user_id_fkey",
        ),
        sa.PrimaryKeyConstraint("id", name="medical_device_mvt_pkey"),
        sa.UniqueConstraint(
            "external_patient_no", "device_data_id", name="_mvt_business_patient_device"
        ),
    )
    op.create_table(
        "medical_device_bte",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "external_patient_no",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "device_data_id",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column("patient_user_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "examination_date",
            sa.DATE(),
            autoincrement=False,
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "bite_array",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=False,
            comment="Data array containing detailed information of the bites",
        ),
        sa.Column(
            "comment",
            sa.TEXT(),
            autoincrement=False,
            nullable=True,
            comment="Additional notes or comments",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "deleted_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
            name="medical_device_bte_patient_user_id_fkey",
        ),
        sa.PrimaryKeyConstraint("id", name="medical_device_bte_pkey"),
        sa.UniqueConstraint(
            "external_patient_no", "device_data_id", name="_bte_business_patient_device"
        ),
    )
    op.create_table(
        "medical_device_bfa",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "external_patient_no",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column(
            "device_data_id",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column("patient_user_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column(
            "examination_date",
            sa.DATE(),
            autoincrement=False,
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "auto_cleaning",
            sa.VARCHAR(),
            autoincrement=False,
            nullable=True,
            comment="Auto-cleaning mode (ON/OFF), Enums: BfaDeviceAutoCleaning",
        ),
        sa.Column(
            "area_total",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Total occlusion contact area (mm^2)",
        ),
        sa.Column(
            "area_left",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Left occlusion contact area (mm^2)",
        ),
        sa.Column(
            "area_right",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Right occlusion contact area (mm^2)",
        ),
        sa.Column(
            "ave",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Average pressure - Total (MPa)",
        ),
        sa.Column(
            "ave_left",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Average pressure - Left side (MPa)",
        ),
        sa.Column(
            "ave_right",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Average pressure - Right side (MPa)",
        ),
        sa.Column(
            "max_total",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Maximum pressure - Total (MPa)",
        ),
        sa.Column(
            "max_left",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Maximum pressure - Left side (MPa)",
        ),
        sa.Column(
            "max_right",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Maximum pressure - Right side (MPa)",
        ),
        sa.Column(
            "force_total",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Total bite force (N)",
        ),
        sa.Column(
            "force_left",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Left bite force (N)",
        ),
        sa.Column(
            "force_right",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
            comment="Right bite force (N)",
        ),
        sa.Column(
            "comment",
            sa.TEXT(),
            autoincrement=False,
            nullable=True,
            comment="Additional notes or comments",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "deleted_at",
            postgresql.TIMESTAMP(timezone=True),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            "image_file_paths",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
            comment="A JSON array of objects,  [{'original': '...', 'thumbnail': '...'}]",
        ),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
            name="medical_device_bfa_patient_user_id_fkey",
        ),
        sa.PrimaryKeyConstraint("id", name="medical_device_bfa_pkey"),
        sa.UniqueConstraint(
            "external_patient_no", "device_data_id", name="_bfa_business_patient_device"
        ),
    )
    # ### end Alembic commands ###
