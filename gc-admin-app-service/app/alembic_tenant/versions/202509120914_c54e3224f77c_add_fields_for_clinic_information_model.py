"""Add fields for Clinic Information Model

Revision ID: c54e3224f77c
Revises: d87be4858e08
Create Date: 2025-09-12 09:14:31.333673

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "c54e3224f77c"  # pragma: allowlist secret
down_revision: Union[str, None] = "d87be4858e08"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # rename table
    op.rename_table("tenant_configuration", "clinic_configuration")

    # rename columns
    op.alter_column(
        "clinic_configuration", "tenant_name", new_column_name="clinic_name"
    )
    op.alter_column("clinic_configuration", "tenant_slug", new_column_name="clinic_no")
    op.alter_column("clinic_informations", "clinic_uuid", new_column_name="tenant_uuid")
    op.alter_column("clinic_informations", "clinic_slug", new_column_name="clinic_no")

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # revert column renames
    op.alter_column(
        "clinic_configuration", "clinic_name", new_column_name="tenant_name"
    )
    op.alter_column("clinic_configuration", "clinic_no", new_column_name="tenant_slug")
    op.alter_column("clinic_informations", "clinic_no", new_column_name="clinic_slug")
    op.alter_column("clinic_informations", "tenant_uuid", new_column_name="clinic_uuid")

    # revert table rename
    op.rename_table("clinic_configuration", "tenant_configuration")

    # ### end Alembic commands ###
