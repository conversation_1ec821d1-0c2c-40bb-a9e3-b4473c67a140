"""remove columns login success/failed

Revision ID: 4f4037a12f93
Revises: 59c2dd36dd78
Create Date: 2025-09-14 19:23:56.433915

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "4f4037a12f93"  # pragma: allowlist secret
down_revision: Union[str, None] = "59c2dd36dd78"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("doctor_users", "login_failed")
    op.drop_column("doctor_users", "login_success")
    op.drop_column("patient_users", "login_failed")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "patient_users",
        sa.Column("login_failed", sa.INTEGER(), autoincrement=False, nullable=False),
    )
    op.add_column(
        "doctor_users",
        sa.Column(
            "login_success",
            sa.INTEGER(),
            server_default=sa.text("0"),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.add_column(
        "doctor_users",
        sa.Column("login_failed", sa.INTEGER(), autoincrement=False, nullable=False),
    )
    # ### end Alembic commands ###
