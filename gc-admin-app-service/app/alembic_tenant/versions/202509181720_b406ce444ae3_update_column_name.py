"""update column name

Revision ID: b406ce444ae3
Revises: 3c9eb3bff98e
Create Date: 2025-09-18 17:20:40.838831

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "b406ce444ae3"  # pragma: allowlist secret
down_revision: Union[str, None] = "3c9eb3bff98e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "patient_profiles", "full_name_kanna", new_column_name="full_name_kana"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "patient_profiles", "full_name_kana", new_column_name="full_name_kanna"
    )
    # ### end Alembic commands ###
