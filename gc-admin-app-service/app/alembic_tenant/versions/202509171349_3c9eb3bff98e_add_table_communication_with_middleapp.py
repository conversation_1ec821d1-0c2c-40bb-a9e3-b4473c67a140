"""add table communication with middleapp

Revision ID: 3c9eb3bff98e
Revises: 4f4037a12f93
Create Date: 2025-09-17 13:49:58.427983

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "3c9eb3bff98e"
down_revision: Union[str, None] = "4f4037a12f93"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "patient_medical_devices",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "external_patient_no",
            sa.String(),
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
        sa.Column(
            "device_type",
            sa.String(),
            nullable=False,
            comment="Enums: MedicalDeviceType, BFA, EMG,....",
        ),
        sa.Column(
            "device_data_id",
            sa.String(),
            nullable=False,
            comment="Unique data identifier from the device",
        ),
        sa.Column(
            "examination_date",
            sa.Date(),
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "raw_data",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="Data medical device",
        ),
        sa.Column(
            "image_file_name", sa.String(), nullable=True, comment="File image name"
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "patient_user_id",
            "device_type",
            "device_data_id",
            name="uq_patient_medical_device_per_device",
        ),
    )
    op.create_table(
        "patient_treatment_images",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "external_patient_no",
            sa.String(),
            nullable=False,
            comment="Patient number from the external system or device",
        ),
        sa.Column("patient_user_id", sa.Integer(), nullable=False),
        sa.Column(
            "examination_date",
            sa.Date(),
            nullable=False,
            comment="Date of the examination/measurement",
        ),
        sa.Column(
            "image_file_name", sa.String(), nullable=False, comment="File image name"
        ),
        sa.Column(
            "image_file_path",
            sa.String(),
            nullable=False,
            comment="Path File image original",
        ),
        sa.Column(
            "preview_image_file_path",
            sa.String(),
            nullable=True,
            comment="Path File image thumbnail",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "patient_user_id",
            "image_file_name",
            name="uq_patient_treatment_image_user_id_image_file_name",
        ),
    )
    op.add_column(
        "patient_profiles",
        sa.Column(
            "address",
            sa.String(),
            nullable=True,
            comment="Full Address from Middleware Application",
        ),
    )
    op.add_column(
        "patient_profiles",
        sa.Column(
            "full_name",
            sa.String(),
            nullable=True,
            comment="Full Name from Middleware Application",
        ),
    )
    op.add_column(
        "patient_profiles",
        sa.Column(
            "full_name_kanna",
            sa.String(),
            nullable=True,
            comment="Full Name in Kanna from Middleware Application",
        ),
    )
    op.alter_column(
        "patient_profiles",
        "last_name",
        existing_type=sa.VARCHAR(length=255),
        nullable=True,
    )
    op.alter_column(
        "patient_profiles",
        "first_name",
        existing_type=sa.VARCHAR(length=255),
        nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "patient_profiles",
        "first_name",
        existing_type=sa.VARCHAR(length=255),
        nullable=False,
    )
    op.alter_column(
        "patient_profiles",
        "last_name",
        existing_type=sa.VARCHAR(length=255),
        nullable=False,
    )
    op.drop_column("patient_profiles", "full_name_kanna")
    op.drop_column("patient_profiles", "full_name")
    op.drop_column("patient_profiles", "address")
    op.drop_table("patient_treatment_images")
    op.drop_table("patient_medical_devices")
    # ### end Alembic commands ###
