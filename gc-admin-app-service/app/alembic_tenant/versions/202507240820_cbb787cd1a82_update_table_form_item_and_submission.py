"""update table form item and submission

Revision ID: cbb787cd1a82
Revises: 4b0f9bb19c14
Create Date: 2025-07-24 08:20:31.404304

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "cbb787cd1a82"
down_revision: Union[str, None] = "4b0f9bb19c14"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "form_items",
        sa.Column(
            "form_item_group_uuid",
            sa.UUID(),
            nullable=True,
            comment="Optional reference to item group",
        ),
    )
    op.execute(
        "UPDATE form_items SET form_item_group_uuid = form_item_group_id WHERE form_item_group_id is not NULL;"
    )
    op.drop_constraint(
        "form_items_form_item_group_id_fkey", "form_items", type_="foreignkey"
    )
    op.create_foreign_key(
        "form_items_form_item_group_uuid_fkey",
        "form_items",
        "form_item_groups",
        ["form_item_group_uuid"],
        ["uuid"],
    )
    op.drop_column("form_items", "form_item_group_id")
    op.add_column(
        "form_submissions",
        sa.Column(
            "form_flow_data",
            sa.JSON(),
            nullable=False,
            comment="Submitted answers in JSON format and form item type",
        ),
    )
    op.add_column(
        "form_submissions",
        sa.Column(
            "is_active",
            sa.Boolean(),
            nullable=False,
            comment="Whether this question is active",
        ),
    )
    op.drop_column("form_submissions", "form_data")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    op.add_column(
        "form_submissions",
        sa.Column(
            "form_data",
            postgresql.JSON(astext_type=sa.Text()),
            autoincrement=False,
            nullable=False,
            comment="Submitted answers in JSON format and form item type",
        ),
    )
    op.drop_column("form_submissions", "is_active")
    op.drop_column("form_submissions", "form_flow_data")
    op.add_column(
        "form_items",
        sa.Column(
            "form_item_group_id",
            sa.UUID(),
            autoincrement=False,
            nullable=True,
            comment="Optional reference to item group",
        ),
    )
    op.execute("UPDATE form_items SET form_item_group_id = form_item_group_uuid;")
    op.drop_constraint(
        "form_items_form_item_group_uuid_fkey", "form_items", type_="foreignkey"
    )
    op.create_foreign_key(
        "form_items_form_item_group_id_fkey",
        "form_items",
        "form_item_groups",
        ["form_item_group_id"],
        ["uuid"],
    )
    op.drop_column("form_items", "form_item_group_uuid")
    # ### end Alembic commands ###
