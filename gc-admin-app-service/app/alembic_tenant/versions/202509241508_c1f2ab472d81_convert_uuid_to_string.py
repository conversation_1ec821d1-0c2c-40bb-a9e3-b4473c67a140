"""convert_uuid_to_string

Revision ID: c1f2ab472d81
Revises: 52ff7a14cbac
Create Date: 2025-09-24 15:08:57.094891

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "c1f2ab472d81"  # pragma: allowlist secret
down_revision: Union[str, None] = "52ff7a14cbac"  # pragma: allowlist secret
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "documents_sent",
        "document_uuid",
        existing_type=sa.UUID(),
        type_=sa.String(),
        comment="UUID represents a document.",
        existing_comment="Unique identifier for the document",
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "documents_sent",
        "document_uuid",
        existing_type=sa.String(),
        type_=sa.UUID(),
        comment="Unique identifier for the document",
        existing_comment="UUID represents a document.",
        existing_nullable=False,
    )
    # ### end Alembic commands ###
