"""create_document_sent_table

Revision ID: 52ff7a14cbac
Revises: b406ce444ae3
Create Date: 2025-09-22 13:31:42.447378

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "52ff7a14cbac"  # pragma: allowlist secret
down_revision: Union[str, None] = "b406ce444ae3"  # pragma: allowlist secret
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "documents_sent",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "document_uuid",
            sa.UUID(),
            nullable=False,
            comment="Unique identifier for the document",
        ),
        sa.Column(
            "patient_user_id",
            sa.Integer(),
            nullable=False,
            comment="ID of the patient who received the document",
        ),
        sa.Column(
            "document_url",
            sa.String(length=500),
            nullable=False,
            comment="URL where the document can be accessed",
        ),
        sa.Column(
            "preview_document_data",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="JSON data containing document preview information (thumbnail, metadata, etc.)",
        ),
        sa.Column(
            "read_status",
            sa.Integer(),
            server_default=sa.text("2"),
            nullable=False,
            comment="Read status",
        ),
        sa.Column(
            "read_at",
            sa.TIMESTAMP(timezone=True),
            nullable=True,
            comment="Timestamp when the document was read by the patient",
        ),
        sa.Column(
            "sent_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
            comment="Timestamp when the document was sent",
        ),
        sa.Column(
            "sent_by",
            sa.Integer(),
            nullable=False,
            comment="ID of the doctor/staff who sent the document",
        ),
        sa.ForeignKeyConstraint(
            ["patient_user_id"],
            ["patient_users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["sent_by"],
            ["doctor_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "document_uuid",
            "patient_user_id",
            name="uq_documents_sent_document_patient",
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("documents_sent")
    # ### end Alembic commands ###
