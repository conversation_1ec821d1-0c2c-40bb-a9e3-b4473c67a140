# Japanese (Japan) translations for PROJECT.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-08-29 15:00+0700\n"
"PO-Revision-Date: 2025-08-29 15:00+0700\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: ja_JP <<EMAIL>>\n"
"Language: ja_JP\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"Generated-By: Babel 2.17.0\n"

#: api/health_check.py:10
msgid "OK"
msgstr "わかりました"

#: core/messages.py:38
msgid "Unknown error!"
msgstr "不明なエラーが発生しました。"

#: core/messages.py:43
msgid "Twilio error!"
msgstr "Twilioエラーが発生しました。"

#: core/messages.py:48
msgid "Twilio send message error!"
msgstr "Twilioメッセージ送信エラーが発生しました。"

#: core/messages.py:53
msgid "S3 bucket error!"
msgstr "S3バケットへのアクセスでエラーが発生しました。"

#: core/messages.py:59
msgid "X-Tenant-Clinic-No header is required!"
msgstr "X-Tenant-Clinic-No ヘッダーは必須です。"

#: core/messages.py:64 core/messages.py:156
msgid "Tenant not found!"
msgstr "テナントが見つかりません。"

#: core/messages.py:70
msgid "Clinic information not found!"
msgstr "クリニック情報が見つかりません。"

#: core/messages.py:75
msgid "Clinic created successfully!"
msgstr "クリニックが正常に作成されました。"

#: core/messages.py:80
msgid "Clinic creation failed!"
msgstr "クリニックの作成に失敗しました。"

#: core/messages.py:85
msgid "Clinic not found!"
msgstr "クリニックが見つかりません。"

#: core/messages.py:92
msgid "Invalid date provided!"
msgstr "無効な日付が指定されました。"

#: core/messages.py:97
msgid "Invalid end date!"
msgstr "無効な終了日が指定されました。"

#: core/messages.py:104
msgid "File upload failed!"
msgstr "ファイルのアップロードに失敗しました。"

#: core/messages.py:109
msgid "Invalid file format or content file error!"
msgstr "無効なファイル形式または内容エラーです。"

#: core/messages.py:114
msgid "Unsupported source!"
msgstr "サポートされていないソースです。"

#: core/messages.py:119
msgid "Invalid device type!"
msgstr "無効なデバイスタイプです。"

#: core/messages.py:124
msgid "Missing data ID!"
msgstr "データIDが不足しています。"

#: core/messages.py:129
msgid "File uploaded successfully!"
msgstr "ファイルが正常にアップロードされました。"

#: core/messages.py:134
msgid "Invalid image file type!"
msgstr "無効な画像ファイル形式です。"

#: core/messages.py:139 core/messages.py:166
msgid "Missing business number!"
msgstr "事業者番号が不足しています。"

#: core/messages.py:146
msgid "File already processed!"
msgstr "このファイルはすでに処理されています。"

#: core/messages.py:151
msgid "Device configuration not found!"
msgstr "デバイス設定が見つかりません。"

#: core/messages.py:161
msgid "Tenant DB URI is missing!"
msgstr "テナントDBの接続URIが見つかりません。"

#: core/messages.py:171
msgid "Sync medical file successfully"
msgstr "医療ファイルの同期が正常に完了しました。"

#: core/messages.py:176
msgid "Sync medical file failed!"
msgstr "医療ファイルの同期に失敗しました。"

#: core/messages.py:181
msgid "Patient not found!"
msgstr "患者が見つかりません。"

#: core/messages.py:188
msgid "Thumbnail creation accepted"
msgstr "サムネイル作成が正常に承認されました。"

#: core/messages.py:193
msgid "Thumbnail creation failed"
msgstr "サムネイル作成に失敗しました。"
