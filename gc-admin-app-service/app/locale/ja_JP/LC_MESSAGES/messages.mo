��                        �     �          2     P     b     �     �     �     �     �     �  *        3     L     e     v     y     �     �     �     �     �            
   8     F     a     p  !   �  �  �  3   5  0   i  0   �  *   �  -   �  9   $  9   ^  <   �  *   �  '      -   (  <   V  *   �  *   �  &   �     	  !   #	  D   E	  3   �	  <   �	  5   �	  '   1
  9   Y
  -   �
  '   �
  <   �
  *   &  0   Q  ,   �   Clinic created successfully! Clinic creation failed! Clinic information not found! Clinic not found! Device configuration not found! File already processed! File upload failed! File uploaded successfully! Invalid date provided! Invalid device type! Invalid end date! Invalid file format or content file error! Invalid image file type! Missing business number! Missing data ID! OK Patient not found! S3 bucket error! Sync medical file failed! Sync medical file successfully Tenant DB URI is missing! Tenant not found! Thumbnail creation accepted Thumbnail creation failed Twilio error! Twilio send message error! Unknown error! Unsupported source! X-Tenant-Slug header is required! Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-29 15:00+0700
PO-Revision-Date: 2025-08-29 15:00+0700
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: ja_JP
Language-Team: ja_JP <<EMAIL>>
Plural-Forms: nplurals=1; plural=0;
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 クリニックが正常に作成されました。 クリニックの作成に失敗しました。 クリニック情報が見つかりません。 クリニックが見つかりません。 デバイス設定が見つかりません。 このファイルはすでに処理されています。 ファイルのアップロードに失敗しました。 ファイルが正常にアップロードされました。 無効な日付が指定されました。 無効なデバイスタイプです。 無効な終了日が指定されました。 無効なファイル形式または内容エラーです。 無効な画像ファイル形式です。 事業者番号が不足しています。 データIDが不足しています。 わかりました 患者が見つかりません。 S3バケットへのアクセスでエラーが発生しました。 医療ファイルの同期に失敗しました。 医療ファイルの同期が正常に完了しました。 テナントDBの接続URIが見つかりません。 テナントが見つかりません。 サムネイル作成が正常に承認されました。 サムネイル作成に失敗しました。 Twilioエラーが発生しました。 Twilioメッセージ送信エラーが発生しました。 不明なエラーが発生しました。 サポートされていないソースです。 X-Tenant-Slug ヘッダーは必須です。 