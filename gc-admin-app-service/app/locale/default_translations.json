{"Unknown error!": "不明なエラーが発生しました。", "Twilio error!": "Twilioエラーが発生しました。", "Twilio send message error!": "Twilioメッセージ送信エラーが発生しました。", "S3 bucket error!": "S3バケットへのアクセスでエラーが発生しました。", "X-Tenant-Clinic-No header is required!": "X-Tenant-Clinic-No ヘッダーは必須です。", "Tenant not found!": "テナントが見つかりません。", "Clinic information not found!": "クリニック情報が見つかりません。", "Clinic created successfully!": "クリニックが正常に作成されました。", "Clinic creation failed!": "クリニックの作成に失敗しました。", "Clinic not found!": "クリニックが見つかりません。", "Invalid date provided!": "無効な日付が指定されました。", "Invalid end date!": "無効な終了日が指定されました。", "File upload failed!": "ファイルのアップロードに失敗しました。", "Invalid file format or content file error!": "無効なファイル形式または内容エラーです。", "Unsupported source!": "サポートされていないソースです。", "Invalid device type!": "無効なデバイスタイプです。", "Missing data ID!": "データIDが不足しています。", "File uploaded successfully!": "ファイルが正常にアップロードされました。", "Invalid image file type!": "無効な画像ファイル形式です。", "Missing business number!": "事業者番号が不足しています。", "File already processed!": "このファイルはすでに処理されています。", "Device configuration not found!": "デバイス設定が見つかりません。", "Tenant DB URI is missing!": "テナントDBの接続URIが見つかりません。", "Sync medical file successfully": "医療ファイルの同期が正常に完了しました。", "Sync medical file failed!": "医療ファイルの同期に失敗しました。", "Patient not found!": "患者が見つかりません。", "Thumbnail creation accepted": "サムネイル作成が正常に承認されました。", "Thumbnail creation failed": "サムネイル作成に失敗しました。", "OK": "わかりました"}