# English (United States) translations for PROJECT.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-08-29 15:00+0700\n"
"PO-Revision-Date: 2025-08-29 15:00+0700\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: en_US\n"
"Language-Team: en_US <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: api/health_check.py:10
msgid "OK"
msgstr ""

#: core/messages.py:38
msgid "Unknown error!"
msgstr ""

#: core/messages.py:43
msgid "Twilio error!"
msgstr ""

#: core/messages.py:48
msgid "Twilio send message error!"
msgstr ""

#: core/messages.py:53
msgid "S3 bucket error!"
msgstr ""

#: core/messages.py:59
msgid "X-Tenant-Clinic-No header is required!"
msgstr ""

#: core/messages.py:64 core/messages.py:156
msgid "Tenant not found!"
msgstr ""

#: core/messages.py:70
msgid "Clinic information not found!"
msgstr ""

#: core/messages.py:75
msgid "Clinic created successfully!"
msgstr ""

#: core/messages.py:80
msgid "Clinic creation failed!"
msgstr ""

#: core/messages.py:85
msgid "Clinic not found!"
msgstr ""

#: core/messages.py:92
msgid "Invalid date provided!"
msgstr ""

#: core/messages.py:97
msgid "Invalid end date!"
msgstr ""

#: core/messages.py:104
msgid "File upload failed!"
msgstr ""

#: core/messages.py:109
msgid "Invalid file format or content file error!"
msgstr ""

#: core/messages.py:114
msgid "Unsupported source!"
msgstr ""

#: core/messages.py:119
msgid "Invalid device type!"
msgstr ""

#: core/messages.py:124
msgid "Missing data ID!"
msgstr ""

#: core/messages.py:129
msgid "File uploaded successfully!"
msgstr ""

#: core/messages.py:134
msgid "Invalid image file type!"
msgstr ""

#: core/messages.py:139 core/messages.py:166
msgid "Missing business number!"
msgstr ""

#: core/messages.py:146
msgid "File already processed!"
msgstr ""

#: core/messages.py:151
msgid "Device configuration not found!"
msgstr ""

#: core/messages.py:161
msgid "Tenant DB URI is missing!"
msgstr ""

#: core/messages.py:171
msgid "Sync medical file successfully"
msgstr ""

#: core/messages.py:176
msgid "Sync medical file failed!"
msgstr ""

#: core/messages.py:181
msgid "Patient not found!"
msgstr ""

#: core/messages.py:188
msgid "Thumbnail creation accepted"
msgstr ""

#: core/messages.py:193
msgid "Thumbnail creation failed"
msgstr ""

