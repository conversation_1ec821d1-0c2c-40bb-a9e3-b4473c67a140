from typing import Optional

from core.messages import CustomMessageCode
from pydantic import BaseModel, Field, field_validator

from gc_dentist_shared.core.common.utils import UUIDString
from gc_dentist_shared.core.enums.pricing_enum import TenantPricingStorageKey


class CreateExtraPricingStorageRequest(BaseModel):
    storage_key_id: int = Field(..., description="Pricing Storage Key ID", gt=0)
    tenant_uuid: UUIDString = Field(..., description="Tenant UUID")

    @field_validator("storage_key_id")
    @classmethod
    def validate_storage_key_id(cls, v: int) -> int:
        if v not in TenantPricingStorageKey.get_member_values():
            raise ValueError(CustomMessageCode.PRICING_STORAGE_KEY_ID_IS_INVALID.title)
        return v


class UpdatePlanStorageRequest(BaseModel):
    tenant_uuid: UUIDString = Field(..., description="Tenant UUID")


class UpdateTenantConfigurationRequest(BaseModel):
    default_storage: Optional[int] = None
    extra_storage: Optional[int] = None
    plan_key_id: Optional[int] = None
