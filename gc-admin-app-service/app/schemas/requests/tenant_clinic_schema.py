from typing import Optional

from core.messages import CustomMessageCode
from pydantic import BaseModel, EmailStr, Field, model_validator


class UpdateClinicInfoSchema(BaseModel):
    phone_number: Optional[str] = None
    email: Optional[EmailStr] = None
    address_1: Optional[str] = None
    address_2: Optional[str] = None
    address_3: Optional[str] = None
    latitude: Optional[str] = Field(None, lt=90, gt=-90)
    longitude: Optional[str] = Field(None, lt=180, gt=-180)
    logo_url: Optional[str] = None
    opening_hours: Optional[dict] = None

    @model_validator(mode="after")
    def check_at_least_one_field(self):
        data_validate = self.model_dump(exclude_none=True, exclude_unset=True)

        if not data_validate:
            raise ValueError(
                CustomMessageCode.UPDATE_TENANT_CLINIC_INVALID_REQUEST.title
            )
        return self
