from datetime import datetime

from pydantic import BaseModel, ConfigDict

from gc_dentist_shared.core.common.timezone import Timestamp


class TenantExtraStorageResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    storage_key_id: int
    pricing: float | None = None
    storage: int | None = None
    expired_at: datetime | None = None
    created_at: Timestamp
    updated_at: Timestamp | None = None
