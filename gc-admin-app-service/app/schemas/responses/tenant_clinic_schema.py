from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field


class CreatePlanStorageResponse(BaseModel):
    plan_key_id: int = Field(..., description="plan_key_id of the m_plans")
    tenant_plan_id: int = Field(..., description="tenant_plan_id of the tenant_plans")
    extra_storage_ids: list[int] = Field(..., description="storage_id of the storage")
    total_extra_storage: int = Field(..., description="Sum storage of the tenant")
    default_storage: int = Field(..., description="Default storage size of the plan")


class TenantInfoResponseSchema(BaseModel):
    tenant_uuid: str | UUID
    clinic_name: Optional[str] = None
    clinic_no: Optional[str] = None
    status: Optional[int] = None
    phone_number: Optional[str] = None
    email: Optional[str] = None
    address_1: Optional[str] = None
    address_2: Optional[str] = None
    address_3: Optional[str] = None
    latitude: Optional[str] = None
    longitude: Optional[str] = None
    logo_url: Optional[str] = None
    opening_hours: Optional[dict] = None
