from typing import Optional

from configuration.settings import configuration
from pydantic import BaseModel

from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation


class SystemUserProfileSchema(BaseModel):
    id: int
    first_name: str
    last_name: str

    phone: Optional[str | None] = None
    country_code: Optional[str | None] = None
    email: Optional[str | None] = None

    def model_post_init(self, __context):
        self._decrypt_fields(["date_of_birth", "phone", "email"])

    def _decrypt_fields(self, fields: list[str]):
        aes = AesGCMRotation(configuration)
        for field in fields:
            value = getattr(self, field, None)
            if value:
                setattr(self, field, aes.decrypt_data(value))
