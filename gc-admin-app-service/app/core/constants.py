from enum import IntEnum, StrEnum


class TenantClinicStatus(IntEnum):
    INPROCESS = 20
    SUCCESS = 40
    FAILED = 90
    FAILED_CREATED_CLINIC_SETTING = 91
    CANCELED = 99


class ClinicRedisKey(StrEnum):
    CLINICS_CACHE = "noda_data:all_clinics_cache"


class GenderEnum(IntEnum):
    MALE = 1
    FEMALE = 2
    OTHER = 0


MAPPING_LANG_REGION = {
    "ja": "ja-JP",
    "en": "en-US",
}

X_TENANT_CLINIC_NO = "X-Tenant-Clinic-No"
X_TENANT_UUID = "X-Tenant-UUID"


TWILIO_SMS_CHANNEL = "sms"
TWILIO_STATUS_APPROVED = "approved"


ENDPOINT_ACCOUNT_API = "http://localhost:8002"


PARAGRAPH_WITH_BREAK_OR_END_REGEX = r"<p[^>]*>(.*?)(<br>|</p>)"
REMOVE_HTML_TAGS_REGEX = r"<[^>]+>"

ALLOWED_IMAGE_TYPES = {
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/heic",
    "image/heif",
    "image/webp",
    "image/avif",
    "image/svg+xml",
}

S3_SYNC_CONCURRENCY_LIMIT = 20
