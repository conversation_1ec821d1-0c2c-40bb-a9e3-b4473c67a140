from enum import Enum

from gc_dentist_shared.core.common.i18n import i18n as _

ERROR_PROCESS = "Request could not be processed, please try again."
INIT_CLINIC_INPROCESS = "Init clinic in process."


class CustomMessageCode(Enum):
    """Custom message codes for application-specific errors and messages."""

    def __new__(cls, code, title, description):
        obj = object.__new__(cls)
        obj._value_ = code
        obj._title = title
        obj._description = description
        return obj

    @property
    def code(self):
        return self.value

    @property
    def title(self):
        # Using the i18n instance to translate the title
        return _(self._title)

    @property
    def description(self):
        return self._description

    def __str__(self):
        return f"{self.code} - {self.title}: {self.description}"

    # Server Errors
    UNKNOWN_ERROR = (
        5000,
        _("Unknown error!", "不明なエラーが発生しました。"),
        "An unexpected error occurred.",
    )
    TWILIO_ERROR = (
        5001,
        _("Twilio error!", "Twilioエラーが発生しました。"),
        "An error occurred while processing the Twilio request.",
    )
    TWILIO_SEND_MESSAGE_ERROR = (
        5002,
        _("Twilio send message error!", "Twilioメッセージ送信エラーが発生しました。"),
        "An error occurred while sending a message via Twilio.",
    )
    S3_BUCKET_ERROR = (
        5003,
        _("S3 bucket error!", "S3バケットへのアクセスでエラーが発生しました。"),
        "An error occurred while accessing the S3 bucket.",
    )
    # Tenant Clinic
    X_TENANT_CLINIC_NO_IS_REQUIRED = (
        4000,
        _(
            "X-Tenant-Clinic-No header is required!",
            "X-Tenant-Clinic-No ヘッダーは必須です。",
        ),
        "The X-Tenant-Clinic-No header must be provided in the request.",
    )
    TENANT_NOT_FOUND = (
        4004,
        _("Tenant not found!", "テナントが見つかりません。"),
        "The specified tenant does not exist.",
    )

    CLINIC_INFO_NOT_FOUND = (
        4005,
        _("Clinic information not found!", "クリニック情報が見つかりません。"),
        "The specified clinic information does not exist.",
    )
    CLINIC_CREATED_SUCCESS = (
        4006,
        _("Clinic created successfully!", "クリニックが正常に作成されました。"),
        "The clinic has been created successfully.",
    )
    CLINIC_CREATED_FAILED = (
        4007,
        _("Clinic creation failed!", "クリニックの作成に失敗しました。"),
        "An error occurred while creating the clinic.",
    )
    CLINIC_NOT_FOUND = (
        4008,
        _("Clinic not found!", "クリニックが見つかりません。"),
        "The specified clinic does not exist.",
    )
    TENANT_NOT_STATUS_RETRY = (
        4009,
        "Tenant not in a retryable status!",
        "The tenant is not in a status that allows retrying the operation.",
    )
    ERROR_RETRY_STEP_TENANT = (
        4010,
        "Error retry step tenant!",
        "An error occurred while retrying the tenant creation step.",
    )
    SYSTEM_USER_NOT_FOUND = (
        4011,
        "System user not found!",
        "The specified user does not exist.",
    )
    SEARCH_TENANT_CLINIC_FAILED = (
        4012,
        "Search tenant clinic failed!",
        "An error occurred while searching for the tenant clinic.",
    )
    UPDATE_TENANT_CLINIC_SUCCESS = (
        4013,
        "Update tenant clinic successfully!",
        "The tenant clinic has been updated successfully.",
    )
    UPDATE_TENANT_CLINIC_FAILED = (
        4014,
        "Update tenant clinic failed!",
        "An error occurred while updating the tenant clinic.",
    )
    UPDATE_TENANT_CLINIC_INVALID_REQUEST = (
        4015,
        "Invalid request data!",
        "The provided request data is invalid or malformed.",
    )

    # Validation Errors
    VALUE_ERROR_INVALID_DATE_PAST = (
        10000,
        _("Invalid date provided!", "無効な日付が指定されました。"),
        "The date provided must be in the past.",
    )
    VALUE_ERROR_INVALID_END_DATE = (
        10001,
        _("Invalid end date!", "無効な終了日が指定されました。"),
        "The end date must be after the start date.",
    )

    # Master Data
    MASTER_DATA_NOT_FOUND = (
        30001,
        "Master data not found!",
        "The requested master data does not exist.",
    )
    MASTER_DATA_FETCH_SUCCESS = (
        30002,
        "Master data fetched successfully!",
        "The master data has been retrieved successfully.",
    )
    MASTER_DATA_FETCH_FAILED = (
        30003,
        "Master data fetch failed!",
        "An error occurred while fetching the master data.",
    )
    MASTER_DATA_COLUMN_NOT_EXIST = (
        30004,
        "Master data column does not exist!",
        "The specified column does not exist in the master data model.",
    )
    MASTER_DATA_INVALID_FILTER = (
        30005,
        "Invalid filter condition!",
        "The provided filter condition is invalid or malformed.",
    )

    # Source file
    IMPORT_FILE_UPLOAD_FAILED = (
        6000,
        _("File upload failed!", "ファイルのアップロードに失敗しました。"),
        "The file could not be processed.",
    )
    IMPORT_FILE_INVALID_FORMAT = (
        6001,
        _(
            "Invalid file format or content file error!",
            "無効なファイル形式または内容エラーです。",
        ),
        "The file content is not valid JSON or does not match the required data structure.",
    )
    IMPORT_FILE_UNSUPPORTED_SOURCE = (
        6002,
        _("Unsupported source!", "サポートされていないソースです。"),
        "The specified source is not supported by this endpoint.",
    )
    IMPORT_FILE_INVALID_DEVICE_TYPE = (
        6003,
        _("Invalid device type!", "無効なデバイスタイプです。"),
        "The 'type' field in 'fi_data' is missing or unsupported.",
    )
    IMPORT_FILE_MISSING_DATA_ID = (
        6004,
        _("Missing data ID!", "データIDが不足しています。"),
        "The 'id' field in 'fi_data' is required.",
    )
    IMPORT_FILE_UPLOAD_SUCCESS = (
        6005,
        _("File uploaded successfully!", "ファイルが正常にアップロードされました。"),
        "File upload successfully",
    )
    IMPORT_FILE_INVALID_IMAGE_TYPE = (
        6006,
        _("Invalid image file type!", "無効な画像ファイル形式です。"),
        "The uploaded image file has an unsupported format.",
    )

    # Process file
    PROCESS_FILE_ALREADY_PROCESSED = (
        7001,
        _("File already processed!", "このファイルはすでに処理されています。"),
        "This file has already been submitted and is being processed or has been completed.",
    )
    PROCESS_FILE_NO_CONFIG_FOUND = (
        7002,
        _("Device configuration not found!", "デバイス設定が見つかりません。"),
        "No processing configuration was found for the specified device type.",
    )
    PROCESS_FILE_TENANT_NOT_FOUND = (
        7003,
        _("Tenant not found!", "テナントが見つかりません。"),
        "The tenant specified by the business number could not be found.",
    )
    PROCESS_FILE_TENANT_DB_URI_MISSING = (
        7004,
        _("Tenant DB URI is missing!", "テナントDBの接続URIが見つかりません。"),
        "The database connection URI is not configured for this tenant.",
    )

    PROCESS_FILE_SUCCESS = (
        7006,
        _("Sync medical file successfully", "医療ファイルの同期が正常に完了しました。"),
        "Sync medical file successfully",
    )
    PROCESS_FILE_FAILED = (
        7007,
        _("Sync medical file failed!", "医療ファイルの同期に失敗しました。"),
        "Sync medical file failed!",
    )
    PROCESS_FILE_PATIENT_USER_NOT_FOUND = (
        7008,
        _("Patient not found!", "患者が見つかりません。"),
        "The specified patient does not exist.",
    )

    # Storage
    SYNC_STORAGE_USAGE_SUCCESS = (
        9000,
        "Sync storage usage successfully",
        "Sync storage usage successfully",
    )
    SYNC_STORAGE_USAGE_FAILED = (
        9001,
        "Sync storage usage failed",
        "Sync storage usage failed",
    )

    # Pricing
    PRICING_STORAGE_NOT_FOUND = (
        11001,
        "Pricing storage not found!",
        "The specified pricing storage does not exist.",
    )
    PRICING_STORAGE_TENANT_NOT_FOUND = (
        11002,
        "Tenant not found!",
        "The specified tenant does not exist.",
    )
    PRICING_STORAGE_CREATED_SUCCESS = (
        11003,
        "Pricing storage created successfully!",
        "The pricing storage has been created successfully.",
    )
    PRICING_STORAGE_CREATED_FAILED = (
        11004,
        "Pricing storage creation failed!",
        "An error occurred while creating the pricing storage.",
    )
    PRICING_STORAGE_GET_LIST_FAILED = (
        11005,
        "Get list pricing storage failed!",
        "An error occurred while get list the pricing storage.",
    )
    PRICING_STORAGE_TENANT_CONFIGRATION_NOT_FOUND = (
        11006,
        "Tenant configuration not found!",
        "The tenant configuration does not exist.",
    )
    PRICING_STORAGE_PERIOD_NOT_FOUND = (
        11007,
        "Pricing storage period not found!",
        "The specified pricing storage period does not exist.",
    )
    PRICING_PLAN_NOT_FOUND = (
        11008,
        "Plan not found!",
        "The specified plan does not exist.",
    )
    PRICING_TENANT_PLAN_NOT_FOUND = (
        11009,
        "Tenant plan not found!",
        "The specified tenant plan does not exist.",
    )
    PRICING_PLAN_NOT_CHANGED = (
        11010,
        "Plan not changed!",
        "The specified plan is the same as the current plan.",
    )
    PRICING_PLAN_STORAGE_UPDATED_SUCCESS = (
        11011,
        "Plan storage updated successfully!",
        "The plan storage has been updated successfully.",
    )
    PRICING_PLAN_STORAGE_UPDATED_FAILED = (
        11012,
        "Plan storage update failed!",
        "An error occurred while updating the plan storage.",
    )
    PRICING_STORAGE_KEY_ID_IS_INVALID = (
        11013,
        "Pricing storage key id is invalid!",
        "The specified pricing storage key id is invalid.",
    )
    PRICING_PLAN_KEY_ID_IS_INVALID = (
        11014,
        "Plan key id is invalid!",
        "The specified plan key id is invalid.",
    )
