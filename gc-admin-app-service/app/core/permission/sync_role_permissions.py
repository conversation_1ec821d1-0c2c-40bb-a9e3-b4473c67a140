from sqlalchemy import select, update

from gc_dentist_shared.core.enums.role_enum import ROLE_KEY_MAPPING
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.core.permissions.load_file_yaml import (
    load_tenant_role_permissions_yaml,
)
from gc_dentist_shared.tenant_models import Permission, RolePermission, Roles


async def sync_roles_permissions(tenant_db_session):
    """Sync roles for multiple tenants"""
    # Get the directory where this script is located
    try:
        role_permissions_yaml = load_tenant_role_permissions_yaml()

        # Load existing roles system
        existing_roles = await fetch_existing_roles_system(tenant_db_session)

        # Load existing permission
        existing_permissions = await fetch_existing_permission(tenant_db_session)

        # Load role permission map and Mark all role and permission as deleted
        existing_links = await fetch_and_soft_delete_all_role_permissions(
            tenant_db_session
        )

        role_perm_objs_add = []
        for role_name, patterns in role_permissions_yaml.items():
            if not patterns:
                log.warning(f"[❗WARN] Pattern not found: {role_name}")
                continue

            role_key_id = ROLE_KEY_MAPPING.get(role_name)
            if not role_key_id or role_key_id not in existing_roles:
                log.warning(f"[❗WARN] Role not found: {role_name}")
                continue

            matched_keys = set()

            for pattern in patterns:
                matched_keys.update(
                    match_permission_keys(pattern, existing_permissions)
                )

            for key in matched_keys:
                permission_id = existing_permissions.get(key)
                if not permission_id:
                    log.warning(f"[❗WARN] Permission not found for key: {key}")
                    continue

                pair_key = (role_key_id, permission_id)
                if pair_key in existing_links:
                    await restore_role_permission(
                        tenant_db_session, existing_links[pair_key]
                    )
                else:
                    role_perm_objs_add.append(
                        RolePermission(
                            role_key_id=role_key_id,
                            permission_id=permission_id,
                            delete_flag=False,
                        )
                    )

        tenant_db_session.add_all(role_perm_objs_add)
        await tenant_db_session.flush()

    except Exception as e:
        log.error(f"❌ Failed to sync Role permission: {str(e)}")
        raise e


async def fetch_and_soft_delete_all_role_permissions(tenant_db) -> dict:
    stmt = (
        update(RolePermission)
        .where(RolePermission.id.isnot(None))
        .values(delete_flag=True)
        .returning(
            RolePermission.role_key_id,
            RolePermission.permission_id,
            RolePermission.id,
        )
    )

    result = await tenant_db.execute(stmt)
    rows = result.mappings().all()

    return {(r["role_key_id"], r["permission_id"]): r["id"] for r in rows}


async def fetch_existing_roles_system(tenant_db) -> list[int]:
    result = await tenant_db.execute(
        select(Roles.role_key_id).where(
            Roles.is_system.is_(True), Roles.delete_flag.is_(False)
        )
    )
    return [r["role_key_id"] for r in result.mappings().all()]


async def fetch_existing_permission(tenant_db) -> dict:
    execute_permission = await tenant_db.execute(
        select(Permission.id, Permission.permission_key).where(
            Permission.delete_flag.is_(False)
        )
    )
    permissions = execute_permission.mappings().all()
    return {p["permission_key"]: p["id"] for p in permissions}


async def restore_role_permission(tenant_db, role_permission_id: int):
    await tenant_db.execute(
        update(RolePermission)
        .where(RolePermission.id == role_permission_id)
        .values(delete_flag=False)
    )


def match_permission_keys(pattern, all_keys):
    if pattern == "*":
        return list(all_keys.keys())
    elif pattern.endswith(":*"):
        prefix = pattern[:-2]
        return [k for k in all_keys if k.startswith(f"{prefix}")]
    elif ":" in pattern:  # noqa
        module, action = pattern.split(":", 1)
        return [
            k
            for k in all_keys
            if k.endswith(f":{action}") and k.startswith(f"{module}.") or k == pattern
        ]
    return [pattern]
