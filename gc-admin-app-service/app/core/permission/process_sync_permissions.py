from core.permission.sync_permissions import check_exists_tenant_plan, sync_permissions
from core.permission.sync_role_permissions import sync_roles_permissions
from core.permission.sync_roles import sync_roles
from db.db_connection import CentralDatabase, TenantDatabase
from services.utils import get_tenant_uuid_by_db_name

from gc_dentist_shared.core.logger.config import log


async def process_sync_tenant(
    tenant_db_names: list[str],
    plan_key_id: int,
):
    if not tenant_db_names:
        log.warning("No tenant databases specified, skipping all operations")
        return

    log.info(f"📋 Using plan_key_id: {plan_key_id}")
    log.info(f"🔄 Processing tenants: {', '.join(tenant_db_names)}")

    all_success = True
    async with CentralDatabase.get_instance_db() as central_db_session:
        for tenant_db_name in tenant_db_names:
            tenant_uuid = await get_tenant_uuid_by_db_name(
                central_db_session=central_db_session,
                db_name=tenant_db_name,
            )

            if not await check_exists_tenant_plan(
                central_db_session, tenant_uuid, plan_key_id
            ):
                msg_error = (
                    f"❌ Tenant '{tenant_db_name}' (uuid={tenant_uuid}) "
                    f"does not have a plan with plan_key_id={plan_key_id}"
                )
                log.error(msg_error)
                all_success = False
                continue
            async with TenantDatabase.get_instance_tenant_db_by_name(
                tenant_db_name
            ) as tenant_db_session:
                async with tenant_db_session.begin():
                    try:
                        async with tenant_db_session.begin_nested():
                            # Step 1: Sync roles
                            log.info(f"🔄 Syncing roles for tenant: {tenant_db_name}")
                            await sync_roles(tenant_db_session)

                            # Step 2: Sync roles and permissions
                            log.info(
                                f"🔄 Syncing permission for tenant: {tenant_db_name}"
                            )
                            await sync_permissions(tenant_db_session, plan_key_id)
                            # Step 3: Sync plan permissions
                            log.info(
                                f"🔄 Syncing role and permission for tenant: {tenant_db_name}"
                            )
                            await sync_roles_permissions(tenant_db_session)
                        log.info(f"✅ Tenant {tenant_db_name} processed successfully")
                    except Exception as e:
                        await tenant_db_session.rollback()
                        log.error(
                            f"❌ Failed to process tenant {tenant_db_name}: {str(e)}"
                        )
                        all_success = False

    if all_success:
        log.info("✅ All Tenant completed successfully")
    else:
        log.error("❌ Some Tenant failed")

    return all_success
