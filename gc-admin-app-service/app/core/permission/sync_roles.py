from sqlalchemy import update

from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.core.permissions.load_file_yaml import load_tenant_roles_yaml
from gc_dentist_shared.tenant_models.roles import Roles


async def sync_roles(tenant_db_session):
    """Sync roles for multiple tenants"""
    # Get the directory where this script is located
    try:
        roles_data_yaml = load_tenant_roles_yaml()

        # Load existing roles system and Mark all roles as deleted
        existing_roles = await fetch_and_soft_delete_all_system_roles(tenant_db_session)

        # Load roles from YAML and create/update them
        roles = roles_data_yaml.get("roles", [])
        role_objs_add = []
        for role in roles:
            role_key_id = role["role_key_id"]
            if role_key_id in existing_roles:
                await restore_role(tenant_db_session, role)
            else:
                role_objs_add.append(
                    Roles(
                        role_key_id=role["role_key_id"],
                        name_json=role.get("name_json"),
                        is_active=role.get("is_active", True),
                        is_system=role.get("is_system", True),
                        delete_flag=False,
                    )
                )

        tenant_db_session.add_all(role_objs_add)
        await tenant_db_session.flush()
    except Exception as e:
        log.error(f"❌ Failed to sync roles: {str(e)}")
        raise


async def fetch_and_soft_delete_all_system_roles(tenant_db) -> list[int]:
    stmt = (
        update(Roles)
        .where(Roles.is_system.is_(True))
        .values(delete_flag=True)
        .returning(Roles.role_key_id)
    )
    result = await tenant_db.scalars(stmt)
    return list(result.all())


async def restore_role(tenant_db, role: dict) -> None:
    await tenant_db.execute(
        update(Roles)
        .where(Roles.role_key_id == role["role_key_id"])
        .values(
            name_json=role.get("name_json"),
            is_active=role.get("is_active", True),
            is_system=role.get("is_system", True),
            delete_flag=False,
        )
    )
