from sqlalchemy import select, update

from gc_dentist_shared.central_models.tenant_plans import TenantPlan
from gc_dentist_shared.core.enums.plan_enum import PLAN_KEY_MAPPING
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.core.permissions.load_file_yaml import (
    load_central_plan_permissions_yaml,
)
from gc_dentist_shared.tenant_models.permissions import Permission


async def sync_permissions(tenant_db_session, plan_key_id):
    try:
        central_plan_permissions_yaml = load_central_plan_permissions_yaml()
        all_permissions_of_plan = central_plan_permissions_yaml.get(
            PLAN_KEY_MAPPING[plan_key_id]
        )

        if all_permissions_of_plan is None:
            msg_error = "❌ Error not get permissions of plan"
            log.error(msg_error)
            raise Exception(msg_error)

        # Load existing permission system and Mark all permission as deleted
        existing_permission = await fetch_and_soft_delete_all_permissions(
            tenant_db_session
        )
        perm_objs_add = []
        for module, sub_tree in all_permissions_of_plan.items():
            parsed = parse_permissions(sub_tree, module=module)
            for p in parsed:
                key = p.permission_key
                if key in existing_permission:
                    await restore_permission(
                        tenant_db_session, existing_permission[key]
                    )
                else:
                    perm_objs_add.append(p)

        tenant_db_session.add_all(perm_objs_add)
        await tenant_db_session.flush()

    except Exception as e:
        log.error(f"❌ Failed to sync permission: {str(e)}")
        raise


async def check_exists_tenant_plan(central_db_session, tenant_uuid, plan_key_id):
    async with central_db_session:
        result = await central_db_session.execute(
            select(TenantPlan.id).where(
                TenantPlan.tenant_uuid == tenant_uuid,
                TenantPlan.plan_key_id == plan_key_id,
            )
        )
    return result.scalar_one_or_none() is not None


async def fetch_and_soft_delete_all_permissions(tenant_db) -> dict:
    stmt = (
        update(Permission)
        .where(Permission.id.isnot(None))
        .values(delete_flag=True)
        .returning(
            Permission.id,
            Permission.permission_key,
        )
    )

    result = await tenant_db.execute(stmt)
    rows = result.mappings().all()
    return {p["permission_key"]: p["id"] for p in rows}


async def restore_permission(tenant_db, permission_id: int):
    await tenant_db.execute(
        update(Permission)
        .where(Permission.id == permission_id)
        .values(delete_flag=False)
    )


def parse_permissions(tree: dict, module: str = "", path: list[str] | None = None):
    if path is None:
        path = []

    permissions = []
    for key, value in tree.items():
        current_path = [*path, key] if key else path
        if isinstance(value, list):
            full_path = ".".join(filter(None, current_path))
            for action in value:
                permission_key = (
                    f"{module}.{full_path}:{action}"
                    if full_path
                    else f"{module}:{action}"
                )
                permissions.append(
                    Permission(
                        module=module,
                        sub_module=full_path or "",
                        action=action,
                        permission_key=permission_key,
                        delete_flag=False,
                    )
                )
        elif isinstance(value, dict):
            permissions += parse_permissions(value, module, current_path)
    return permissions
