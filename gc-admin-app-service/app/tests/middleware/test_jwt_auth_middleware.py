from contextlib import Async<PERSON>xitStack
from datetime import datetime
from unittest.mock import patch

import pytest
import pytest_asyncio
from fastapi import status
from httpx import ASGITransport, AsyncClient
from tests.helpers.insert_data.insert_oauth_client import unittest_insert_oauth_client
from tests.helpers.insert_data.insert_oauth_token import (
    unittest_create_jwt_token,
    unittest_insert_oauth_token,
)
from tests.helpers.insert_data.insert_system_user import (
    unittest_insert_unittest_system_user,
)
from tests.helpers.jwks_mock import MockJWKSClient
from werkzeug.security import gen_salt

from gc_dentist_shared.core.common.digital_signature import DigitalSignature
from gc_dentist_shared.core.constants import AUTHORIZATION_HEADER
from gc_dentist_shared.core.messages import CustomMessageCode


class TestJWTAuthMiddleware:
    pytestmark = pytest.mark.skip_middleware_auth

    @pytest.fixture
    def app_local(self):
        from main import create_app

        app = create_app()

        @app.get("/protected")
        def protected_endpoint():
            return {"message": "success"}

        return app

    @pytest_asyncio.fixture
    async def async_client_local(self, app_local):
        transport = ASGITransport(app=app_local, raise_app_exceptions=False)
        async with AsyncClient(transport=transport, base_url="http://test") as ac:
            yield ac

    @pytest_asyncio.fixture(scope="class")
    def rsa_key_data(self):
        rsa_key_pair = DigitalSignature.create_rsa_key_pair()
        return {
            "key_id": f"key_{datetime.now().strftime('%Y%m%d')}",
            "private_key": rsa_key_pair["private_key"].encode(),
            "public_key": rsa_key_pair["public_key"].encode(),
        }

    @pytest_asyncio.fixture(scope="class")
    async def setup_data(
        self,
        async_central_db_session_object,
        rsa_key_data,
    ):
        async with async_central_db_session_object.begin():
            system_user_id = await unittest_insert_unittest_system_user(
                async_central_db_session_object
            )

            system_user_not_active_id = await unittest_insert_unittest_system_user(
                async_central_db_session_object, custom_user_fields={"is_active": False}
            )

            internal_oauth_client = await unittest_insert_oauth_client(
                async_central_db_session_object
            )
            valid_token = await unittest_insert_oauth_token(
                async_central_db_session_object,
                internal_client=internal_oauth_client,
                rsa_key_pair=rsa_key_data,
                system_user_id=system_user_id,
            )

            expired_token = await unittest_insert_oauth_token(
                async_central_db_session_object,
                internal_client=internal_oauth_client,
                rsa_key_pair=rsa_key_data,
                system_user_id=system_user_id,
                token_expire=-5,
            )

            invalid_user_token = await unittest_insert_oauth_token(
                async_central_db_session_object,
                internal_client=internal_oauth_client,
                rsa_key_pair=rsa_key_data,
                system_user_id=system_user_not_active_id,
            )

        return {
            "internal_oauth_client": internal_oauth_client,
            "system_user_id": system_user_id,
            "valid_token": valid_token,
            "expired_token": expired_token,
            "invalid_user_token": invalid_user_token,
        }

    @staticmethod
    def get_headers(token_type: str, access_token: str) -> dict:
        headers = {
            AUTHORIZATION_HEADER: f"{token_type} {access_token}",
        }
        return headers

    """ TEST CASES """

    @pytest.mark.asyncio
    async def test_exclude_url(
        self,
        async_client_local,
    ):
        response = await async_client_local.get("/protected", headers={})
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert response.json()["msg"] == "Unauthorized!"

        with patch(
            "configuration.settings.configuration.TOKEN_EXCLUDE_URLS", ["/protected"]
        ):
            response = await async_client_local.get("/protected", headers={})
            assert response.status_code == status.HTTP_200_OK
            assert response.json() == {"message": "success"}

    @pytest.mark.asyncio
    async def test_authentication_success(
        self,
        async_client_local,
        async_central_db_session_object,
        setup_data,
        rsa_key_data,
    ):
        valid_token = setup_data["valid_token"]
        headers = self.get_headers(
            token_type=valid_token["token_type"],
            access_token=valid_token["access_token"],
        )
        jwks_client_mock = MockJWKSClient(rsa_key_data["public_key"])
        with patch(
            "gc_dentist_shared.core.common.jwks_client_manager.JWKSClientManager.get_instance",
            return_value=jwks_client_mock,
        ):
            response = await async_client_local.get("/protected", headers=headers)

            assert response.status_code == status.HTTP_200_OK
            assert response.json() == {"message": "success"}

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "test_id, headers_func, expected_code, expected_message",
        [
            # Missing Authentication Header
            (
                "missing_auth_header",
                lambda self, setup_data: {},
                CustomMessageCode.UNAUTHORIZED_ERROR.code,
                CustomMessageCode.UNAUTHORIZED_ERROR.title,
            ),
            # Invalid Authorization Header
            (
                "invalid_token_format",
                lambda self, setup_data: {
                    AUTHORIZATION_HEADER: "InvalidFormat",
                },
                CustomMessageCode.UNAUTHORIZED_ERROR.code,
                CustomMessageCode.UNAUTHORIZED_ERROR.title,
            ),
            # Invalid Token Type
            (
                "invalid_token_type",
                lambda self, setup_data: {
                    AUTHORIZATION_HEADER: f"{gen_salt(5)} {setup_data['valid_token']['access_token']}",
                },
                CustomMessageCode.UNAUTHORIZED_ERROR.code,
                CustomMessageCode.UNAUTHORIZED_ERROR.title,
            ),
            # Invalid Access Token Data
            (
                "invalid_access_token",
                lambda self, setup_data: {
                    AUTHORIZATION_HEADER: f"{setup_data['valid_token']['token_type']} {gen_salt(48)}",
                },
                CustomMessageCode.UNAUTHORIZED_ERROR.code,
                CustomMessageCode.UNAUTHORIZED_ERROR.title,
            ),
            # Access Token Expired
            (
                "expired_access_token",
                lambda self, setup_data: {
                    AUTHORIZATION_HEADER: (
                        f"{setup_data['expired_token']['token_type']} "
                        f"{setup_data['expired_token']['access_token']}"
                    ),
                },
                CustomMessageCode.TOKEN_EXPIRED.code,
                CustomMessageCode.TOKEN_EXPIRED.title,
            ),
            # Invalid Doctor User
            (
                "invalid_system_user",
                lambda self, setup_data: {
                    AUTHORIZATION_HEADER: (
                        f"{setup_data['invalid_user_token']['token_type']} "
                        f"{setup_data['invalid_user_token']['access_token']}"
                    ),
                },
                CustomMessageCode.UNAUTHORIZED_ERROR.code,
                CustomMessageCode.UNAUTHORIZED_ERROR.title,
            ),
        ],
    )
    async def test_authentication_failures(
        self,
        test_id,
        headers_func,
        expected_code,
        expected_message,
        async_client_local,
        setup_data,
        async_central_db_session_object,
        tenant_uuid,
        rsa_key_data,
    ):
        headers = headers_func(self, setup_data)

        jwks_client_mock = MockJWKSClient(rsa_key_data["public_key"])
        with patch(
            "gc_dentist_shared.core.common.jwks_client_manager.JWKSClientManager.get_instance",
            return_value=jwks_client_mock,
        ):
            response = await async_client_local.get("/protected", headers=headers)

            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            result = response.json()
            assert result["msg"] == expected_message
            assert result["msg_code"] == expected_code

    @pytest.mark.asyncio
    async def test_authentication_with_valid_jwt_not_in_db(
        self,
        async_client_local,
        async_central_db_session_object,
        setup_data,
        rsa_key_data,
    ):
        # Valid JWT Token but does not exist in DB
        jwt_token = unittest_create_jwt_token(
            user_id=setup_data["system_user_id"],
            internal_client=setup_data["internal_oauth_client"],
            token_expire=10,
            rsa_key_pair=rsa_key_data,
        )

        headers = self.get_headers(
            token_type=jwt_token["token_type"],
            access_token=jwt_token["access_token"],
        )

        jwks_client_mock = MockJWKSClient(rsa_key_data["public_key"])
        with patch(
            "gc_dentist_shared.core.common.jwks_client_manager.JWKSClientManager.get_instance",
            return_value=jwks_client_mock,
        ):
            response = await async_client_local.get("/protected", headers=headers)

            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            result = response.json()
            assert result["msg"] == CustomMessageCode.UNAUTHORIZED_ERROR.title
            assert result["msg_code"] == CustomMessageCode.UNAUTHORIZED_ERROR.code

    @pytest.mark.asyncio
    async def test_authentication_invalid_public_key(
        self,
        async_client_local,
        setup_data,
        async_central_db_session_object,
        rsa_key_data,
    ):
        headers = self.get_headers(
            token_type=setup_data["valid_token"]["token_type"],
            access_token=setup_data["valid_token"]["access_token"],
        )
        jwks_client_mock = MockJWKSClient("invalid_public_key")
        with patch(
            "gc_dentist_shared.core.common.jwks_client_manager.JWKSClientManager.get_instance",
            return_value=jwks_client_mock,
        ):
            response = await async_client_local.get("/protected", headers=headers)

            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            result = response.json()
            assert result["msg"] == CustomMessageCode.UNAUTHORIZED_ERROR.title
            assert result["msg_code"] == CustomMessageCode.UNAUTHORIZED_ERROR.code

    @pytest.mark.asyncio
    async def test_authentication_case_db_exceptions(
        self,
        async_client_local,
        async_central_db_session_object,
        setup_data,
        rsa_key_data,
    ):

        headers = self.get_headers(
            token_type=setup_data["valid_token"]["token_type"],
            access_token=setup_data["valid_token"]["access_token"],
        )

        jwks_client_mock = MockJWKSClient(rsa_key_data["public_key"])
        async with AsyncExitStack() as stack:
            stack.enter_context(
                patch(
                    "gc_dentist_shared.core.common.jwks_client_manager.JWKSClientManager.get_instance",
                    return_value=jwks_client_mock,
                )
            )
            stack.enter_context(
                patch(
                    "db.db_connection.CentralDatabase.get_instance_db",
                    side_effect=Exception("Database connection error"),
                )
            )

            response = await async_client_local.get("/protected", headers=headers)

            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            result = response.json()
            assert result["msg"] == CustomMessageCode.UNAUTHORIZED_ERROR.title
            assert result["msg_code"] == CustomMessageCode.UNAUTHORIZED_ERROR.code
