from unittest.mock import patch

import pytest
from core.constants import ClinicRedisKey
from core.messages import CustomMessageCode
from tests.helpers.redis_mock import mock_redis_client


def set_header_to_redis(clinic_no):
    mock_redis_client.set(
        name=ClinicRedisKey.CLINICS_CACHE.value, value=str([clinic_no])
    )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "header_clinic_no, expected_status, is_redis_set, message, exists",
    [
        (True, 200, True, None, True),
        (
            "invalid-slug",
            400,
            False,
            CustomMessageCode.CLINIC_NOT_FOUND.title,
            False,
        ),
        (
            "",
            400,
            False,
            CustomMessageCode.X_TENANT_CLINIC_NO_IS_REQUIRED.title,
            False,
        ),
        (True, 200, False, None, True),
    ],
)
async def test_clinic_no_check(
    async_client,
    clinic_no,
    header_clinic_no,
    expected_status,
    is_redis_set,
    message,
    exists,
):
    if is_redis_set:
        set_header_to_redis(clinic_no)

    headers = {"X-Tenant-Clinic-No": f"{header_clinic_no}"}

    if isinstance(header_clinic_no, bool):
        headers = {"X-Tenant-Clinic-No": clinic_no}
    with patch(
        "gc_dentist_shared.core.common.redis.RedisCli.get_instance",
        return_value=mock_redis_client,
    ):
        response = await async_client.get("/v1_0/tenants/clinic-no", headers=headers)
        result = response.json()
        assert response.status_code == expected_status
        assert result.get("message") == message
