# import uuid
# from unittest.mock import AsyncMock, patch

# import pytest
# from core.constants import TenantClinicStatus
# from fastapi import status
# from services.tenant_clinics_service import TenantClinicService
# from sqlalchemy_utils import drop_database


# # @pytest.mark.asyncio
# # @patch("services.migration_service.TenantClinicService.create_tenant_clinic_info")
# # async def test_create_tenant_streaming_realtime(
# #     mock_create_clinic_info,
# #     async_client,
# #     async_central_db_session_object,
# #     build_postgres_url,
# #     mock_get_url_db_sync,
# # ):
# #     mock_create_clinic_info.return_value = AsyncMock()
# #     db_name = "unittest_" + str(uuid.uuid4())
# #     mock_tenant_uri = mock_get_url_db_sync(db_name)
# #     clinic_name = str(uuid.uuid4())
# #     tenant_uuid = str(uuid.uuid4())
# #     payload = {
# #         "tenant_uuid": tenant_uuid,
# #         "clinic_name": clinic_name,
# #         "clinic_no": clinic_name,
# #         "db_name": db_name,
# #         "plan_id": 1,
# #         "clinic_info": {
# #             "clinic_name": "noda_clinic 2",
# #             "phone_number": "+81347913389",
# #             "email": "<EMAIL>",
# #             "address_1": "adress_1",
# #             "address_2": "adress_2",
# #             "address_3": "adress_3",
# #         },
# #         "manager_info": {
# #             "phone": "0347913389",
# #             "country_code": "+81",
# #             "gender": 1,
# #             "date_of_birth": "2000/02/02",
# #             "first_name": "admin",
# #             "last_name": "test",
# #             "email": "<EMAIL>",
# #             "password": str(uuid.uuid4()) + "@A" + "123456",
# #             "required_change_password": True,
# #             "is_active": True,
# #         },
# #     }

# #     response = await async_client.post("/v1_0/tenants/streaming", json=payload)
# #     assert response.status_code == status.HTTP_200_OK
# #     assert "Created database" in response.text
# #     assert "Migration database successfully" in response.text

# #     migrate_payload = {"databases": [{"tenant_uuid": tenant_uuid, "db_name": db_name}]}
# #     response = await async_client.post(
# #         "/v1_0/tenants/migration/streaming", json=migrate_payload
# #     )
# #     assert response.status_code == status.HTTP_200_OK
# #     assert "Start Migration Database Changes" in response.text

# #     clinic_service = TenantClinicService(session=async_central_db_session_object)
# #     new_tenant = await clinic_service.get_tenant_data_by_name(clinic_name=clinic_name)
# #     assert new_tenant.status == TenantClinicStatus.SUCCESS
# #     # url_db = build_postgres_url(db_name)
# #     drop_database(mock_tenant_uri)


# # @pytest.mark.asyncio
# # async def test_create_tenant_streaming(async_client):
# #     async def mock_stream(data, session):
# #         yield "mock line1\n"
# #         yield "mock line2\n"

# #     payload = {
# #         "clinic_name": str(uuid.uuid4()),
# #         "clinic_no": str(uuid.uuid4()),
# #         "db_name": "unittest_" + str(uuid.uuid4()),
# #         "plan_id": 1,
# #         "clinic_info": {
# #             "clinic_name": "noda_clinic 2",
# #             "phone_number": "+81347913389",
# #             "email": "<EMAIL>",
# #             "address_1": "adress_1",
# #             "address_2": "adress_2",
# #             "address_3": "adress_3",
# #         },
# #         "manager_info": {
# #             "phone": "0347913389",
# #             "country_code": "+81",
# #             "gender": 1,
# #             "date_of_birth": "2000/02/02",
# #             "first_name": "admin",
# #             "last_name": "test",
# #             "email": "<EMAIL>",
# #             "password": str(uuid.uuid4()) + "@A" + "123456",
# #             "required_change_password": True,
# #             "is_active": True,
# #         },
# #     }

# #     with patch(
# #         "api.v1.tenant.tenants_api.create_tenant_stream"
# #     ) as mock_create_tenant_stream:
# #         mock_create_tenant_stream.side_effect = mock_stream
# #         response = await async_client.post("/v1_0/tenants/streaming", json=payload)
# #         assert response.status_code == status.HTTP_200_OK
# #         assert "mock line1" in response.text
# #         assert "mock line2" in response.text


# @pytest.mark.asyncio
# @patch("api.v1.tenant.tenants_api.migration_multi_database_stream")
# async def test_migration_multi_database_stream(mock_migration_db_changes, async_client):
#     async def mock_migration_multi_database_stream(data):
#         yield "migration1\n"
#         yield "migration2\n"

#     mock_migration_db_changes.side_effect = mock_migration_multi_database_stream

#     payload = {
#         "databases": [{"tenant_uuid": str(uuid.uuid4()), "db_name": "clinic_a_db"}]
#     }

#     response = await async_client.post(
#         "/v1_0/tenants/migration/streaming", json=payload
#     )
#     assert response.status_code == status.HTTP_200_OK
#     assert "migration1" in response.text
#     assert "migration2" in response.text


# @pytest.mark.asyncio
# @patch("api.v1.tenant.tenants_api.TenantClinicService.get_tenant_db_by_name")
# async def test_create_tenant_fail_existing(mock_get_tenant_db, async_client):
#     mock_get_tenant_db.return_value = "db_existing"

#     payload = {
#         "clinic_name": str(uuid.uuid4()),
#         "clinic_no": str(uuid.uuid4()),
#         "db_name": "unittest_" + str(uuid.uuid4()),
#         "plan_id": 1,
#         "clinic_info": {
#             "clinic_name": "noda_clinic 2",
#             "phone_number": "+81347913389",
#             "email": "<EMAIL>",
#             "address_1": "adress_1",
#             "address_2": "adress_2",
#             "address_3": "adress_3",
#         },
#         "manager_info": {
#             "phone": "0347913389",
#             "country_code": "+81",
#             "gender": 1,
#             "date_of_birth": "2000/02/02",
#             "first_name": "admin",
#             "last_name": "test",
#             "email": "<EMAIL>",
#             "password": str(uuid.uuid4()) + "@A" + "123456",
#             "required_change_password": True,
#             "is_active": True,
#         },
#     }

#     response = await async_client.post("/v1_0/tenants/streaming", json=payload)
#     assert response.status_code == status.HTTP_200_OK
#     assert "Tenant already exists." in response.text
