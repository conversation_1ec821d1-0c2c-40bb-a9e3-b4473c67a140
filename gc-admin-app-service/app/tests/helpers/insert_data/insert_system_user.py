from uuid import uuid4

from gc_dentist_shared.central_models import SystemUser, SystemUserProfile


async def unittest_insert_unittest_system_user(
    db_session, custom_user_fields=None, custom_profile_fields=None
):
    email = str(uuid4()) + "@example.com"
    user_data = {
        "username": email,
        "password": str(uuid4()),
        "is_active": True,
    }
    if custom_user_fields:
        user_data.update(custom_user_fields)

    new_user = SystemUser(**user_data)
    db_session.add(new_user)
    await db_session.flush()
    await db_session.refresh(new_user)

    profile_data = {
        "system_user_id": new_user.id,
        "last_name": str(uuid4()),
        "first_name": str(uuid4()),
        "email": str(uuid4()) + "@example.com",
        "email_hash": str(uuid4()) + "@example.com",
    }
    if custom_profile_fields:
        profile_data.update(custom_profile_fields)

    new_profile = SystemUserProfile(**profile_data)
    db_session.add(new_profile)
    await db_session.flush()
    await db_session.refresh(new_profile)

    return new_user.id
