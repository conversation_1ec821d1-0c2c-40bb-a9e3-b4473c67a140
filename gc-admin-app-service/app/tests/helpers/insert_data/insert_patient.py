import uuid

from sqlalchemy import func, select
from tests.helpers.enums.patient import <PERSON><PERSON><PERSON><PERSON>

from gc_dentist_shared.tenant_models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PatientUser


async def unittest_auto_generate_patient_no(db_session) -> str:
    query = select(func.nextval("patient_users_patient_no_seq"))
    result = await db_session.execute(query)
    patient_no = result.scalar_one_or_none()
    return str(patient_no).zfill(11)


async def unittest_insert_patient(
    db_session,
    custom_user_fields=None,
    custom_profile_fields=None,
):
    patient_no = await unittest_auto_generate_patient_no(db_session)
    user_data = {
        "username": str(uuid.uuid4()),
        "patient_no": patient_no,
        "status": True,
    }
    if custom_user_fields:
        user_data.update(custom_user_fields)

    new_user = PatientUser(**user_data)
    db_session.add(new_user)
    await db_session.flush()
    await db_session.refresh(new_user)

    profile_data = {
        "patient_user_id": new_user.id,
        "last_name": str(uuid.uuid4()),
        "first_name": str(uuid.uuid4()),
        "last_name_kana": str(uuid.uuid4()),
        "first_name_kana": str(uuid.uuid4()),
        "gender": UnittestGender.MALE,
        "date_of_birth": "1990/01/01",
        "prefecture_id": 1,
        "postal_code": "0600000",
    }
    if custom_profile_fields:
        profile_data.update(custom_profile_fields)

    new_profile = PatientProfile(**profile_data)
    db_session.add(new_profile)
    await db_session.flush()
    await db_session.refresh(new_profile)

    return new_user.id
