from datetime import datetime, timedelta, timezone
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock
from uuid import uuid4

import pytest
from app.services.tenant_clinics_service import TenantClinicService
from core.constants import TenantClinicStatus
from pydantic import ValidationError
from schemas.tenant_clinic_requests import (
    ClinicInfoSchema,
    CreateClinicInfoSchema,
    CreateTenantSchema,
    ManagerTenant,
    PlanSchema,
    TenantPayloads,
)
from sqlalchemy.exc import DB<PERSON><PERSON><PERSON>r, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.exception_handler.max_retry_exception import (
    MaxRetriesExceededError,
)


@pytest.fixture
def mock_session():
    session = AsyncMock(spec=AsyncSession)

    async_context = AsyncMock()
    async_context.__aenter__.return_value = session
    async_context.__aexit__.return_value = None

    # async def aexit_mock():
    #     await session.commit()
    #     return None

    # async_context.__aexit__ = aexit_mock

    session.begin.return_value = async_context
    # session.commit = AsyncMock()

    return session


@pytest.fixture
def mock_tenant_clinic_service(mock_session):
    return TenantClinicService(session=mock_session)


@pytest.mark.asyncio
async def test_create_tenant_success(async_central_db_session_object):
    # Mock data
    data = CreateTenantSchema(
        clinic_name=str(uuid4()),
        clinic_no=str(uuid4()),
        db_name=str(uuid4()),
        clinic_info=ClinicInfoSchema(
            clinic_name=str(uuid4()),
            phone_number="09051234567",
            email=f"test{str(uuid4())}@gmail.com",
            address_1=str(uuid4()),
            address_2=str(uuid4()),
            address_3=str(uuid4()),
        ),
        manager_info=ManagerTenant(
            phone="09051234567",
            country_code="+81",
            gender=1,
            date_of_birth="2000/02/02",
            first_name=str(uuid4()),
            last_name=str(uuid4()),
            email=f"test{str(uuid4())}@gmail.com",
            password="Tiendang@123",  # noqa: S106 # pragma: allowlist secret
        ),
        plan_info=PlanSchema(
            plan_key_id=1,
            start_at=datetime.now(timezone.utc),
            end_at=datetime.now(timezone.utc) + timedelta(days=1),
        ),
    )

    service = TenantClinicService(async_central_db_session_object)
    result = await service.create_tenant(data)
    assert result.clinic_name == data.clinic_name
    assert result.tenant_uuid is not None


@pytest.mark.asyncio
async def test_create_tenant_retry_failure(mock_tenant_clinic_service):
    data = TenantPayloads(
        clinic_name="Clinic B",
        clinic_no="clinic-b",
        db_name="clinic_b_db",
        db_uri="postgresql://clinic_b",
    )
    mock_tenant_clinic_service.create_tenant = AsyncMock(
        side_effect=MaxRetriesExceededError(
            "test_create_tenant_max_retries_exceeded",
            Exception("Cannot Create Tenant"),
        )
    )
    # Act & Assert
    with pytest.raises(MaxRetriesExceededError) as exc_info:
        await mock_tenant_clinic_service.create_tenant(data)

        assert "test_create_tenant_max_retries_exceeded" in str(exc_info.value)

        assert mock_tenant_clinic_service.create_tenant.call_count == 3  # Max retries


@pytest.mark.asyncio
async def test_update_tenant_status_success(async_central_db_session_object):
    # Act
    data = CreateTenantSchema(
        clinic_name=str(uuid4()),
        clinic_no=str(uuid4()),
        db_name=str(uuid4()),
        clinic_info=ClinicInfoSchema(
            clinic_name=str(uuid4()),
            phone_number="09051234567",
            email=f"test{str(uuid4())}@gmail.com",
            address_1=str(uuid4()),
            address_2=str(uuid4()),
            address_3=str(uuid4()),
        ),
        manager_info=ManagerTenant(
            phone="09051234567",
            country_code="+81",
            gender=1,
            date_of_birth="2000/02/02",
            first_name=str(uuid4()),
            last_name=str(uuid4()),
            email=f"test{str(uuid4())}@gmail.com",
            password="Tiendang@123",  # noqa: S106 # pragma: allowlist secret
        ),
        plan_info=PlanSchema(
            plan_key_id=1,
            start_at=datetime.now(timezone.utc),
            end_at=datetime.now(timezone.utc) + timedelta(days=1),
        ),
    )
    service = TenantClinicService(async_central_db_session_object)
    new_tenant = await service.create_tenant(data)
    result = await service.update_tenant_status(
        new_tenant.tenant_uuid, TenantClinicStatus.SUCCESS
    )
    assert result is not None

    assert result.tenant_uuid == new_tenant.tenant_uuid


@pytest.mark.asyncio
async def test_update_tenant_status_retry_failure(mock_session):
    tenant_uuid = str(uuid4())
    status = 1
    mock_session.execute = AsyncMock(
        side_effect=OperationalError("stmt", {}, Exception("DB Error"))
    )
    service = TenantClinicService(mock_session)
    with pytest.raises(MaxRetriesExceededError):
        await service.update_tenant_status(tenant_uuid, status)


@pytest.mark.asyncio
async def test_get_db_name_for_tenant_success(mock_session):
    tenant_uuid = str(uuid4())
    expected_db_name = "clinic_db"
    mock_result = MagicMock()
    mock_result.scalar_one_or_none.return_value = expected_db_name
    mock_session.execute = AsyncMock(return_value=mock_result)
    service = TenantClinicService(mock_session)

    result = await service.get_db_name_for_tenant(tenant_uuid)

    mock_session.execute.assert_awaited_once()
    assert result == expected_db_name


@pytest.mark.asyncio
async def test_get_db_name_for_tenant_retry_failure(mock_session):
    tenant_uuid = str(uuid4())
    mock_session.execute = AsyncMock(
        side_effect=DBAPIError("stmt", {}, Exception("DB Error"))
    )
    service = TenantClinicService(mock_session)
    with pytest.raises(MaxRetriesExceededError):
        await service.get_db_name_for_tenant(tenant_uuid)


# @pytest.mark.asyncio
# async def test_create_tenant_clinic_info_retry_failure():
#     mock_session = MagicMock()
#     service = TenantClinicService(mock_session)
#     clinic_info = CreateClinicInfoSchema(
#         clinic_name=str(uuid4()),
#         clinic_db_name=str(uuid4()),
#         clinic_no=str(uuid4()),
#         clinic_uuid=str(uuid4()),
#         phone_number="0123456789",
#         email="<EMAIL>",
#         address_1="addr1",
#         address_2="addr2",
#         address_3="addr3",
#         manager_info={
#             "phone": "0347913389",
#             "country_code": "+81",
#             "gender": 1,
#             "date_of_birth": "2000/02/02",
#             "first_name": str(uuid4()),
#             "last_name": str(uuid4()),
#             "email": f"test{str(int(time.time()))}@gmail.com",
#             "password": "Tiendang@123",  # pragma: allowlist secret
#             "required_change_password": True,
#             "is_active": True,
#         },
#     )

#     with (
#         patch(
#             "db.db_connection.TenantDatabase.get_instance_tenant_db",
#             side_effect=OperationalError("Error DB", None, None),
#         ) as set_db_mock,
#         patch.object(service, "delete_cache_clinic_no", AsyncMock()),
#     ):
#         with pytest.raises(MaxRetriesExceededError):
#             result = await service.create_tenant_clinic_info(clinic_info)
#             assert result is None
#         assert set_db_mock.call_count == 3


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "clinic_info_data, error_field",
    [
        # missing clinic_name
        (
            {
                "clinic_no": "test-clinic",
                "clinic_uuid": "uuid-123",
                "phone_number": "0123456789",
                "email": "<EMAIL>",
                "address_1": "addr1",
                "address_2": "addr2",
                "address_3": "addr3",
                "manager_info": {"first_name": "admin"},
            },
            "clinic_name",
        ),
        # invalid email format
        (
            {
                "clinic_name": "Test Clinic",
                "clinic_no": "test-clinic",
                "clinic_uuid": "uuid-123",
                "phone_number": "0123456789",
                "email": "not-an-email",
                "address_1": "addr1",
                "address_2": "addr2",
                "address_3": "addr3",
                "manager_info": {"first_name": "admin"},
            },
            "email",
        ),
        # missing manager_info
        (
            {
                "clinic_name": "Test Clinic",
                "clinic_no": "test-clinic",
                "clinic_uuid": "uuid-123",
                "phone_number": "0123456789",
                "email": "<EMAIL>",
                "address_1": "addr1",
                "address_2": "addr2",
                "address_3": "addr3",
            },
            "manager_info",
        ),
    ],
)
def test_create_clinic_info_schema_invalid(clinic_info_data, error_field):
    with pytest.raises(ValidationError) as exc_info:
        CreateClinicInfoSchema(**clinic_info_data)
    assert error_field in str(exc_info.value)
