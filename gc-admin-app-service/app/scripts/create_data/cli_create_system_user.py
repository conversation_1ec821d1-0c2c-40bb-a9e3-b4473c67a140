import argparse
import asyncio

from db.db_connection import CentralDatabase
from scripts.common import (
    prompt_password_twice,
    prompt_set_db_uri,
    prompt_str,
    prompt_yes_no,
)
from scripts.create_data.create_system_user import script_create_system_user

from gc_dentist_shared.core.logger.config import log

# ================================================================
# CLI tool for creating a SystemUser + Profile
# Example:
"""
PYTHONPATH=$PYTHONPATH:$(pwd) python scripts/create_data/cli_create_system_user.py \
       --email <EMAIL> \
       --first-name Admin \
       --last-name Root \
       --phone 0364359198 \
       --country_code 84
"""
# Arguments:
#   email             : (str, required) Primary email (also used as username)
#   first-name        : (str, required) First name (given name)
#   last-name         : (str, required) Last name (family name)
#   first-name-kana   : (str, optional) First name in Kana
#   last-name-kana    : (str, optional) Last name in Kana
#   phone             : (str, optional) Phone number
#   country_code      : (str, optional) Country code (e.g., '81', '84')
#   password          : (str, optional) If not provided, you will be prompted
#
# Options:
#   -h, --help        : Show this help message and exit
#
# ================================================================


async def main():
    parser = argparse.ArgumentParser(description="Create a SystemUser + Profile")
    parser.add_argument("--email")
    parser.add_argument("--password")
    parser.add_argument("--first-name")
    parser.add_argument("--last-name")
    parser.add_argument("--first-name-kana")
    parser.add_argument("--last-name-kana")
    parser.add_argument("--phone")
    parser.add_argument("--country_code")
    args = parser.parse_args()

    if prompt_yes_no("❓ Do you want to use a DB URI from the configuration?"):
        db_uri = CentralDatabase.get_url()
    else:
        db_uri = prompt_set_db_uri()

    # Confirm the database URI before proceeding with other prompts
    log.info("📝 Using the following database URI: %s", db_uri)
    if not prompt_yes_no("❓ Is this database URI correct?"):
        log.warning("🔄 Please re-run the script with the correct URI.")
        raise SystemExit(1)

    email = args.email or prompt_str("👉 Enter Email (required)", allow_empty=False)
    first_name = args.first_name or prompt_str(
        "👉 Enter First name (required)", allow_empty=False
    )
    last_name = args.last_name or prompt_str(
        "👉 Enter Last name (required)", allow_empty=False
    )

    first_name_kana = (
        args.first_name_kana
        or prompt_str("👉 Enter First name kana (optional)", allow_empty=True)
        or None
    )
    last_name_kana = (
        args.last_name_kana
        or prompt_str("👉 Enter Last name kana (optional)", allow_empty=True)
        or None
    )

    phone = (
        args.phone
        or prompt_str("👉 Enter phone (optional - ex: 0364359198)", allow_empty=True)
        or None
    )
    country_code = (
        args.country_code
        or prompt_str("👉 Enter country code (optional - ex:+84)", allow_empty=True)
        or None
    )

    password = args.password or prompt_password_twice()
    payload = {
        "username": email,
        "password": password,
        "email": email,
        "first_name": first_name,
        "last_name": last_name,
        "first_name_kana": first_name_kana,
        "last_name_kana": last_name_kana,
        "phone": phone,
        "country_code": country_code,
    }
    log.info("📝 Prepared payload for creating SystemUser:")
    for key, value in payload.items():
        if key == "password":
            log.info("  %s: %s", key, "********")
        else:
            log.info("  %s: %s", key, value)

    if not prompt_yes_no("❓ Are these values correct?"):
        log.warning("🔄 Let's try again...\n")
        raise SystemExit(1)

    try:
        user_id = await script_create_system_user(payload, db_uri)
        log.info("🎉 Done. New user id = %s", user_id)
    except Exception as e:
        log.error("❌ Error: %s", e)
        raise


if __name__ == "__main__":
    asyncio.run(main())
