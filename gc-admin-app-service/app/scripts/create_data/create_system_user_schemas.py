from typing import Optional

from pydantic import BaseModel, Field, field_validator

from gc_dentist_shared.core.common.utils import CustomEmailStr
from gc_dentist_shared.core.common.validator import password_validator


class CreateSystemUserSchemas(BaseModel):
    username: CustomEmailStr = Field(
        ...,
        strip_whitespace=True,
        description="(required) This is email. Must be unique.",
    )

    password: str = Field(
        ...,
        min_length=8,
        strip_whitespace=True,
        description="(required) Password for the account (min 8 chars).",
    )

    @field_validator("password")
    @classmethod
    def validate_password(cls, value: str) -> str:
        return password_validator(value)


class CreateSystemUserProfileSchemas(BaseModel):
    email: CustomEmailStr = Field(
        ...,
        strip_whitespace=True,
        description="(required) Primary email. Must be unique.",
    )

    first_name: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="(required) First name (given name).",
    )
    last_name: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="(required) Last name (family name).",
    )

    first_name_kana: Optional[str] = Field(
        default=None,
        strip_whitespace=True,
        description="(optional) First name in Kana.",
    )
    last_name_kana: Optional[str] = Field(
        default=None,
        strip_whitespace=True,
        description="(optional) Last name in Kana.",
    )

    phone: Optional[str] = Field(
        default=None,
        min_length=10,
        max_length=11,
        strip_whitespace=True,
        description="(optional) Phone number (digits only, 10-11).",
    )
    country_code: Optional[str] = Field(
        default=None,
        min_length=2,
        max_length=3,
        strip_whitespace=True,
        description="(optional) Country calling code (e.g., '81', '84').",
    )
