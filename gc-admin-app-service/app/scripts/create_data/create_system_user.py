from configuration.settings import configuration
from scripts.create_data.create_system_user_schemas import (
    CreateSystemUserProfileSchemas,
    CreateSystemUserSchemas,
)
from sqlalchemy import select
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import async_sessionmaker, create_async_engine

from gc_dentist_shared.central_models import SystemUser, SystemUserProfile
from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.logger.config import log


@retry_on_failure(
    exceptions=(
        OperationalError,
        DBAPIError,
    ),
    log_prefix="script_create_system_user",
)
async def script_create_system_user(payload: dict, db_uri: str) -> int:
    system_user_data = CreateSystemUserSchemas(
        username=payload.get("username"), password=payload.get("password")
    )
    system_user_profile_data = CreateSystemUserProfileSchemas(
        email=payload.get("email"),
        first_name=payload.get("first_name"),
        last_name=payload.get("last_name"),
        first_name_kana=payload.get("first_name_kana"),
        last_name_kana=payload.get("last_name_kana"),
        phone=payload.get("phone"),
        country_code=payload.get("country_code"),
    )
    log.info("✅ Input validation done, ready to insert into DB.")

    engine = create_async_engine(db_uri, echo=False)
    session_marker = async_sessionmaker(autocommit=False, autoflush=False, bind=engine)

    async with session_marker() as central_session:
        try:
            async with central_session.begin():
                if await check_username_exists(
                    central_session, system_user_data.username
                ):
                    raise ValueError(
                        f"Username '{system_user_data.username}' already exists."
                    )

                system_user_id = await create_system_user(
                    central_session, system_user_data
                )

                create_system_user_profile(
                    central_session, system_user_id, system_user_profile_data
                )

            return system_user_id
        except Exception as e:
            log.error(f"❌ Error create system user: {e}")
            raise e


async def check_username_exists(session, username: str) -> bool:
    res = await session.execute(
        select(SystemUser.id).where(SystemUser.username == username)
    )
    return res.scalar_one_or_none() is not None


async def create_system_user(
    session,
    system_user_data: CreateSystemUserSchemas,
) -> int:
    user = SystemUser(
        **system_user_data.model_dump(),
        is_active=True,
    )
    user.set_password(system_user_data.password)

    session.add(user)
    await session.flush()
    log.info("✅ Created SystemUser (id=%s, username=%s)", user.id, user.username)
    return user.id


def create_system_user_profile(
    session,
    system_user_id: int,
    system_user_profile_data: CreateSystemUserProfileSchemas,
) -> int:
    system_user_profile = SystemUserProfile(
        system_user_id=system_user_id,
        **system_user_profile_data.model_dump(),
    )

    aes = AesGCMRotation(configuration)
    system_user_profile.phone_hash = aes.sha256_hash(system_user_profile.phone)
    system_user_profile.email_hash = aes.sha256_hash(system_user_profile.email)
    system_user_profile.phone = aes.encrypt_data(system_user_profile.phone)
    system_user_profile.email = aes.encrypt_data(system_user_profile.email)

    session.add(system_user_profile)
    log.info("✅ Created SystemUserProfile (user_id=%s)", system_user_id)
    return system_user_profile.id
