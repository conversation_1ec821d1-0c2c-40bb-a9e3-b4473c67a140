import urllib.parse
from getpass import getpass

from configuration.settings import configuration

from gc_dentist_shared.core.logger.config import log


def prompt_yes_no(question: str) -> bool:
    while True:
        answer = input(f"{question} (y/n): ").strip().lower()
        if answer in {"y", "yes"}:
            return True
        if answer in {"n", "no"}:
            return False
        log.warning("Please enter 'y' or 'n'.")


def prompt_int(question: str) -> int:
    while True:
        raw = input(question).strip()
        try:
            return int(raw)
        except ValueError:
            log.warning("Please enter a valid integer.")


def prompt_str(question: str, allow_empty: bool = False) -> str:
    while True:
        answer = input(f"{question}: ").strip()
        if answer or allow_empty:
            return answer
        log.warning("Please enter a non-empty string.")


def prompt_password_twice() -> str:
    while True:
        p1 = getpass("👉  Password (required): ")
        p2 = getpass("👉 Confirm password (required): ")
        if not p1:
            log.warning("Password cannot be empty.")
            continue
        if p1 != p2:
            log.warning("Passwords do not match. Try again.")
            continue
        return p1


def check_db_configuration() -> None:
    # 1) Show connection info
    log.info("📌 Database connection info:")
    log.info("  Host      : %s", configuration.POSTGRES_SERVER)
    log.info("  Port      : %s", configuration.POSTGRES_PORT)
    log.info("  User      : %s", configuration.POSTGRES_USER)
    log.info("  Global DB : %s", configuration.POSTGRES_GLOBAL_DB_NAME)

    # 2) Confirm correctness
    if not prompt_yes_no("Are these configuration correct?"):
        log.error("❌ Exiting. Please update your configuration and try again.")
        raise SystemExit(1)


def prompt_set_db_uri() -> str:
    while True:
        try:
            username = input("👉 Enter DB username: ")
            password = input(
                "👉 Enter DB password (special characters are handled automatically): "
            )
            host = input("👉 Enter DB host: ")
            port = int(input("👉 Enter DB port: "))
            db_name = input("👉 Enter DB name: ")

            encoded_username = urllib.parse.quote_plus(username)
            encoded_password = urllib.parse.quote_plus(password)

            # Construct the full URI
            db_uri = f"postgresql+asyncpg://{encoded_username}:{encoded_password}@{host}:{port}/{db_name}"

            log.info("\n--- Generated URI ---")
            log.info("Use this URI to connect to your database: ")
            log.info(f"{db_uri} \n")
            return db_uri
        except Exception as e:
            log.error(f"❌ Error set db uri {str(e)}")
            log.warning("Try again enter db uri")
