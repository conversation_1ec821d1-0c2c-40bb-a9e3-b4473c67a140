import asyncio
import random
from datetime import date, timedelta
from uuid import uuid4

from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from db.db_connection import TenantDatabase
from scripts.common import check_db_configuration, prompt_int
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.enums.document import (
    DocumentDataType,
    DocumentDisplayMode,
    DocumentExtension,
    DocumentS3Status,
    DocumentStatus,
)
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import DocumentGroup, DocumentManagement

S3_BFA_MAIN_IMAGE_PATH = "develop/dummy/main/bfa.png"
S3_BFA_SUB_IMAGE_PATH = "develop/dummy/sub/bfa.png"

S3_BTE_MAIN_IMAGE_PATH = "develop/dummy/main/bte.png"
S3_BTE_SUB_IMAGE_PATH = "develop/dummy/sub/bte.png"

S3_EMG_MAIN_IMAGE_PATH = "develop/dummy/main/emg.png"
S3_EMG_SUB_IMAGE_PATH = "develop/dummy/sub/emg.png"

S3_MA_MAIN_IMAGE_PATH = "develop/dummy/main/mvt.png"
S3_MA_SUB_IMAGE_PATH = "develop/dummy/sub/mvt.png"


S3_INTRAORAL_MAIN_PATHS = [
    "develop/dummy/main/intraoral_1.png",
    "develop/dummy/main/intraoral_2.jpeg",
    "develop/dummy/main/intraoral_3.jpeg",
    "develop/dummy/main/intraoral_4.jpeg",
    "develop/dummy/main/intraoral_5.jpg",
]
S3_INTRAORAL_SUB_PATHS = [
    "develop/dummy/sub/intraoral_1.png",
    "develop/dummy/sub/intraoral_2.jpeg",
    "develop/dummy/sub/intraoral_3.jpeg",
    "develop/dummy/sub/intraoral_4.jpeg",
    "develop/dummy/sub/intraoral_5.jpg",
]


S3_XRAY_MAIN_PATHS = [
    "develop/dummy/main/x_ray_1.jpeg",
    "develop/dummy/main/x_ray_2.jpeg",
    "develop/dummy/main/x_ray_3.jpeg",
    "develop/dummy/main/x_ray_4.jpeg",
    "develop/dummy/main/x_ray_5.jpeg",
    "develop/dummy/main/x_ray_6.jpeg",
    "develop/dummy/main/x_ray_7.jpeg",
    "develop/dummy/main/x_ray_8.jpeg",
    "develop/dummy/main/x_ray_9.jpeg",
    "develop/dummy/main/x_ray_10.jpeg",
]
S3_XRAY_SUB_PATHS = [
    "develop/dummy/sub/x_ray_1.jpeg",
    "develop/dummy/sub/x_ray_2.jpeg",
    "develop/dummy/sub/x_ray_3.jpeg",
    "develop/dummy/sub/x_ray_4.jpeg",
    "develop/dummy/sub/x_ray_5.jpeg",
    "develop/dummy/sub/x_ray_6.jpeg",
    "develop/dummy/sub/x_ray_7.jpeg",
    "develop/dummy/sub/x_ray_8.jpeg",
    "develop/dummy/sub/x_ray_9.jpeg",
    "develop/dummy/sub/x_ray_10.jpeg",
]


S3_DUAL_VERT_MAIN_PATHS = [
    "develop/dummy/main/dual_vert_01.jpg",
    "develop/dummy/main/dual_vert_02.jpg",
]
S3_DUAL_VERT_SUB_PATHS = [
    "develop/dummy/sub/dual_vert_01.jpg",
    "develop/dummy/sub/dual_vert_02.jpg",
]


S3_DUAL_HORI_MAIN_PATHS = [
    "develop/dummy/main/dual_hori_01.jpg",
    "develop/dummy/main/dual_hori_02.jpeg",
]
S3_DUAL_HORI_SUB_PATHS = [
    "develop/dummy/sub/dual_hori_01.jpg",
    "develop/dummy/sub/dual_hori_02.jpeg",
]


S3_LIST_MAIN_PATHS = [
    "develop/dummy/main/list_image_01.jpg",
    "develop/dummy/main/list_image_02.png",
]
S3_LIST_SUB_PATHS = [
    "develop/dummy/sub/list_image_01.jpg",
    "develop/dummy/sub/list_image_02.png",
]


S3_FILE_PDF = "develop/dummy/main/data_pdf.pdf"
S3_FILE_XLSX = "develop/dummy/main/data_sheet.xlsx"


S3_JMS_DEVICE_MAIN_IMAGE_PATH = "develop/dummy/main/jms_device.jpg"
S3_JMS_DEVICE_SUB_IMAGE_PATH = "develop/dummy/sub/jms_device.jpg"

S3_GLUCO_SENSOR_MAIN_IMAGE_PATH = "develop/dummy/main/glucosensor.png"
S3_GLUCO_SENSOR_SUB_IMAGE_PATH = "develop/dummy/sub/glucosensor.png"

S3_ORAL_EXAMINATION_MAIN_IMAGE_PATH = "develop/dummy/main/oral_examination.png"
S3_ORAL_EXAMINATION_SUB_IMAGE_PATH = "develop/dummy/sub/oral_examination.png"

S3_HOME_VISIT_MEDICAL_TREATMENT_MAIN_IMAGE_PATH = (
    "develop/dummy/main/questionnaire_for_home_visit_medical_treatment.png"
)
S3_HOME_VISIT_MEDICAL_TREATMENT_SUB_IMAGE_PATH = (
    "develop/dummy/sub/questionnaire_for_home_visit_medical_treatment.png"
)

S3_ORTHODONTIC_MEDICAL_QUESTIONNAIRE_MAIN_IMAGE_PATH = (
    "develop/dummy/main/orthodontic_medical_questionnaire.png"
)
S3_ORTHODONTIC_MEDICAL_QUESTIONNAIRE_SUB_IMAGE_PATH = (
    "develop/dummy/sub/orthodontic_medical_questionnaire.png"
)

S3_HOME_CARE_INSTRUCTIONS_MAIN_IMAGE_PATH = (
    "develop/dummy/main/home_care_instructions.jpg"
)
S3_HOME_CARE_INSTRUCTIONS_SUB_IMAGE_PATH = (
    "develop/dummy/sub/home_care_instructions.jpg"
)

S3_PERIODONTAL_XAM_6_POINT_METHOD_MAIN_IMAGE_PATH = (
    "develop/dummy/main/periodontal_xam_6_point_method.png"
)
S3_PERIODONTAL_XAM_6_POINT_METHOD_SUB_IMAGE_PATH = (
    "develop/dummy/sub/periodontal_xam_6_point_method.png"
)

S3_ADULT_MEDICAL_QUESTIONNAIRE_MAIN_IMAGE_PATH = (
    "develop/dummy/main/adult_medical_questionnaire.png"
)
S3_ADULT_MEDICAL_QUESTIONNAIRE_SUB_IMAGE_PATH = (
    "develop/dummy/sub/adult_medical_questionnaire.png"
)

S3_TREATMENT_RECORD_MAIN_IMAGE_PATH = "develop/dummy/main/treatment_record.jpg"
S3_TREATMENT_RECORD_SUB_IMAGE_PATH = "develop/dummy/sub/treatment_record.jpg"

DOCUMENT_DEVICE_PROFILES = [
    {
        "name_medical_device": "Bite Force Analyzer",
        "key_name": "bite_force_analyzer",
        "document_data": {"1": S3_BFA_MAIN_IMAGE_PATH},
        "preview_document_data": {"1": S3_BFA_SUB_IMAGE_PATH},
        "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
    },
    {
        "name_medical_device": "Bite Eye",
        "key_name": "bite_eye",
        "document_data": {"1": S3_BTE_MAIN_IMAGE_PATH},
        "preview_document_data": {"1": S3_BTE_SUB_IMAGE_PATH},
        "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
    },
    {
        "name_medical_device": "Wearable Electromyograph",
        "key_name": "wearable_electromyograph",
        "document_data": {"1": S3_EMG_MAIN_IMAGE_PATH},
        "preview_document_data": {"1": S3_EMG_SUB_IMAGE_PATH},
        "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
    },
    {
        "name_medical_device": "Motion Trainer",
        "key_name": "motion_trainer",
        "document_data": {"1": S3_MA_MAIN_IMAGE_PATH},
        "preview_document_data": {"1": S3_MA_SUB_IMAGE_PATH},
        "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
    },
    {
        "name_medical_device": "Intraoral Camera",
        "key_name": "medical_device",
        "document_data": {
            str(i + 1): path for i, path in enumerate(S3_INTRAORAL_MAIN_PATHS)
        },
        "preview_document_data": {
            str(i + 1): path for i, path in enumerate(S3_INTRAORAL_SUB_PATHS)
        },
        "display_mode": DocumentDisplayMode.INTRAORAL_5_IMAGE.value,
    },
    {
        "name_medical_device": "Dental X-Ray",
        "key_name": "medical_device",
        "document_data": {
            str(i + 1): path for i, path in enumerate(S3_XRAY_MAIN_PATHS)
        },
        "preview_document_data": {
            str(i + 1): path for i, path in enumerate(S3_XRAY_SUB_PATHS)
        },
        "display_mode": DocumentDisplayMode.DENTAL_X_RAY_10_IMAGE.value,
    },
    {
        "name_medical_device": "Dual Vertical Camera",
        "key_name": "medical_device",
        "document_data": {
            str(i + 1): path for i, path in enumerate(S3_DUAL_VERT_MAIN_PATHS)
        },
        "preview_document_data": {
            str(i + 1): path for i, path in enumerate(S3_DUAL_VERT_SUB_PATHS)
        },
        "display_mode": DocumentDisplayMode.TWO_VERTICAL_IMAGE.value,
    },
    {
        "name_medical_device": "Dual Horizontal Camera",
        "key_name": "medical_device",
        "document_data": {
            str(i + 1): path for i, path in enumerate(S3_DUAL_HORI_MAIN_PATHS)
        },
        "preview_document_data": {
            str(i + 1): path for i, path in enumerate(S3_DUAL_HORI_SUB_PATHS)
        },
        "display_mode": DocumentDisplayMode.TWO_HORIZONTAL_IMAGE.value,
    },
    {
        "name_medical_device": "Teeth Images",
        "key_name": "medical_device",
        "document_data": {
            str(i + 1): path for i, path in enumerate(S3_LIST_MAIN_PATHS)
        },
        "preview_document_data": {
            str(i + 1): path for i, path in enumerate(S3_LIST_SUB_PATHS)
        },
        "display_mode": DocumentDisplayMode.LIST_IMAGE.value,
    },
    {
        "name_medical_device": "JMS Tongue Pressure Measuring Device",
        "key_name": "medical_device",
        "document_data": {"1": S3_JMS_DEVICE_MAIN_IMAGE_PATH},
        "preview_document_data": {"1": S3_JMS_DEVICE_SUB_IMAGE_PATH},
        "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
    },
    {
        "name_medical_device": "Glucosensor",
        "key_name": "medical_device",
        "document_data": {"1": S3_GLUCO_SENSOR_MAIN_IMAGE_PATH},
        "preview_document_data": {"1": S3_GLUCO_SENSOR_SUB_IMAGE_PATH},
        "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
    },
    {
        "name_medical_device": "Oral Examination",
        "key_name": "medical_device",
        "document_data": {"1": S3_ORAL_EXAMINATION_MAIN_IMAGE_PATH},
        "preview_document_data": {"1": S3_ORAL_EXAMINATION_SUB_IMAGE_PATH},
        "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
    },
    {
        "name_medical_device": "Home Visit Medical Treatment Questionnaire",
        "key_name": "medical_device",
        "document_data": {"1": S3_HOME_VISIT_MEDICAL_TREATMENT_MAIN_IMAGE_PATH},
        "preview_document_data": {"1": S3_HOME_VISIT_MEDICAL_TREATMENT_SUB_IMAGE_PATH},
        "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
    },
    {
        "name_medical_device": "Orthodontic Medical Questionnaire",
        "key_name": "medical_device",
        "document_data": {"1": S3_ORTHODONTIC_MEDICAL_QUESTIONNAIRE_MAIN_IMAGE_PATH},
        "preview_document_data": {
            "1": S3_ORTHODONTIC_MEDICAL_QUESTIONNAIRE_SUB_IMAGE_PATH
        },
        "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
    },
    {
        "name_medical_device": "Home Care Instructions",
        "key_name": "medical_device",
        "document_data": {"1": S3_HOME_CARE_INSTRUCTIONS_MAIN_IMAGE_PATH},
        "preview_document_data": {"1": S3_HOME_CARE_INSTRUCTIONS_SUB_IMAGE_PATH},
        "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
    },
    {
        "name_medical_device": "Periodontal Exam (6-Point Method)",
        "key_name": "medical_device",
        "document_data": {"1": S3_PERIODONTAL_XAM_6_POINT_METHOD_MAIN_IMAGE_PATH},
        "preview_document_data": {
            "1": S3_PERIODONTAL_XAM_6_POINT_METHOD_SUB_IMAGE_PATH
        },
        "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
    },
    {
        "name_medical_device": "Adult Medical Questionnaire",
        "key_name": "medical_device",
        "document_data": {"1": S3_ADULT_MEDICAL_QUESTIONNAIRE_MAIN_IMAGE_PATH},
        "preview_document_data": {"1": S3_ADULT_MEDICAL_QUESTIONNAIRE_SUB_IMAGE_PATH},
        "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
    },
    {
        "name_medical_device": "Treatment Record",
        "key_name": "medical_device",
        "document_data": {"1": S3_TREATMENT_RECORD_MAIN_IMAGE_PATH},
        "preview_document_data": {"1": S3_TREATMENT_RECORD_SUB_IMAGE_PATH},
        "display_mode": DocumentDisplayMode.SINGLE_IMAGE.value,
    },
]


async def seed_data(tenant_db_name, patient_user_ids: list[int], num_docs: int):
    log.info("Dummy data for db_name: %s", tenant_db_name)

    token = set_current_db_name(tenant_db_name)
    try:
        async with TenantDatabase.get_instance_tenant_db() as db_session:
            async with db_session:
                document_group_mapping = await get_document_group_mapping(db_session)

        tasks = [
            _create_documents(
                patient_user_id=patient_user_id,
                document_group_mapping=document_group_mapping,
                num_docs=num_docs,
            )
            for patient_user_id in patient_user_ids
        ]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        for idx, r in enumerate(results):
            if isinstance(r, Exception):
                log.error(
                    "❌  Task for patient_user_id=%s failed",
                    patient_user_ids[idx],
                    exc_info=r,
                )

        await asyncio.sleep(1)
        log.info("Concurrent seeding done: %s tasks", len(patient_user_ids))
    finally:
        reset_current_db_name(token)


async def get_document_group_mapping(db_session: AsyncSession) -> dict[str, int]:
    stmt = select(DocumentGroup.id, DocumentGroup.key_name)
    result = await db_session.execute(stmt)
    rows = result.all()
    mapping = {row.key_name: row.id for row in rows}
    """
        return = {
           "treatment_record":1,
           "medical_questionnaire":2,
           "periodontal_exam":3,
           "patient_provided_materials":4,
           "img_layout":5,
           "other":6,
           "medical_device":7,
           "bite_force_analyzer":8,
           "bite_eye":9,
           "wearable_electromyograph":10,
           "motion_trainer":11
        }
    """
    return mapping


async def _create_documents(patient_user_id, document_group_mapping, num_docs):
    log.info("Dummy data for patient_user_id: %s", patient_user_id)
    documents_data = generate_random_documents_data(
        num_docs=num_docs,
        patient_user_id=patient_user_id,
        document_group_mapping=document_group_mapping,
    )
    async with TenantDatabase.get_instance_tenant_db() as db_session:
        async with db_session.begin():
            add_all_documents(db_session, documents_data)


def add_all_documents(db_session, documents_data: list[dict]):
    document_objects = [DocumentManagement(**data) for data in documents_data]
    db_session.add_all(document_objects)


def generate_random_documents_data(
    num_docs: int,
    patient_user_id: int,
    document_group_mapping: dict[str, int],
    date_range_days: int = 15,
) -> list[dict]:
    today = date.today()
    docs = []
    remaining = num_docs - 2  # reserve 2 slots for PDF + XLSX

    while remaining > 0:
        # Pick a random day within the date range
        exam_delta = random.randint(0, max(0, date_range_days))
        examination_date = today - timedelta(days=exam_delta)

        # Random number of docs for this day (cannot exceed remaining)
        docs_in_day = random.randint(
            1, min(10, remaining, len(DOCUMENT_DEVICE_PROFILES))
        )

        # Pick unique document types for this day
        chosen_docs = random.sample(DOCUMENT_DEVICE_PROFILES, docs_in_day)

        for doc_profile in chosen_docs:
            docs.append(
                {
                    "patient_user_id": patient_user_id,
                    "name": doc_profile["name_medical_device"],
                    "status": DocumentStatus.ACTIVATED.value,
                    "document_group_id": document_group_mapping[
                        doc_profile["key_name"]
                    ],
                    "data_type": DocumentDataType.ORIGINAL.value,
                    "document_data": doc_profile["document_data"],
                    "preview_document_data": doc_profile["preview_document_data"],
                    "examination_date": examination_date,
                    "medical_history_id": None,
                    "display_mode": doc_profile["display_mode"],
                    "document_uuid": str(uuid4()),
                    "version_id": 1,
                    "is_latest": True,
                    "extra_data": {"type": f"Dummy data #{len(docs) + 1}"},
                    "s3_status": DocumentS3Status.AVAILABLE.value,
                    "document_extension": DocumentExtension.IMAGE.value,
                }
            )
            remaining -= 1
            if remaining == 0:
                break

    another_document_extension = [
        {
            "patient_user_id": patient_user_id,
            "name": "PDF File",
            "status": DocumentStatus.ACTIVATED.value,
            "document_group_id": document_group_mapping["other"],
            "data_type": DocumentDataType.ORIGINAL.value,
            "document_data": {"1": S3_FILE_PDF},
            "preview_document_data": None,
            "examination_date": today,
            "medical_history_id": None,
            "display_mode": DocumentDisplayMode.OTHER.value,
            "document_uuid": str(uuid4()),
            "version_id": 1,
            "is_latest": True,
            "extra_data": {"type": f"Dummy data #{num_docs - 1}"},
            "s3_status": DocumentS3Status.AVAILABLE.value,
            "document_extension": DocumentExtension.PDF.value,
        },
        {
            "patient_user_id": patient_user_id,
            "name": "XLSX file",
            "status": DocumentStatus.ACTIVATED.value,
            "document_group_id": document_group_mapping["other"],
            "data_type": DocumentDataType.ORIGINAL.value,
            "document_data": {"1": S3_FILE_XLSX},
            "preview_document_data": None,
            "examination_date": today,
            "medical_history_id": None,
            "display_mode": DocumentDisplayMode.OTHER.value,
            "document_uuid": str(uuid4()),
            "version_id": 1,
            "is_latest": True,
            "extra_data": {"type": f"Dummy data #{num_docs}"},
            "s3_status": DocumentS3Status.AVAILABLE.value,
            "document_extension": DocumentExtension.XLSX.value,
        },
    ]

    return docs + another_document_extension


def parse_patient_ids(raw: str) -> list[int] | None:
    """
    Parse a string like: "1,2,5,10" -> [1, 2, 5, 10]
    Only comma is accepted as delimiter.
    """
    try:
        ids = [int(t.strip()) for t in raw.split(",") if t.strip()]
        return list(set(ids))
    except Exception as e:
        log.error("❌ No valid patient_user_ids provided.: ", str(e))
        return None


if __name__ == "__main__":
    check_db_configuration()

    # 3) Ask for tenant DB name
    tenant_db_name = input(
        "👉 Enter the tenant tenant_tenant_db_name to seed: "
    ).strip()
    if not tenant_db_name:
        log.error("❌ Tenant tenant_db_name cannot be empty.")
        raise SystemExit(1)

    # 4) Ask for patient_user_id
    raw_ids = input("👉 Enter patient_user_id(s) (e.g. '1,2,3'): ")
    patient_user_ids = parse_patient_ids(raw_ids)
    if not patient_user_ids:
        raise SystemExit(1)

    # 5) Number Document each Patient
    num_docs_per_patient = prompt_int(
        "👉 Enter number of documents to create for each patient: "
    )

    # 6) Run the async seeding
    asyncio.run(seed_data(tenant_db_name, patient_user_ids, num_docs_per_patient))
