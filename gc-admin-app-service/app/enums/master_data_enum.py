from enums.base import StrEnum

from gc_dentist_shared.central_models import MasterPlan, MasterPricingStorage


class MasterModelEnum(StrEnum):
    """Enumeration for master model names."""

    M_PLANS = "m_plans"
    M_PRICING_STORAGES = "m_pricing_storages"


class MappingModel:
    MASTER_MODEL_MAPPING = {  # noqa: RUF012
        MasterModelEnum.M_PLANS.value: MasterPlan,
        MasterModelEnum.M_PRICING_STORAGES.value: MasterPricingStorage,
    }

    def get_model_fields(self, model_name: MasterModelEnum):
        """Get the model class based on the model name."""
        model = self.MASTER_MODEL_MAPPING.get(model_name.value, None)
        if model:
            return model.__table__.c
        return None

    def get_pk_column_model(self, model_name: MasterModelEnum):
        """Get the primary key column of the model."""
        model = self.MASTER_MODEL_MAPPING.get(model_name.value, None)
        if model:
            return next(iter(model.__table__.primary_key.columns.values()), None)
        return None
