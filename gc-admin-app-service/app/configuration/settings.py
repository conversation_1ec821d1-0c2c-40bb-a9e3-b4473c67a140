from pathlib import Path
from typing import ClassVar, Literal

from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    BASE_DIR: ClassVar[Path] = Path(__file__).resolve().parent

    model_config = SettingsConfigDict(
        env_file=str(BASE_DIR / ".env"),
        env_file_encoding="utf-8",
        extra="ignore",
    )

    PROJECT_NAME: str
    ENVIRONMENT: Literal["unittest", "develop", "testing", "staging", "production"]
    BACKEND_CORS_ORIGINS: list[str] = []
    BACKEND_CORS_METHODS: list[str] = [
        "GET",
        "POST",
        "PUT",
        "OPTIONS",
        "PATCH",
        "DELETE",
    ]

    POSTGRES_SERVER: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_GLOBAL_DB_NAME: str
    POSTGRES_PORT: int
    DB_ECHO: bool = False
    DB_INIT: bool = False

    READ_ONLY_POSTGRES_SERVER: str
    READ_ONLY_POSTGRES_USER: str
    READ_ONLY_POSTGRES_PASSWORD: str
    READ_ONLY_POSTGRES_GLOBAL_DB_NAME: str
    READ_ONLY_POSTGRES_PORT: int

    COMMUNICATE_SECRET_KEY: str

    # Redis configuration
    REDIS_HOST: str
    REDIS_PORT: int
    REDIS_PASSWORD: str | None = None
    REDIS_DATABASE: int
    REDIS_TIMEOUT: int
    REDIS_SSL: bool = False

    TOKEN_REFRESH_REDIS_PREFIX: str | None = None

    # Firebase configuration
    FIREBASE_ENABLED: bool = False
    FIREBASE_CERT_PATH: str | None = None
    FIREBASE_DRY_RUN: bool = True

    AES_SECRET_ID_ROTATION: str
    AWS_SECRET_ROTATION_KEY_MAPPING: dict = {}
    AWS_SECRET_CURRENT_VERSION: str
    AES_SECRET_KEY_MAPPING: str

    AWS_REGION_NAME: str | None = None
    AWS_ACCESS_KEY_ID: str | None = None
    AWS_SECRET_ACCESS_KEY: str | None = None

    # S3 configuration
    S3_BUCKET_NAME: str | None = None
    S3_FOLDER_NAME: str | None = None
    EXPIRED_GENERATE_PRESIGNED_URL: int = 60 * 60 * 1  # Expiration time, unit: seconds

    SES_REGION_NAME: str | None = "ap-northeast-1"
    SES_FROM_MAIL: str | None = None

    DENTIST_ENPOINT_API: str | None = None

    POSTGERS_TENANT_TEMPLATE_DB_NAME: str = "tenant_template"

    # Twilio configuration
    TWILIO_ACCOUNT_SID: str | None = None
    TWILIO_AUTH_TOKEN: str | None = None
    TWILIO_SERVICE_SID: str | None = None
    TWILIO_MESSAGE_SERVICE_SID: str | None = None

    # Auth configuration
    AUTH_SERVICE_JWKS_URL: str = "http://127.0.0.1:8000/.well-known/jwks.json"
    AUTH_SERVICE_JWKS_LIFESPAN: int = 300  # in seconds
    JWT_ALGORITHM: str = "HS256"

    TOKEN_EXCLUDE_URLS: list[str] = [
        "/docs",
        "/openapi.json",
        "/health",
    ]

    # Api key
    LAMBDA_SECRET_KEY: str | None = None


def get_settings() -> Settings:
    """Read configuration optimization writing method"""
    return Settings()


configuration = get_settings()
