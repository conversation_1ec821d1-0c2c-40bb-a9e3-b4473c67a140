from typing import Optional

from db.db_connection import CentralDatabase
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import OAuth2Token, SystemUser, TenantClinic
from gc_dentist_shared.core.logger.config import log


class JwtAuthService:
    """
    Service for handling JWT authentication.
    """

    async def get_db_name_by_tenant_uuid(
        self, central_session: AsyncSession, tenant_uuid: str
    ):
        """
        Get the database name associated with a tenant UUID.
        """
        query = select(TenantClinic.db_name).where(
            TenantClinic.tenant_uuid == tenant_uuid
        )
        result = await central_session.execute(query)
        db_name = result.scalar_one_or_none()

        if not db_name:
            log.error(f"❌ Database name not found for tenant UUID: {tenant_uuid}")
            return None
        return db_name

    async def validate_token(
        self,
        central_session: AsyncSession,
        access_token: str,
        user_id: int,
    ):
        """
        Validate the JWT token and return the decoded claims.
        """
        try:
            conditions = [
                OAuth2Token.access_token == access_token,
                OAuth2Token.system_user_id == user_id,
            ]
            query = select(OAuth2Token.token_uuid).where(*conditions)
            result = await central_session.execute(query)
            token_uuid = result.scalar_one_or_none()

            if not token_uuid:
                log.error("❌ JWT token not found in the database")
                return None
            return token_uuid
        except Exception as e:
            log.error(f"❌ Error validating JWT token: {e}")
            return None

    async def validate_auth(
        self,
        access_token: str,
        user_id: int,
        role_id: Optional[int] = None,
    ):
        """
        Validate the role ID against the tenant UUID.
        """
        try:
            async with CentralDatabase().get_instance_db() as central_session:
                async with central_session.begin():
                    token_uuid = await self.validate_token(
                        central_session, access_token, user_id
                    )
                    if not token_uuid:
                        return None

                stmt = select(SystemUser).where(
                    SystemUser.id == user_id, SystemUser.is_active.is_(True)
                )
                result = await central_session.execute(stmt)
                system_user = result.scalar_one_or_none()
                if not system_user:
                    log.error("❌ System user not found or inactive")
                    return None

                return system_user

        except Exception as e:
            log.error(f"❌ Error validating role ID: {e}")
            return None
