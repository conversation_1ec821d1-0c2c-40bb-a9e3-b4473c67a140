from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from core.messages import CustomMessageCode
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from db.db_connection import TenantDatabase
from sqlalchemy import func, select, update
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import (
    MasterPricingStorage,
    TenantClinic,
    TenantExtraStorage,
)
from gc_dentist_shared.core.common.redis import RedisCli
from gc_dentist_shared.core.constants import StorageRedis
from gc_dentist_shared.core.enums.pricing_enum import TenantExtraStorageStatus
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import TenantConfiguration


class PricingLambdaService:
    def __init__(self, central_db_session: AsyncSession, redis_cli: RedisCli = None):
        self.central_db_session = central_db_session
        self.redis_cli = redis_cli

    # region Private Common Methods

    # endregion

    # region Lambda clean up pricing storage expired
    async def process_expired_extra_storage(self):
        log.info(" START Clean up pricing storage expired")

        # Step 1 Get all tenant extra storage expired (read-only, no transaction needed)
        tenant_extra_storages = await self._get_tenant_extra_storage_expired(
            self.central_db_session
        )

        if not tenant_extra_storages:
            log.info("No tenant extra storage expired")
            log.info(" END Clean up pricing storage expired")
            return

        # Validate data before processing
        validated_storages = self._validate_expired_storages(tenant_extra_storages)
        if not validated_storages:
            log.warning("No valid expired storages to process")
            log.info(" END Clean up pricing storage expired")
            return

        # Process each tenant with proper error handling
        successful_tenants = []
        failed_tenants = []

        for tenant_data in validated_storages:
            try:
                success = await self._process_single_tenant_expiry(tenant_data)
                if success:
                    successful_tenants.append(tenant_data["tenant_uuid"])
                else:
                    failed_tenants.append(tenant_data["tenant_uuid"])
            except Exception as e:
                log.error(f"❌ Failed to process tenant {tenant_data.get('tenant_uuid')}: {e}")
                failed_tenants.append(tenant_data["tenant_uuid"])

        # Step 2: Update status to expired only for successfully processed tenants
        if successful_tenants:
            successful_storage_ids = [
                storage_id
                for tenant in validated_storages
                if tenant["tenant_uuid"] in successful_tenants
                for storage_id in tenant["ids"]
            ]

            async with self.central_db_session.begin():
                await self._update_tenant_extra_storage_status(
                    self.central_db_session,
                    successful_storage_ids,
                    TenantExtraStorageStatus.EXPIRED.status_id,
                )

        log.info(f"✅ Successfully processed {len(successful_tenants)} tenants")
        if failed_tenants:
            log.warning(f"⚠️ Failed to process {len(failed_tenants)} tenants: {failed_tenants}")

        log.info(" END Clean up pricing storage expired")

    async def _get_tenant_extra_storage_expired(self, db_session: AsyncSession):
        query = (
            select(
                TenantExtraStorage.tenant_uuid,
                TenantClinic.db_name,
                func.sum(MasterPricingStorage.storage).label("total_storage"),
                func.array_agg(TenantExtraStorage.id).label("ids"),
            )
            .join(
                MasterPricingStorage,
                MasterPricingStorage.storage_key_id
                == TenantExtraStorage.storage_key_id,
                # Changed to INNER JOIN to ensure we have valid storage data
            )
            .join(
                TenantClinic,
                TenantClinic.tenant_uuid == TenantExtraStorage.tenant_uuid,
                # Changed to INNER JOIN to ensure we have valid tenant data
            )
            .where(
                TenantExtraStorage.expired_at < func.now(),
                TenantExtraStorage.status == TenantExtraStorageStatus.ACTIVE.status_id,
                MasterPricingStorage.storage.is_not(None),  # Ensure storage is not null
                MasterPricingStorage.storage > 0,  # Ensure positive storage value
            )
            .group_by(TenantExtraStorage.tenant_uuid, TenantClinic.db_name)
        )
        result = await db_session.execute(query)
        return result.mappings().all()

    def _validate_expired_storages(self, tenant_extra_storages):
        """Validate expired storage data before processing"""
        validated = []
        for tenant_data in tenant_extra_storages:
            if (tenant_data.get("total_storage") and
                tenant_data.get("total_storage") > 0 and
                tenant_data.get("tenant_uuid") and
                tenant_data.get("db_name") and
                tenant_data.get("ids")):
                validated.append(tenant_data)
            else:
                log.warning(f"⚠️ Invalid storage data for tenant {tenant_data.get('tenant_uuid')}")
        return validated

    async def _process_single_tenant_expiry(self, tenant_data) -> bool:
        """Process expiry for a single tenant with proper transaction management"""
        token = set_current_db_name(tenant_data["db_name"])
        try:
            async with TenantDatabase.get_instance_tenant_db() as tenant_db_session:
                async with tenant_db_session.begin():
                    tenant_config = await self._update_total_extra_storage_of_tenant(
                        tenant_db_session, tenant_data
                    )

                    if not tenant_config:
                        log.error(f"❌ Tenant config not found for tenant {tenant_data['tenant_uuid']}")
                        return False

                    # Update Redis only after successful DB update
                    updated_storage_in_redis = await self._update_total_extra_storage_to_redis(
                        tenant_data, tenant_config
                    )
                    if not updated_storage_in_redis:
                        log.warning(f"⚠️ Failed to update Redis for tenant {tenant_data['tenant_uuid']}")
                        # Don't fail the entire operation for Redis issues
                        # Redis will be eventually consistent

                    log.info(f"✅ Successfully processed tenant {tenant_data['tenant_uuid']}")
                    return True

        except Exception as e:
            log.error(f"❌ Error processing tenant {tenant_data['tenant_uuid']}: {e}")
            return False
        finally:
            reset_current_db_name(token)

    async def _update_tenant_extra_storage_status(
        self, db_session: AsyncSession, storage_ids: list, status: int
    ):
        """Update storage status for given storage IDs"""
        if not storage_ids:
            log.warning("No storage IDs provided for status update")
            return

        query = (
            update(TenantExtraStorage)
            .where(TenantExtraStorage.id.in_(storage_ids))
            .values(status=status)
        )
        result = await db_session.execute(query)
        log.info(f"Updated {result.rowcount} storage records to status {status}")

    # This method is now replaced by _process_single_tenant_expiry
    # Keeping for backward compatibility but marked as deprecated
    async def _sync_total_pricing_storage_expired_to_tenant(
        self, tenant_extra_storages
    ):
        """
        DEPRECATED: Use _process_single_tenant_expiry instead
        This method has transaction management issues
        """
        log.warning("⚠️ Using deprecated method _sync_total_pricing_storage_expired_to_tenant")
        log.info(" START Sync total extra storage expired to tenant")

        for tenant in tenant_extra_storages:
            success = await self._process_single_tenant_expiry(tenant)
            if not success:
                log.error(f"❌ Failed to sync tenant {tenant.get('tenant_uuid')}")

        log.info(" END Sync total extra storage expired to tenant")

    async def _update_total_extra_storage_of_tenant(
        self, db_session: AsyncSession, tenant_data
    ):
        """Update tenant configuration by reducing extra storage"""
        tenant_uuid = tenant_data.get("tenant_uuid") if isinstance(tenant_data, dict) else tenant_data.tenant_uuid
        total_storage = tenant_data.get("total_storage") if isinstance(tenant_data, dict) else tenant_data.total_storage

        query = select(TenantConfiguration).where(
            TenantConfiguration.tenant_uuid == tenant_uuid
        )
        result = await db_session.execute(query)
        tenant_config = result.scalar_one_or_none()

        if not tenant_config:
            log.error(f"❌ Tenant config not found for tenant {tenant_uuid}")
            return None

        # Validate storage amounts
        current_extra = tenant_config.extra_storage or 0
        if current_extra < total_storage:
            log.error(
                f"❌ Tenant {tenant_uuid} extra storage ({current_extra}) < expired amount ({total_storage})"
            )
            return None

        # Update storage amount
        tenant_config.extra_storage = current_extra - total_storage

        # Note: Removed manual commit as it's handled by the transaction context
        log.info(f"✅ Updated tenant {tenant_uuid} extra storage: {current_extra} -> {tenant_config.extra_storage}")
        return tenant_config

    async def _update_total_extra_storage_to_redis(
        self, tenant_data, tenant_config: TenantConfiguration
    ) -> bool:
        """Update Redis storage limit with proper validation"""
        if not self.redis_cli:
            log.error("❌ Redis client is not available")
            return False

        tenant_uuid = tenant_data.get("tenant_uuid") if isinstance(tenant_data, dict) else tenant_data.tenant_uuid
        total_storage = tenant_data.get("total_storage") if isinstance(tenant_data, dict) else tenant_data.total_storage

        if not tenant_uuid:
            log.error("❌ Tenant UUID is missing from tenant data")
            return False

        redis_key = StorageRedis.STORAGE_LIMIT.value % tenant_uuid

        try:
            current_storage_result = await self.redis_cli.get(redis_key)

            return (
                await self._update_existing_storage(
                    redis_key, tenant_uuid, current_storage_result, total_storage
                )
                if current_storage_result
                else await self._set_new_storage(
                    redis_key, tenant_uuid, tenant_config
                )
            )
        except Exception as e:
            log.error(f"❌ Redis operation failed for tenant {tenant_uuid}: {e}")
            return False

    async def _update_existing_storage(
        self,
        redis_key: str,
        tenant_uuid: str,
        current_storage_result: str,
        expired_storage_amount: int,
    ) -> bool:
        """Update existing Redis storage with atomic operation"""
        try:
            current_storage = int(current_storage_result)
        except (ValueError, TypeError):
            log.error(f"❌ Invalid storage value in Redis for tenant {tenant_uuid}: {current_storage_result}")
            return False

        if current_storage < expired_storage_amount:
            log.error(
                f"❌ Current storage ({current_storage}) < expired amount ({expired_storage_amount}) "
                f"for tenant {tenant_uuid}. Data inconsistency detected."
            )
            return False

        try:
            # Use atomic DECRBY operation to prevent race conditions
            final_value = await self.redis_cli.decrby(redis_key, expired_storage_amount)

            if final_value is not None and final_value >= 0:
                log.info(
                    f"✅ Updated storage for tenant {tenant_uuid}: "
                    f"{current_storage} - {expired_storage_amount} = {final_value}"
                )
                return True
            else:
                log.error(f"❌ Redis DECRBY resulted in negative value for tenant {tenant_uuid}")
                # Rollback the operation
                await self.redis_cli.incrby(redis_key, expired_storage_amount)
                return False

        except Exception as e:
            log.error(f"❌ Redis DECRBY operation failed for tenant {tenant_uuid}: {e}")
            return False

    async def _set_new_storage(
        self, redis_key: str, tenant_uuid: str, tenant_config: TenantConfiguration
    ) -> bool:
        """Set new storage limit in Redis"""
        try:
            extra_storage = tenant_config.extra_storage or 0
            default_storage = tenant_config.default_storage or 0
            initial_storage_amount = extra_storage + default_storage

            is_success = await self.redis_cli.set(redis_key, initial_storage_amount)

            if is_success:
                log.info(
                    f"✅ Set initial storage for tenant {tenant_uuid}: {initial_storage_amount}"
                )
                return True
            else:
                log.error(f"❌ Redis set operation failed for tenant {tenant_uuid}")
                return False

        except Exception as e:
            log.error(f"❌ Failed to set Redis storage for tenant {tenant_uuid}: {e}")
            return False

    # endregion
