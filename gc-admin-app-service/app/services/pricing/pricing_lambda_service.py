from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from core.messages import CustomMessageCode
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from db.db_connection import TenantDatabase
from sqlalchemy import func, select, update
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import (
    MasterPricingStorage,
    TenantClinic,
    TenantExtraStorage,
)
from gc_dentist_shared.core.common.redis import RedisCli
from gc_dentist_shared.core.constants import StorageRedis
from gc_dentist_shared.core.enums.pricing_enum import TenantExtraStorageStatus
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import TenantConfiguration


class PricingLambdaService:
    def __init__(self, central_db_session: AsyncSession, redis_cli: RedisCli = None):
        self.central_db_session = central_db_session
        self.redis_cli = redis_cli

    # region Private Common Methods

    # endregion

    # region Lambda clean up pricing storage expired
    async def process_expired_extra_storage(self):
        log.info(" START Clean up pricing storage expired")

        async with self.central_db_session.begin():
            # Step 1 Get all tenant extra storage expired
            tenant_extra_storages = await self._get_tenant_extra_storage_expired(
                self.central_db_session
            )

            if not tenant_extra_storages:
                log.info("No tenant extra storage expired")

                log.info(" END Clean up pricing storage expired")
                return

            # Step 2 Update status to expired
            tenant_extra_storage_ids = [
                storage_id
                for tenant in tenant_extra_storages
                for storage_id in tenant["ids"]
            ]
            await self._update_tenant_extra_storage_status(
                self.central_db_session,
                tenant_extra_storage_ids,
                TenantExtraStorageStatus.EXPIRED.status_id,
            )

            # Step 3 Update total extra storage of tenant
            await self._sync_total_pricing_storage_expired_to_tenant(
                tenant_extra_storages
            )

        log.info(" END Clean up pricing storage expired")

    async def _get_tenant_extra_storage_expired(self, db_session: AsyncSession):
        query = (
            select(
                TenantExtraStorage.tenant_uuid,
                TenantClinic.db_name,
                func.sum(MasterPricingStorage.storage).label("total_storage"),
                func.array_agg(TenantExtraStorage.id).label("ids"),
            )
            .join(
                MasterPricingStorage,
                MasterPricingStorage.storage_key_id
                == TenantExtraStorage.storage_key_id,
                isouter=True,
            )
            .join(
                TenantClinic,
                TenantClinic.tenant_uuid == TenantExtraStorage.tenant_uuid,
                isouter=True,
            )
            .where(
                TenantExtraStorage.expired_at < func.now(),
                TenantExtraStorage.status == TenantExtraStorageStatus.ACTIVE.status_id,
            )
            .group_by(TenantExtraStorage.tenant_uuid, TenantClinic.db_name)
        )
        result = await db_session.execute(query)
        return result.mappings().all()

    async def _update_tenant_extra_storage_status(
        self, db_session: AsyncSession, tenant_extra_storages: list, status: int
    ):
        query = (
            update(TenantExtraStorage)
            .where(TenantExtraStorage.id.in_(tenant_extra_storages))
            .values(status=status)
        )
        await db_session.execute(query)

    async def _sync_total_pricing_storage_expired_to_tenant(
        self, tenant_extra_storages
    ):
        log.info(" START Sync total extra storage expired to tenant")

        for tenant in tenant_extra_storages:
            log.info(
                f" START Sync total extra storage expired to tenant {tenant.tenant_uuid}"
            )
            token = set_current_db_name(tenant.db_name)
            try:
                async with TenantDatabase.get_instance_tenant_db() as tenant_db_session:
                    async with tenant_db_session.begin():
                        tenant_config = (
                            await self._update_total_extra_storage_of_tenant(
                                tenant_db_session, tenant
                            )
                        )

                        if not tenant_config:
                            raise CustomValueError(
                                message=CustomMessageCode.TENANT_NOT_FOUND.title,
                                message_code=CustomMessageCode.TENANT_NOT_FOUND.code,
                            )

                        updated_storage_in_redis = await self._update_total_extra_storage_to_redis(
                            tenant, tenant_config
                        )
                        if not updated_storage_in_redis:
                            log.warning(
                                f"⚠️ Failed to update total extra storage to redis for tenant {tenant.tenant_uuid}"
                            )

                        log.info(
                            f"✅ Sync total extra storage expired to tenant {tenant.tenant_uuid} successfully"
                        )

            except Exception as e:
                log.error(f"❌ Error setting current database name: {e}")
                continue
            finally:
                log.info(
                    f" END Sync total extra storage expired to tenant {tenant.tenant_uuid}"
                )
                reset_current_db_name(token)


        log.info(" END Sync total extra storage expired to tenant")

    async def _update_total_extra_storage_of_tenant(
        self, db_session: AsyncSession, tenant
    ) -> bool:
        query = select(TenantConfiguration).where(
            TenantConfiguration.tenant_uuid == tenant.tenant_uuid
        )
        result = await db_session.execute(query)
        tenant_config = result.scalar_one_or_none()
        if not tenant_config:
            log.error(f"❌ Tenant config not found for tenant {tenant.tenant_uuid}")
            return False

        if tenant_config.extra_storage < tenant.total_storage:
            log.error(
                f"❌ Tenant config extra storage is less than total extra storage for tenant {tenant.tenant_uuid}"
            )
            return False

        tenant_config.extra_storage -= tenant.total_storage
        await db_session.commit()
        return tenant_config

    async def _update_total_extra_storage_to_redis(
        self, tenant, tenant_config: TenantConfiguration
    ) -> bool:
        tenant_uuid = tenant.get("tenant_uuid")
        if not tenant_uuid:
            log.error("❌ Tenant UUID is missing from tenant data")
            return False

        redis_key = StorageRedis.STORAGE_LIMIT.value % tenant_uuid
        current_storage_result = await self.redis_cli.get(redis_key)

        return (
            await self._update_existing_storage(
                redis_key, tenant_uuid, current_storage_result, tenant.total_storage
            )
            if current_storage_result
            else await self._set_new_storage(
                redis_key, tenant_uuid, tenant_config
            )
        )

    async def _update_existing_storage(
        self,
        redis_key: str,
        tenant_uuid: str,
        current_storage_result: str,
        expired_storage_amount: int,
    ) -> bool:
        current_storage = int(current_storage_result)

        if current_storage < expired_storage_amount:
            log.error(
                f"❌ Current storage ({current_storage}) is less than expired amount ({expired_storage_amount}) "
                f"for tenant {tenant_uuid}. Possible data inconsistency."
            )
            return False

        final_value = await self.redis_cli.decrby(redis_key, expired_storage_amount)

        if final_value:
            log.info(
                f"✅ Processed expired storage for tenant {tenant_uuid}: "
                f"{current_storage} - {expired_storage_amount} = {final_value}"
            )
            return True
        else:
            log.error(f"❌ Redis DECRBY operation failed for tenant {tenant_uuid}")
            return False

    async def _set_new_storage(
        self, redis_key: str, tenant_uuid: str, tenant_config: TenantConfiguration
    ):
        initial_storage_amount = tenant_config.extra_storage + tenant_config.default_storage
        is_success = await self.redis_cli.set(redis_key, initial_storage_amount)

        if is_success:
            log.info(
                f"✅ Set initial storage for tenant {tenant_uuid}: {initial_storage_amount}"
            )
        else:
            log.error(f"❌ Redis set operation failed for new tenant {tenant_uuid}")

    # endregion
