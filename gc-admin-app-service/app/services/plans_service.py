from datetime import datetime, timezone

from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from core.messages import CustomMessageCode
from core.permission.sync_permissions import sync_permissions
from core.permission.sync_role_permissions import sync_roles_permissions
from db.db_connection import TenantDatabase
from schemas.requests.pricing import UpdateTenantConfigurationRequest
from sqlalchemy import select
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import MasterPlan, TenantClinic, TenantPlan
from gc_dentist_shared.core.common.redis import RedisCli
from gc_dentist_shared.core.constants import StorageRedis
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.enums.plan_enum import TenantPlanStatus
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import ClinicConfiguration


class PlansService:
    def __init__(self, central_db_session: AsyncSession, redis_cli: RedisCli = None):
        self.central_db_session = central_db_session
        self.redis_cli = redis_cli

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="update_plan_storage",
    )
    async def update_plan_storage(self, plan_key_id: int, tenant_uuid: str):
        log.info("START update plan storage")
        async with self.central_db_session.begin():
            current_tenant_plan = await self._get_current_tenant_plan(
                self.central_db_session, tenant_uuid
            )

            await self._validate_current_plan(current_tenant_plan, plan_key_id)

            master_plan = await self._validate_and_get_plan_by_key_id(
                self.central_db_session, plan_key_id
            )
            tenant = await self._get_tenant_by_uuid(
                self.central_db_session, tenant_uuid
            )

            # Deactivate current plan
            # TODO(TuanTC) This is temporary solution, need to confirm later
            await self._deactive_current_tenant_plan(
                self.central_db_session, current_tenant_plan
            )

            # Activate a new plan
            new_tenant_plan = await self._activate_tenant_plan(
                self.central_db_session, plan_key_id, tenant_uuid
            )

            storage_data = UpdateTenantConfigurationRequest(
                default_storage=master_plan.default_storage,
                plan_key_id=plan_key_id,
            )

            # Sync plan data to tenant_configuration(TenantDB)
            await self._sync_storage_to_tenant_database(
                tenant.db_name,
                tenant.tenant_uuid,
                storage_data,
            )

        log.info("✅ Update plan storage successfully")
        log.info(" END update plan storage")
        return new_tenant_plan

    # region Private Methods

    async def _get_current_tenant_plan(
        self, db_session: AsyncSession, tenant_uuid: str
    ):
        query = select(TenantPlan).where(
            TenantPlan.tenant_uuid == tenant_uuid,
            TenantPlan.status == TenantPlanStatus.ACTIVATE.value,
        )
        result = await db_session.execute(query)
        tenant_plan = result.scalar_one_or_none()
        return tenant_plan

    async def _validate_current_plan(self, current_tenant_plan, plan_key_id):
        if not current_tenant_plan:
            raise CustomValueError(
                message=CustomMessageCode.PRICING_TENANT_PLAN_NOT_FOUND.title,
                message_code=CustomMessageCode.PRICING_TENANT_PLAN_NOT_FOUND.code,
            )
        if current_tenant_plan.plan_key_id == plan_key_id:
            raise CustomValueError(
                message=CustomMessageCode.PRICING_PLAN_NOT_CHANGED.title,
                message_code=CustomMessageCode.PRICING_PLAN_NOT_CHANGED.code,
            )

    async def _validate_and_get_plan_by_key_id(
        self, db_session: AsyncSession, plan_key_id: int
    ):
        query = select(MasterPlan).where(MasterPlan.plan_key_id == plan_key_id)
        result = await db_session.execute(query)
        plan = result.scalar_one_or_none()
        if not plan:
            raise CustomValueError(
                message=CustomMessageCode.PRICING_PLAN_NOT_FOUND.title,
                message_code=CustomMessageCode.PRICING_PLAN_NOT_FOUND.code,
            )
        return plan

    async def _get_tenant_by_uuid(
        self, db_session: AsyncSession, tenant_uuid: str
    ) -> TenantClinic:
        query = select(TenantClinic).where(TenantClinic.tenant_uuid == tenant_uuid)
        result = await db_session.execute(query)
        tenant = result.scalar_one_or_none()

        if not tenant:
            raise CustomValueError(
                message=CustomMessageCode.TENANT_NOT_FOUND.title,
                message_code=CustomMessageCode.TENANT_NOT_FOUND.code,
            )

        return tenant

    async def _deactive_current_tenant_plan(
        self, central_db_session: AsyncSession, current_tenant_plan
    ):
        current_tenant_plan.status = TenantPlanStatus.DEACTIVATE.value
        await central_db_session.flush()
        await central_db_session.refresh(current_tenant_plan)

    async def _activate_tenant_plan(
        self, central_db_session: AsyncSession, plan_key_id, tenant_uuid
    ):
        existing_plan = await self._find_existing_tenant_plan(
            central_db_session, tenant_uuid, plan_key_id
        )

        if existing_plan:
            return await self._reactivate_existing_plan(
                central_db_session, existing_plan
            )
        else:
            return await self._create_new_tenant_plan(
                central_db_session, plan_key_id, tenant_uuid
            )

    async def _find_existing_tenant_plan(
        self, central_db_session: AsyncSession, tenant_uuid: str, plan_key_id: int
    ):
        query = select(TenantPlan).where(
            TenantPlan.tenant_uuid == tenant_uuid,
            TenantPlan.plan_key_id == plan_key_id,
        )
        result = await central_db_session.execute(query)
        return result.scalar_one_or_none()

    async def _reactivate_existing_plan(
        self, central_db_session: AsyncSession, tenant_plan: TenantPlan
    ) -> TenantPlan:
        tenant_plan.status = TenantPlanStatus.ACTIVATE.value
        await central_db_session.flush()
        await central_db_session.refresh(tenant_plan)
        return tenant_plan

    async def _create_new_tenant_plan(
        self, central_db_session: AsyncSession, plan_key_id: int, tenant_uuid: str
    ) -> TenantPlan:
        new_tenant_plan = TenantPlan(
            tenant_uuid=tenant_uuid,
            plan_key_id=plan_key_id,
            status=TenantPlanStatus.ACTIVATE.value,
            start_at=datetime.now(timezone.utc),
        )
        central_db_session.add(new_tenant_plan)
        await central_db_session.flush()
        await central_db_session.refresh(new_tenant_plan)
        return new_tenant_plan

    async def _sync_storage_to_tenant_database(
        self,
        tenant_db_name: str,
        tenant_uuid: str,
        storage_data: UpdateTenantConfigurationRequest,
    ):
        token = set_current_db_name(tenant_db_name)
        try:
            log.info(f"START sync extra storage to tenant: {tenant_uuid}")
            async with TenantDatabase.get_instance_tenant_db() as tenant_db_session:
                async with tenant_db_session.begin():
                    tenant_config = await self._update_tenant_configuration(
                        tenant_db_session, tenant_uuid, storage_data
                    )

                    # Sync permissions to tenant
                    await sync_permissions(tenant_db_session, tenant_config.plan_key_id)
                    log.info(
                        f"✅ Successfully synced permissions to tenant: {tenant_uuid}"
                    )
                    await sync_roles_permissions(tenant_db_session)
                    log.info(
                        f"✅ Successfully synced roles_permissions to tenant: {tenant_uuid}"
                    )

            # Update storage limit in Redis
            total_storage = self._calculate_total_storage(tenant_config)
            await self._update_storage_limit_in_redis(
                self.redis_cli, tenant_uuid, total_storage
            )

            # Clear permission cache of the old plan
            # await self._clear_permission_cache(tenant_uuid)

            log.info(f"✅ Successfully synced extra storage to tenant: {tenant_uuid}")
            log.info(f" END sync extra storage to tenant: {tenant_uuid}")
        except Exception as e:
            log.error(f"❌ Failed to sync extra storage to tenant: {e}")
            raise e
        finally:
            reset_current_db_name(token)

    def _calculate_total_storage(self, tenant_config) -> int:
        extra_storage = tenant_config.extra_storage or 0
        default_storage = tenant_config.default_storage or 0
        return extra_storage + default_storage

    async def _update_storage_limit_in_redis(
        self, redis_cli, tenant_uuid: str, total_storage: int
    ):
        prefix = StorageRedis.STORAGE_LIMIT.value % tenant_uuid
        try:
            result = await redis_cli.set(prefix, total_storage)

            if not result:
                log.warning(
                    f"⚠️ Redis SET returned False for tenant {tenant_uuid}, key={prefix}, value={total_storage}"
                )
                return

            log.info(
                f"✅ Redis storage limit updated: tenant={tenant_uuid}, key={prefix}, value={total_storage}GB"
            )
        except Exception as e:
            log.error(
                f"❌ Exception while updating Redis storage limit: tenant={tenant_uuid}, key={prefix}, "
                f"value={total_storage}, error={e}"
            )

    async def _update_tenant_configuration(
        self,
        db_session: AsyncSession,
        tenant_uuid: str,
        storage_data: UpdateTenantConfigurationRequest,
    ):
        tenant_config = await self._get_tenant_configuration(db_session, tenant_uuid)
        self._apply_storage_updates(tenant_config, storage_data)

        await db_session.flush()
        await db_session.refresh(tenant_config)
        return tenant_config

    async def _get_tenant_configuration(self, db_session, tenant_uuid):
        query_get = select(ClinicConfiguration).where(
            ClinicConfiguration.tenant_uuid == tenant_uuid
        )
        result = await db_session.execute(query_get)
        tenant_config = result.scalar_one_or_none()

        if not tenant_config:
            raise CustomValueError(
                message=CustomMessageCode.PRICING_STORAGE_TENANT_CONFIGRATION_NOT_FOUND.title,
                message_code=CustomMessageCode.PRICING_STORAGE_TENANT_CONFIGRATION_NOT_FOUND.code,
            )

        return tenant_config

    def _apply_storage_updates(
        self,
        tenant_config: ClinicConfiguration,
        storage_data: UpdateTenantConfigurationRequest,
    ):
        """Apply storage updates to tenant configuration object"""
        if storage_data.extra_storage is not None:
            tenant_config.extra_storage = (
                tenant_config.extra_storage or 0
            ) + storage_data.extra_storage

        if storage_data.default_storage is not None:
            tenant_config.default_storage = storage_data.default_storage

        if storage_data.plan_key_id is not None:
            tenant_config.plan_key_id = storage_data.plan_key_id

    # async def _clear_permission_cache(self, tenant_uuid: str):
    #     try:
    #         prefix = PermissionRedisKey.PERMISSION_CACHE_PREFIX.value
    #         pattern = f"{prefix}:*"
    #
    #         keys = list(self.redis_cli.scan_iter(pattern))
    #         if keys:
    #             self.redis_cli.delete(*keys)
    #     except Exception as e:
    #         log.error(f"❌ Error clearing permission cache: {e}")

    # endregion
