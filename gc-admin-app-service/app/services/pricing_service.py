from datetime import datetime, time, timedelta, timezone
from typing import Optional
from zoneinfo import ZoneInfo

from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from enums.pricing_enum import PricingStoragePeriod
from fastapi_pagination import Page, Params
from fastapi_pagination.ext.sqlalchemy import paginate
from schemas.requests.pricing import (
    CreateExtraPricingStorageRequest,
    UpdateTenantConfigurationRequest,
)
from schemas.responses.pricing import TenantExtraStorageResponse
from sqlalchemy import String, cast, or_, select
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import (
    MasterPricingStorage,
    TenantClinic,
    TenantExtraStorage,
)
from gc_dentist_shared.core.common.redis import RedisCli
from gc_dentist_shared.core.constants import TIMEZONE_DEFAULT, StorageRedis
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import ClinicConfiguration


class PricingService:
    def __init__(self, central_db_session: AsyncSession, redis_cli: RedisCli = None):
        self.central_db_session = central_db_session
        self.redis_cli = redis_cli

    # region Public Methods
    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_extra_storage",
    )
    async def create_extra_storage(self, payload: CreateExtraPricingStorageRequest):
        log.info(" START create extra storage")

        async with self.central_db_session.begin():
            master_extra_storage = await self._get_pricing_storage_by_id(
                self.central_db_session, payload.storage_key_id
            )

            tenant = await self._get_tenant_by_uuid(
                self.central_db_session, payload.tenant_uuid
            )

            extra_storage = await self._create_extra_storage(
                self.central_db_session, tenant, master_extra_storage
            )
            log.info("✅ Create pricing extra storage successfully in central database")

            storage_data = UpdateTenantConfigurationRequest(
                extra_storage=master_extra_storage.storage
            )

            # Sync extra storage data to tenant_configuration(TenantDB)
            await self._sync_storage_to_tenant_database(
                tenant.db_name,
                tenant.tenant_uuid,
                storage_data,
            )

        log.info("✅ Create pricing extra storage successfully")

        log.info(" END create extra storage")
        return extra_storage

    async def get_list_pricing_extra_storage(
        self, tenant_uuid: str, params: Params = None, search: str | None = None
    ) -> Page[TenantExtraStorageResponse]:
        query = await self._build_list_tenant_extra_storage(
            tenant_uuid=tenant_uuid, search=search
        )
        return await paginate(
            self.central_db_session, query, params=params, unique=False
        )

    # endregion

    # region Private Methods - Data Retrieval

    async def _get_pricing_storage_by_id(
        self, db_session: AsyncSession, storage_key_id: int
    ) -> MasterPricingStorage:
        query = select(MasterPricingStorage).where(
            MasterPricingStorage.storage_key_id == storage_key_id,
            MasterPricingStorage.is_active.is_(True),
        )
        result = await db_session.execute(query)
        pricing_storage = result.scalar_one_or_none()

        if not pricing_storage:
            raise CustomValueError(
                message=CustomMessageCode.PRICING_STORAGE_NOT_FOUND.title,
                message_code=CustomMessageCode.PRICING_STORAGE_NOT_FOUND.code,
            )

        return pricing_storage

    async def _get_tenant_by_uuid(
        self, db_session: AsyncSession, tenant_uuid: str
    ) -> TenantClinic:
        query = select(TenantClinic).where(TenantClinic.tenant_uuid == tenant_uuid)
        result = await db_session.execute(query)
        tenant = result.scalar_one_or_none()

        if not tenant:
            raise CustomValueError(
                message=CustomMessageCode.TENANT_NOT_FOUND.title,
                message_code=CustomMessageCode.TENANT_NOT_FOUND.code,
            )

        return tenant

    async def _get_tenant_configuration(self, db_session, tenant_uuid):
        query_get = select(ClinicConfiguration).where(
            ClinicConfiguration.tenant_uuid == tenant_uuid
        )
        result = await db_session.execute(query_get)
        tenant_config = result.scalar_one_or_none()

        if not tenant_config:
            raise CustomValueError(
                message=CustomMessageCode.PRICING_STORAGE_TENANT_CONFIGRATION_NOT_FOUND.title,
                message_code=CustomMessageCode.PRICING_STORAGE_TENANT_CONFIGRATION_NOT_FOUND.code,
            )

        return tenant_config

    # endregion

    # region Private Methods - Extra Storage Creation Flow

    async def _create_extra_storage(
        self, db_session: AsyncSession, tenant, master_extra_storage
    ):
        expired_at = self._calculate_expiration_date(master_extra_storage.period)

        extra_storage = TenantExtraStorage(
            tenant_uuid=tenant.tenant_uuid,
            storage_key_id=master_extra_storage.storage_key_id,
            expired_at=expired_at,
        )
        db_session.add(extra_storage)
        await db_session.flush()
        await db_session.refresh(extra_storage)
        return extra_storage

    def _calculate_expiration_date(self, period_id, tenant_tz: str = TIMEZONE_DEFAULT):
        # TODO This is temporary solution, need to confirm later
        days = PricingStoragePeriod.get_days_by_period_id(period_id)
        if days is None:
            raise CustomValueError(
                message=CustomMessageCode.PRICING_STORAGE_PERIOD_NOT_FOUND.title,
                message_code=CustomMessageCode.PRICING_STORAGE_PERIOD_NOT_FOUND.code,
            )

        if days == 0:
            return None

        now_utc = datetime.now(timezone.utc)

        tz = ZoneInfo(tenant_tz)
        now_local = now_utc.astimezone(tz)

        start_date_local = now_local.date()

        valid_to_local = datetime.combine(
            start_date_local + timedelta(days=days), time.min, tz
        )

        expired_at_utc_naive = valid_to_local.astimezone(timezone.utc)
        return expired_at_utc_naive

    # endregion

    # region Private Methods - Tenant Database Sync Flow

    async def _sync_storage_to_tenant_database(
        self,
        tenant_db_name: str,
        tenant_uuid: str,
        storage_data: UpdateTenantConfigurationRequest,
    ):
        token = set_current_db_name(tenant_db_name)
        try:
            log.info(f"START sync extra storage to tenant: {tenant_uuid}")
            async with TenantDatabase.get_instance_tenant_db() as tenant_db_session:
                async with tenant_db_session.begin():
                    tenant_config = await self._update_tenant_configuration(
                        tenant_db_session, tenant_uuid, storage_data
                    )

            # Update storage limit in Redis
            total_storage = self._calculate_total_storage(tenant_config)
            await self._update_storage_limit_in_redis(
                self.redis_cli, tenant_uuid, total_storage
            )

            log.info(f"✅ Successfully synced extra storage to tenant: {tenant_uuid}")
            log.info(f" END sync extra storage to tenant: {tenant_uuid}")
        except Exception as e:
            log.error(f"❌ Failed to sync extra storage to tenant: {e}")
            raise e
        finally:
            reset_current_db_name(token)

    async def _update_tenant_configuration(
        self,
        db_session: AsyncSession,
        tenant_uuid: str,
        storage_data: UpdateTenantConfigurationRequest,
    ):
        tenant_config = await self._get_tenant_configuration(db_session, tenant_uuid)
        self._apply_storage_updates(tenant_config, storage_data)

        await db_session.flush()
        await db_session.refresh(tenant_config)
        return tenant_config

    def _apply_storage_updates(
        self,
        tenant_config: ClinicConfiguration,
        storage_data: UpdateTenantConfigurationRequest,
    ):
        """Apply storage updates to tenant configuration object"""
        if storage_data.extra_storage is not None:
            tenant_config.extra_storage = (
                tenant_config.extra_storage or 0
            ) + storage_data.extra_storage

        if storage_data.default_storage is not None:
            tenant_config.default_storage = storage_data.default_storage

        if storage_data.plan_key_id is not None:
            tenant_config.plan_key_id = storage_data.plan_key_id

    def _calculate_total_storage(self, tenant_config) -> int:
        extra_storage = tenant_config.extra_storage or 0
        default_storage = tenant_config.default_storage or 0
        return extra_storage + default_storage

    async def _update_storage_limit_in_redis(
        self, redis_cli, tenant_uuid: str, total_storage: int
    ):
        prefix = StorageRedis.STORAGE_LIMIT.value % tenant_uuid
        try:
            result = await redis_cli.set(prefix, total_storage)

            if not result:
                log.warning(
                    f"⚠️ Redis SET returned False for tenant {tenant_uuid}, key={prefix}, value={total_storage}"
                )
                return

            log.info(
                f"✅ Redis storage limit updated: tenant={tenant_uuid}, key={prefix}, value={total_storage}GB"
            )
        except Exception as e:
            log.error(
                f"❌ Exception while updating Redis storage limit: tenant={tenant_uuid}, key={prefix}, "
                f"value={total_storage}, error={e}"
            )

    # endregion

    # region Private Methods - Query Building

    async def _build_list_tenant_extra_storage(
        self, tenant_uuid: str, search: Optional[str] = None
    ):
        fields = [
            TenantExtraStorage.id,
            TenantExtraStorage.storage_key_id,
            TenantExtraStorage.expired_at,
            TenantExtraStorage.created_at,
            TenantExtraStorage.updated_at,
            MasterPricingStorage.name,
            MasterPricingStorage.pricing,
            MasterPricingStorage.storage,
        ]

        where_clause = [TenantExtraStorage.tenant_uuid == tenant_uuid]

        if search:
            search_pattern = f"%{search}%"
            where_clause.append(
                or_(
                    MasterPricingStorage.name.ilike(search_pattern),
                    cast(MasterPricingStorage.storage, String).ilike(search_pattern),
                    cast(MasterPricingStorage.pricing, String).ilike(search_pattern),
                )
            )

        query = (
            select(*fields)
            .join(
                MasterPricingStorage,
                TenantExtraStorage.storage_key_id
                == MasterPricingStorage.storage_key_id,
            )
            .where(*where_clause)
            .order_by(TenantExtraStorage.id.desc())
        )
        return query

    # endregion
