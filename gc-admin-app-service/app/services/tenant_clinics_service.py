import ast

from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from configuration.settings import configuration
from core.constants import ClinicRedis<PERSON>ey, TenantClinicStatus
from core.messages import CustomMessageCode
from core.permission.process_sync_permissions import process_sync_tenant
from db.db_connection import CentralDatabase, TenantDatabase
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from schemas.requests.tenant_clinic_schema import UpdateClinicInfoSchema
from schemas.responses.tenant_clinic_schema import TenantInfoResponseSchema
from schemas.tenant_clinic_requests import (
    CreateClinicInfoSchema,
    CreateTenantConfigSchema,
    CreateTenantSchema,
    ExtraStoragesSchema,
    PlanSchema,
    RetryTenantSchema,
    StepDataSchema,
)
from sqlalchemy import func, or_, select, text, update
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import (
    MasterPlan,
    MasterPricingStorage,
    TenantClinic,
    TenantExtraStorage,
    TenantPlan,
)
from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.common.redis import RedisCli
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.enums.role_enum import ROLE_KEY_MAPPING, RoleKeyEnum
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.core.permissions.load_file_yaml import load_tenant_roles_yaml
from gc_dentist_shared.tenant_models import (
    ClinicConfiguration,
    ClinicInformation,
    DoctorProfile,
    DoctorRole,
    DoctorUser,
    Roles,
)


class TenantClinicService:
    def __init__(self, session: AsyncSession):
        self.session = session

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_tenant",
    )
    async def create_tenant(self, data: CreateTenantSchema) -> TenantClinic:
        tenant = TenantClinic(
            clinic_name=data.clinic_name,
            clinic_no=data.clinic_no,
            db_name=data.db_name,
            db_uri=data.db_uri,
            phone_number=data.clinic_info.phone_number,
            email=data.clinic_info.email,
            address_1=data.clinic_info.address_1,
            address_2=data.clinic_info.address_2,
            address_3=data.clinic_info.address_3,
            latitude=data.clinic_info.latitude,
            longitude=data.clinic_info.longitude,
            logo_url=data.clinic_info.logo_url,
            opening_hours=data.clinic_info.opening_hours,
        )
        async with self.session.begin():
            self.session.add(tenant)
            await self.session.flush()
            await self.session.refresh(tenant)
            return tenant

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="update_tenant_status",
    )
    async def update_tenant_status(self, tenant_uuid: str, status: int) -> bool:
        async with self.session.begin():
            result = await self.session.execute(
                text(
                    """
                    UPDATE tenant_clinics
                    SET status = :status
                    WHERE tenant_uuid = :tenant_uuid
                    RETURNING tenant_uuid
                """
                ),
                {"tenant_uuid": tenant_uuid, "status": status},
            )
            row = result.mappings().first()
            return row

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_db_name_for_tenant",
    )
    async def get_db_name_for_tenant(self, tenant_uuid: str) -> str:
        async with self.session.begin():
            result = await self.session.execute(
                text(
                    "SELECT db_name FROM tenant_clinics WHERE tenant_uuid = :tenant_uuid"
                ),
                {"tenant_uuid": tenant_uuid},
            )
            db_name = result.scalar_one_or_none()
            return db_name

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_tenant_db_name_by_clinic_name",
    )
    async def get_tenant_db_name_by_clinic_name(self, clinic_name: str) -> str:
        async with self.session.begin():
            result = await self.session.execute(
                select(TenantClinic.db_name).where(
                    TenantClinic.clinic_name == clinic_name
                )
            )
            db_name = result.scalar_one_or_none()
            return db_name

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_tenant_data_by_name",
    )
    async def get_tenant_data_by_name(self, clinic_name: str) -> TenantClinic:
        async with self.session.begin():
            result = await self.session.execute(
                select(TenantClinic).where(TenantClinic.clinic_name == clinic_name)
            )
            tenant_obj = result.scalar_one_or_none()
            return tenant_obj

    async def validate_data_create_tenant(self, data: CreateTenantSchema) -> bool:
        # Validate role_key_id
        roles_data = load_tenant_roles_yaml()
        for role in roles_data.get("roles", []):
            if role.get("role_key_id") not in ROLE_KEY_MAPPING.values():
                log.error(f"❌ Invalid role_key_id {role.get('role_key_id')}")
                return False

        async with self.session:
            # Validate plan_key_id
            plan_key_id = data.plan_info.plan_key_id
            result = await self.session.execute(
                select(MasterPlan.plan_key_id).where(
                    MasterPlan.plan_key_id == plan_key_id
                )
            )
            plan = result.scalar_one_or_none()
            if not plan:
                log.error(f"❌ Invalid plan_key_id {plan_key_id}")
                return False

            # Validate extra storage ids
            extra_storage_ids = [
                storage.storage_key_id for storage in data.extra_storages_info
            ]
            if extra_storage_ids:
                result = await self.session.execute(
                    select(MasterPricingStorage.storage_key_id).where(
                        MasterPricingStorage.storage_key_id.in_(extra_storage_ids)
                    )
                )
                valid_storage_ids = {
                    row["storage_key_id"] for row in result.mappings().all()
                }
                invalid_ids = set(extra_storage_ids) - valid_storage_ids
                if invalid_ids:
                    log.error(f"❌ Invalid extra_storage_ids {invalid_ids}")
                    return False
        return True

    async def create_tenant_multi_plan(
        self, session: AsyncSession, tenant_uuid: str, objs: list[PlanSchema]
    ):
        if not objs:
            return []
        plan_ids = []
        for obj in objs:
            try:
                plan = TenantPlan(
                    tenant_uuid=tenant_uuid,
                    **obj.model_dump(),
                )
                session.add(plan)
                await session.flush()
                await session.refresh(plan)
                plan_ids.append(plan.id)
            except Exception as e:
                log.error(f"❌ Error creating tenant plan: {str(e)}")
        return plan_ids

    async def create_tenant_plan(
        self, session: AsyncSession, tenant_uuid: str, obj: PlanSchema
    ):
        if not obj:
            return 0

        plan = TenantPlan(
            tenant_uuid=tenant_uuid,
            **obj.model_dump(),
        )
        session.add(plan)
        await session.flush()
        await session.refresh(plan)
        return plan.id

    async def create_tenant_multi_extra_storages(
        self, session: AsyncSession, tenant_uuid: str, objs: list[ExtraStoragesSchema]
    ):
        if not objs:
            return []
        storage_ids = []
        for obj in objs:
            storage = TenantExtraStorage(
                tenant_uuid=tenant_uuid,
                **obj.model_dump(),
            )
            session.add(storage)
            await session.flush()
            await session.refresh(storage)
            storage_ids.append(storage.id)
        return storage_ids

    async def sum_extra_storage_of_tenant(
        self, session: AsyncSession, extra_storage_ids: list[int]
    ) -> int:
        if not extra_storage_ids:
            return 0
        total_size = 0
        try:
            result = await session.execute(
                select(func.sum(MasterPricingStorage.storage)).where(
                    MasterPricingStorage.storage_key_id.in_(extra_storage_ids)
                )
            )
            total_size = result.scalar_one_or_none() or 0
        except Exception as e:
            log.error(f"❌ Error summing storage sizes: {str(e)}")

        return total_size

    async def get_default_stograge(
        self, session: AsyncSession, plan_key_id: int
    ) -> int:
        try:
            result = await session.execute(
                select(MasterPlan.default_storage).where(
                    MasterPlan.plan_key_id == plan_key_id
                )
            )
            default_storage = result.scalar_one_or_none() or 0

            return default_storage
        except Exception as e:
            log.error(f"❌ Error getting default storage: {str(e)}")
            return 0

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_tenant_plans_and_storages",
    )
    async def create_tenant_plans_and_storages(
        self,
        tenant_uuid: str,
        plan_info: PlanSchema,
        extra_storages_info: list[ExtraStoragesSchema],
    ):
        async with self.session.begin():
            # Create tenant plan
            await self.create_tenant_plan(self.session, tenant_uuid, plan_info)
            # Create tenant relation storages
            await self.create_tenant_multi_extra_storages(
                self.session, tenant_uuid, extra_storages_info
            )

    async def create_tenant_clinic_doctor(
        self,
        session: AsyncSession,
        data: CreateClinicInfoSchema,
    ) -> int:
        """
        Create a new clinic account record in the database.
        :param data: Data for creating a clinic.
        :return: The id created clinic doctor.
        """
        clinic_ifo = ClinicInformation(
            **data.model_dump(exclude={"manager_info", "clinic_db_name"}),
        )
        admin_user = DoctorUser(
            username=data.manager_info.email,
            required_change_password=data.manager_info.required_change_password,
        )
        admin_user.set_password(data.manager_info.password, data.tenant_uuid)

        # Add clinic information
        session.add(clinic_ifo)
        await session.flush()
        await session.refresh(clinic_ifo)

        # Add admin user
        session.add(admin_user)
        await session.flush()
        await session.refresh(admin_user)
        return admin_user.id

    async def create_tenant_clinic_doctor_profile(
        self, session: AsyncSession, data: CreateClinicInfoSchema, doctor_user_id: int
    ) -> int:
        admin_profile = DoctorProfile(
            doctor_user_id=doctor_user_id,
            **data.manager_info.model_dump(
                exclude={
                    "password",
                    "required_change_password",
                    "is_active",
                },
                mode="json",
            ),
        )

        # Encrypt sensitive fields
        aes = AesGCMRotation(configuration)
        admin_profile.phone_hash = aes.sha256_hash(admin_profile.phone)
        admin_profile.email_hash = aes.sha256_hash(admin_profile.email)
        admin_profile.date_of_birth_hash = aes.sha256_hash(admin_profile.date_of_birth)
        admin_profile.phone = aes.encrypt_data(admin_profile.phone)
        admin_profile.email = aes.encrypt_data(admin_profile.email)
        admin_profile.date_of_birth = aes.encrypt_data(admin_profile.date_of_birth)

        session.add(admin_profile)
        await session.flush()
        await session.refresh(admin_profile)
        return admin_profile.id

    async def create_roles_default_for_clinic(self, session: AsyncSession) -> None:
        roles_data = load_tenant_roles_yaml()
        for role in roles_data.get("roles", []):
            existing_role = await session.execute(
                select(Roles).where(Roles.role_key_id == role["role_key_id"])
            )
            role_obj = existing_role.scalar_one_or_none()
            if role_obj:
                continue

            role_obj = Roles(**role)
            session.add(role_obj)
        await session.flush()

    async def create_relation_role_doctor(
        self, session: AsyncSession, doctor_user_id: int
    ):
        doctor = DoctorRole(
            doctor_user_id=doctor_user_id,
            role_key_id=RoleKeyEnum.CLINIC_ADMIN.value,
        )
        session.add(doctor)
        await session.flush()
        return doctor.id

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_tenant_config",
    )
    async def create_tenant_config(
        self,
        session: AsyncSession,
        obj: CreateTenantConfigSchema,
        plan_info: PlanSchema,
        extra_storages_info: list[ExtraStoragesSchema],
    ):
        # Calculate total extra storage
        master_extra_storage_ids = [
            storage.storage_key_id for storage in extra_storages_info
        ]
        total_extra_storage = await self.sum_extra_storage_of_tenant(
            session=self.session, extra_storage_ids=master_extra_storage_ids
        )
        # Get default storage from plan
        default_storage = await self.get_default_stograge(
            session=self.session,
            plan_key_id=plan_info.plan_key_id,
        )

        async with session.begin():
            config = ClinicConfiguration(
                **obj.model_dump(),
                default_storage=default_storage,
                extra_storage=total_extra_storage,
            )
            session.add(config)
            await session.flush()
            await session.refresh(config)
            return config.tenant_uuid

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_admin_and_role_for_tenant",
    )
    async def create_admin_and_role_for_tenant(
        self,
        session: AsyncSession,
        clinic_data: CreateClinicInfoSchema,
    ) -> int:
        async with session.begin():
            doctor_user_id = await self.create_tenant_clinic_doctor(
                session=session,
                data=clinic_data,
            )
            await self.create_tenant_clinic_doctor_profile(
                session=session,
                data=clinic_data,
                doctor_user_id=doctor_user_id,
            )
            await self.create_roles_default_for_clinic(session=session)
            await self.create_relation_role_doctor(
                session=session,
                doctor_user_id=doctor_user_id,
            )
        return doctor_user_id

    def func_log_step_errors(
        self,
        func_name: str,
        msg: str,
        data: dict,
    ) -> dict:
        log.error(f"❌ Error {func_name}: {msg}")
        self.result_errors[func_name] = {
            "error": msg,
            "data": data,
        }

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="log_tenant_clinic_settings_errors",
    )
    async def log_tenant_clinic_settings_errors(
        self,
        tenant_uuid: str,
    ):
        if not self.result_errors:
            return
        async with CentralDatabase.get_instance_db() as db_session:
            async with db_session.begin():
                result = await db_session.execute(
                    update(TenantClinic)
                    .where(TenantClinic.tenant_uuid == tenant_uuid)
                    .values(
                        status=TenantClinicStatus.FAILED_CREATED_CLINIC_SETTING.value,
                        step_data_errors=self.result_errors,
                    )
                )
                return result.rowcount

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="update_status_tenant",
    )
    async def update_status_tenant(self, tenant_uuid: str, status: int, **kwargs):
        async with CentralDatabase.get_instance_db() as db_session:
            async with db_session.begin():
                result = await db_session.execute(
                    update(TenantClinic)
                    .where(TenantClinic.tenant_uuid == tenant_uuid)
                    .values(status=status, **kwargs)
                )
                return result.rowcount

    async def create_tenant_clinic_settings(
        self,
        clinic_data: CreateClinicInfoSchema,
        plan_info: PlanSchema,
        extra_storages_info: list[ExtraStoragesSchema],
    ):
        token = set_current_db_name(clinic_data.clinic_db_name)
        self.result_errors = {}
        try:
            # 1. Create admin and role
            try:
                async with TenantDatabase.get_instance_tenant_db() as session:
                    await self.create_admin_and_role_for_tenant(
                        session=session,
                        clinic_data=clinic_data,
                    )
                log.info(
                    f"✅ Created clinic admin and roles for {clinic_data.clinic_no}"
                )
            except Exception as e:
                self.func_log_step_errors(
                    func_name=self.create_admin_and_role_for_tenant.__name__,
                    msg=str(e),
                    data={"clinic_data": clinic_data.model_dump(mode="json")},
                )

            # 2. Create tenant plans and storages
            try:
                await self.create_tenant_plans_and_storages(
                    tenant_uuid=clinic_data.tenant_uuid,
                    plan_info=plan_info,
                    extra_storages_info=extra_storages_info,
                )
                log.info(
                    f"✅ Created tenant plans and storages for {clinic_data.clinic_no}"
                )
            except Exception as e:
                self.func_log_step_errors(
                    func_name=self.create_tenant_plans_and_storages.__name__,
                    msg=str(e),
                    data={
                        "tenant_uuid": clinic_data.tenant_uuid,
                        "plan_info": plan_info.model_dump(mode="json"),
                        "extra_storages_info": [
                            storage.model_dump(mode="json")
                            for storage in extra_storages_info
                        ],
                    },
                )
            # 3. Create tenant configuration info
            data_tenant_config = CreateTenantConfigSchema(
                tenant_uuid=clinic_data.tenant_uuid,
                clinic_name=clinic_data.clinic_name,
                clinic_no=clinic_data.clinic_no,
                plan_key_id=plan_info.plan_key_id,
            )
            try:
                async with TenantDatabase.get_instance_tenant_db() as session:
                    await self.create_tenant_config(
                        session=session,
                        obj=data_tenant_config,
                        plan_info=plan_info,
                        extra_storages_info=extra_storages_info,
                    )
                log.info(f"✅ Created clinic settings for {clinic_data.clinic_no}")
            except Exception as e:
                self.func_log_step_errors(
                    func_name=self.create_tenant_config.__name__,
                    msg=str(e),
                    data={
                        "obj": data_tenant_config.model_dump(mode="json"),
                        "plan_info": plan_info.model_dump(mode="json"),
                        "extra_storages_info": [
                            storage.model_dump(mode="json")
                            for storage in extra_storages_info
                        ],
                    },
                )

            # 4. Sync permissions
            func_step_2 = self.create_tenant_plans_and_storages.__name__
            previous_step_status = func_step_2 not in self.result_errors
            msg_process_sync_tenant = None
            status_sync_tenant = False
            try:
                if previous_step_status:
                    status_sync_tenant = await process_sync_tenant(
                        tenant_db_names=[clinic_data.clinic_db_name],
                        plan_key_id=plan_info.plan_key_id,
                    )
                    log.info(
                        f"✅ Synced permissions for tenant {clinic_data.clinic_db_name}"
                    )
            except Exception as e:
                msg_process_sync_tenant = str(e)

            if not status_sync_tenant:
                self.func_log_step_errors(
                    func_name=process_sync_tenant.__name__,
                    msg=msg_process_sync_tenant
                    or "Error process_sync_tenant function!",
                    data={
                        "tenant_db_names": [clinic_data.clinic_db_name],
                        "plan_key_id": plan_info.plan_key_id,
                    },
                )
        except Exception as e:
            log.error(f"❌ Error creating clinic info: {str(e)}")
            raise e
        finally:

            reset_current_db_name(token)
            await self.delete_cache_clinic_no()  # noqa: ASYNC102

        if self.result_errors:
            await self.log_tenant_clinic_settings_errors(
                tenant_uuid=clinic_data.tenant_uuid,
            )
            raise Exception(
                "Error(s) occurred during clinic setup. Check logs for details."
            )

    async def delete_cache_clinic_no(self) -> None:
        """
        Delete the clinic no cache.
        :return: None
        """
        try:
            redis_cli = await RedisCli.get_instance(configuration)
            prefix = ClinicRedisKey.CLINICS_CACHE.value
            await redis_cli.delete(prefix)
        except Exception as e:
            log.error(f"❌ Error setting cache for clinic no: {str(e)}")

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_list_clinic_no_from_central_db",
    )
    async def get_list_clinic_no_from_central_db(self):
        """
        Get a list of all clinics.
        :return: List of ClinicInformation objects.
        """
        async with self.session.begin():
            result = await self.session.execute(
                select(TenantClinic.clinic_no).where(
                    TenantClinic.status == TenantClinicStatus.SUCCESS.value
                )
            )
            rows = result.scalars().all()
            return [row for row in rows if row is not None]

    async def set_cache_clinic_no(self, list_clinic: list[str]) -> None:
        """
        Set the clinic slug in cache.
        :param clinic_no: clinic_no of the clinic.
        """
        try:
            redis_cli = await RedisCli.get_instance(configuration)
            prefix = ClinicRedisKey.CLINICS_CACHE.value

            if list_clinic:
                await redis_cli.set(prefix, str(list_clinic))
            else:
                await redis_cli.delete(prefix)
        except Exception as e:
            log.error(f"❌ Error setting cache for clinic slugs: {str(e)}")

    async def get_cache_clinic_no(self) -> list[str]:
        """
        Get the clinic slug from cache.
        :return: List of clinic slugs.
        """
        redis_cli = await RedisCli.get_instance(configuration)
        prefix = ClinicRedisKey.CLINICS_CACHE.value
        cached_value = await redis_cli.get(prefix)

        if cached_value:
            try:
                list_clinic = ast.literal_eval(cached_value)
                if isinstance(list_clinic, list):
                    return list_clinic
            except (SyntaxError, ValueError) as e:
                log.error(f"❌ Error parsing cached clinic slugs: {str(e)}")
        return []

    async def check_clinic_no_exists(self, clinic_no: str) -> bool:
        """
        Check if the clinic_no exists in the cache.
        :param clinic_no: clinic_no of the clinic.
        :return: True if the slug exists, False otherwise.
        """
        list_clinic = await self.get_cache_clinic_no()
        if not list_clinic:
            list_clinic = await self.get_list_clinic_no_from_central_db()
            await self.set_cache_clinic_no(list_clinic)

        return clinic_no in list_clinic if list_clinic else False

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_tenant_uuid_by_db_name",
    )
    async def get_tenant_uuid_by_db_name(self, db_name: str) -> str:
        async with self.session.begin():
            result = await self.session.execute(
                select(TenantClinic.tenant_uuid).where(TenantClinic.db_name == db_name)
            )
            tenant_uuid = result.scalar_one_or_none()
            return tenant_uuid

    def build_params_step(self, step_data: StepDataSchema, value_extra: dict):
        params = {
            **value_extra,
        }
        if step_data:
            params.update(
                **{
                    key: getattr(step_data.data, key)
                    for key in step_data.data.__class__.model_fields.keys()
                }
            )

        return params

    def condition_run_function(self, session: AsyncSession, obj: RetryTenantSchema):
        step_configs = {
            self.create_admin_and_role_for_tenant: {"session": session, "index": 1},
            self.create_tenant_plans_and_storages: {"index": 2},
            self.create_tenant_config: {"session": session, "index": 3},
            process_sync_tenant: {
                "index": 4,
                "depends_on": {self.create_tenant_plans_and_storages.__name__},
            },
        }
        steps = {
            func_name: self.build_params_step(
                getattr(obj, func_name.__name__), value_extra
            )
            for func_name, value_extra in step_configs.items()
            if getattr(obj, func_name.__name__) is not None
        }

        return steps

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="validate_retry_step",
    )
    async def validate_retry_step(self, clinic_no: str):
        async with self.session.begin():
            result = await self.session.execute(
                select(TenantClinic).where(TenantClinic.clinic_no == clinic_no)
            )
            tenant = result.scalar_one_or_none()
            if not tenant:
                log.error(f"❌ Tenant with clinic no {clinic_no} not found")
                raise Exception(CustomMessageCode.TENANT_NOT_FOUND.title)

            if (
                tenant.status != TenantClinicStatus.FAILED_CREATED_CLINIC_SETTING.value
                or not tenant.step_data_errors
            ):
                log.error(
                    f"❌ Tenant {clinic_no} status is not FAILED_CREATED_CLINIC_SETTING"
                )
                raise Exception(CustomMessageCode.TENANT_NOT_STATUS_RETRY.title)

        return tenant

    async def retry_step_create_tenant_error(self, clinic_no: str) -> None:
        tenant: TenantClinic = await self.validate_retry_step(clinic_no)
        tenant_uuid = tenant.tenant_uuid

        self.result_errors: dict = tenant.step_data_errors
        object_data = RetryTenantSchema(**self.result_errors)

        token = set_current_db_name(tenant.db_name)
        try:
            async with TenantDatabase.get_instance_tenant_db() as tenant_session:
                functions_to_run = self.condition_run_function(
                    session=tenant_session, obj=object_data
                )
                # Sort functions by index
                functions_to_run = dict(
                    sorted(
                        functions_to_run.items(),
                        key=lambda item: item[1]["index"],
                    )
                )

                for func_step, params in functions_to_run.items():
                    try:
                        params.pop("index", None)
                        depends_on = params.pop("depends_on", None)
                        if depends_on and bool(depends_on & self.result_errors.keys()):
                            log.warning(
                                f"❗Skipping step {func_step.__name__} due to failed dependencies."
                            )
                            continue
                        await func_step(**params)
                        log.info(f"✅ Successfully retried step: {func_step.__name__}")
                        self.result_errors.pop(func_step.__name__, None)
                    except Exception as e:
                        log.error(
                            f"❌ Error retrying step {func_step.__name__}: {str(e)}"
                        )

        except Exception as e:
            log.error(f"❌ Error initializing tenant database session: {str(e)}")
            raise e
        finally:
            reset_current_db_name(token)
            await self.delete_cache_clinic_no()  # noqa: ASYNC102

        if self.result_errors:
            await self.log_tenant_clinic_settings_errors(
                tenant_uuid=tenant_uuid,
            )
            raise CustomValueError(
                message=CustomMessageCode.ERROR_RETRY_STEP_TENANT.title,
                message_code=CustomMessageCode.ERROR_RETRY_STEP_TENANT.code,
            )

        await self.update_status_tenant(
            tenant_uuid=tenant_uuid,
            status=TenantClinicStatus.SUCCESS.value,
            step_data_errors={},
        )

    async def search_tenant_clinics(
        self, search: str | None = None
    ) -> Page[TenantInfoResponseSchema]:
        query = await self._build_search_tenant_clinics_query(search)
        return await paginate(self.session, query, unique=False)

    async def get_tenant_clinic(self, tenant_uuid: str) -> TenantInfoResponseSchema:
        result = await self._get_tenant_clinic_by_uuid(self.session, tenant_uuid)
        if not result:
            raise CustomValueError(
                message=CustomMessageCode.TENANT_NOT_FOUND.title,
                message_code=CustomMessageCode.TENANT_NOT_FOUND.code,
            )

        result_dict = result.__dict__

        return TenantInfoResponseSchema(**result_dict)

    async def update_tenant_clinic(
        self, tenant_uuid: str, payload: UpdateClinicInfoSchema
    ):
        async with self.session.begin():
            log.info(f"START update tenant clinics: {tenant_uuid}")
            tenant_clinic = await self._get_tenant_clinic_by_uuid(
                self.session, tenant_uuid
            )

            if not tenant_clinic:
                raise CustomValueError(
                    message=CustomMessageCode.TENANT_NOT_FOUND.title,
                    message_code=CustomMessageCode.TENANT_NOT_FOUND.code,
                )

            for key, value in payload.model_dump().items():
                if value is not None:
                    setattr(tenant_clinic, key, value)
            await self.session.flush()
            await self.session.refresh(tenant_clinic)
            log.info(f"✅ Successfully updated tenant clinics: {tenant_uuid}")

            # Update to tenant database
            await self._sync_clinic_information_to_tenant_database(
                payload, tenant_clinic, tenant_uuid
            )
            log.info(f"END update tenant clinics: {tenant_uuid}")

    # region Private Methods - CRUD Tenant Clinic
    async def _build_search_tenant_clinics_query(self, search: str | None = None):
        fields = [
            TenantClinic.tenant_uuid,
            TenantClinic.clinic_name,
            TenantClinic.clinic_no,
            TenantClinic.status,
            TenantClinic.phone_number,
            TenantClinic.email,
            TenantClinic.address_1,
            TenantClinic.address_2,
            TenantClinic.address_3,
            TenantClinic.latitude,
            TenantClinic.longitude,
            TenantClinic.logo_url,
            TenantClinic.opening_hours,
        ]

        where_clause = [TenantClinic.status == TenantClinicStatus.SUCCESS.value]

        if search:
            search_pattern = f"%{search}%"
            where_clause.append(
                or_(
                    TenantClinic.clinic_name.ilike(search_pattern),
                    TenantClinic.clinic_no.ilike(search_pattern),
                )
            )

        query = (
            select(*fields)
            .where(*where_clause)
            .order_by(TenantClinic.created_at.desc())
        )
        return query

    async def _get_tenant_clinic_by_uuid(
        self, db_session: AsyncSession, tenant_uuid: str
    ) -> TenantClinic:
        query = select(TenantClinic).where(
            TenantClinic.tenant_uuid == tenant_uuid,
            TenantClinic.status == TenantClinicStatus.SUCCESS.value,
        )
        result = await db_session.execute(query)
        tenant = result.scalar_one_or_none()
        return tenant

    async def _sync_clinic_information_to_tenant_database(
        self,
        payload: UpdateClinicInfoSchema,
        tenant_clinic: TenantClinic,
        tenant_uuid: str,
    ):
        token = set_current_db_name(tenant_clinic.db_name)
        try:
            async with TenantDatabase.get_instance_tenant_db() as tenant_db_session:
                async with tenant_db_session.begin():
                    await self._update_tenant_clinic_information(
                        db_session=tenant_db_session,
                        data=payload,
                        tenant_uuid=tenant_uuid,
                    )
        except Exception as e:
            log.error(f"❌ Error update tenant clinic doctor profile: {str(e)}")
            raise e
        finally:
            reset_current_db_name(token)

    async def _update_tenant_clinic_information(
        self, db_session: AsyncSession, data: UpdateClinicInfoSchema, tenant_uuid: str
    ):
        stmt = (
            update(ClinicInformation)
            .where(
                ClinicInformation.tenant_uuid == tenant_uuid,
                ClinicInformation.is_active.is_(True),
            )
            .values(
                **data.model_dump(
                    exclude_unset=True,
                    mode="json",
                ),
            )
        )
        result = await db_session.execute(stmt)
        log.info(
            f"Updated {result.rowcount} clinic information records for tenant {tenant_uuid}"
        )

        if result.rowcount == 0:
            log.warning(
                f"No clinic information records found for tenant {tenant_uuid} with is_active=True"
            )

    # endregion
