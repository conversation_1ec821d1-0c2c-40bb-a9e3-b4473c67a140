import asyncio
from typing import Optional

from configuration.settings import configuration
from core.common.api_response import ApiR<PERSON>ponse
from core.constants import S3_SYNC_CONCURRENCY_LIMIT, TenantClinicStatus
from core.messages import CustomMessageCode
from schemas.responses.storage import StorageUsageR<PERSON>ult
from sqlalchemy import select
from sqlalchemy.exc import <PERSON><PERSON><PERSON><PERSON><PERSON>, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import TenantClinic
from gc_dentist_shared.core.common.redis import RedisCli
from gc_dentist_shared.core.common.s3_bucket import S3Client
from gc_dentist_shared.core.constants import StorageRedis
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log


class UsageStorageService:
    def __init__(self, central_db_session: AsyncSession, s3_client: S3Client):
        self.central_db_session = central_db_session
        self.s3_client = s3_client

    async def sync_tenants_storage_usage(
        self, tenant_uuids: Optional[list[str]] = None
    ):
        log.info("START sync_tenants_storage_usage")

        try:
            # Get valid tenant UUIDs
            valid_tenant_uuids = await self._get_valid_tenant_uuids(tenant_uuids)

            if not valid_tenant_uuids:
                log.info("❌ No valid tenant UUIDs found")
                return []

            log.info(f"✅ Found {len(valid_tenant_uuids)} valid tenant UUIDs")

            # Process storage usage sync
            failed_tenant_uuids = await self._sync_storage_for_tenants(
                valid_tenant_uuids
            )

            log.info("✅ Sync storage usage completed successfully")
            log.info("END sync_tenants_storage_usage")

            return failed_tenant_uuids

        except CustomValueError as e:
            log.error(
                f"❌ CustomValueError in sync_tenants_storage_usage: {e.status_code} - {e.message}"
            )
            return ApiResponse.error(
                status_code=e.status_code,
                message=e.message,
                message_code=e.message_code,
            )
        except Exception as e:
            log.error(f"❌ Unexpected error in sync_tenants_storage_usage: {e}")
            return ApiResponse.error(
                message=CustomMessageCode.SYNC_STORAGE_USAGE_FAILED.title,
                message_code=CustomMessageCode.SYNC_STORAGE_USAGE_FAILED.code,
            )

    async def _get_valid_tenant_uuids(self, tenant_uuids: Optional[list[str]]):
        if tenant_uuids:
            return await self._validate_and_get_tenant_uuids(tenant_uuids)
        else:
            return await self._get_all_active_tenant_uuids()

    async def _get_all_active_tenant_uuids(self):
        query = select(TenantClinic.tenant_uuid).where(
            TenantClinic.status == TenantClinicStatus.SUCCESS.value
        )
        result = await self.central_db_session.execute(query)
        return list(result.scalars().all())

    async def _validate_and_get_tenant_uuids(self, tenant_uuids: list[str]):
        active_tenant_uuids = set(await self._get_all_active_tenant_uuids())
        tenant_uuids_set = set(tenant_uuids)

        valid_tenant_uuids = list(tenant_uuids_set & active_tenant_uuids)
        invalid_tenant_uuids = list(tenant_uuids_set - active_tenant_uuids)

        if invalid_tenant_uuids:
            log.warning(f"⚠️ Invalid tenant UUIDs found: {invalid_tenant_uuids}")

        return valid_tenant_uuids

    async def _sync_storage_for_tenants(self, tenant_uuids: list[str]):
        failed_tenant_uuids = []
        redis_cli = await RedisCli.get_instance(configuration)
        sem = asyncio.Semaphore(S3_SYNC_CONCURRENCY_LIMIT)

        s3_tasks = [
            self._get_storage_usage_with_limit(tid, sem) for tid in tenant_uuids
        ]
        s3_results = await asyncio.gather(*s3_tasks, return_exceptions=True)

        for tenant_uuid, storage_result in zip(tenant_uuids, s3_results):
            if isinstance(storage_result, Exception) or not storage_result.success:
                failed_tenant_uuids.append(tenant_uuid)
                continue
            else:
                await self._update_tenant_storage_usage_in_redis(
                    tenant_uuid, storage_result.size_bytes, redis_cli
                )

        return failed_tenant_uuids

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="get_storage_usage_from_s3",
    )
    async def _get_storage_usage_from_s3(self, tenant_uuid: str):
        log.info(f"START getting storage usage from S3 for tenant: {tenant_uuid}")

        tenant_folder = f"{configuration.S3_FOLDER_NAME}/{tenant_uuid}"
        try:
            size_bytes = await self.s3_client.get_size_folder(prefix=tenant_folder)

            log.info(
                f"✅ Successfully retrieved storage usage for {tenant_uuid}: {size_bytes} bytes"
            )

            return StorageUsageResult(
                tenant_uuid=tenant_uuid, success=True, size_bytes=size_bytes
            )
        except Exception as e:
            log.error(f"❌ Failed to retrieve storage usage for {tenant_uuid}: {e}")
            return StorageUsageResult(
                tenant_uuid=tenant_uuid, success=False, error_message=str(e)
            )

    async def _get_storage_usage_with_limit(
        self, tenant_uuid: str, sem: asyncio.Semaphore
    ):
        async with sem:
            return await self._get_storage_usage_from_s3(tenant_uuid)

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="update_tenant_storage_usage_in_redis",
    )
    async def _update_tenant_storage_usage_in_redis(
        self, tenant_uuid: str, size_bytes: int, redis_cli: RedisCli
    ) -> None:
        try:
            redis_key = StorageRedis.STORAGE_CURRENT_USAGE.value % tenant_uuid
            set_result = await redis_cli.set(redis_key, size_bytes)
            if not set_result:
                log.error(
                    f"❌ Failed to set Redis storage usage for {tenant_uuid}: Redis SET returned False"
                )

            log.info(
                f"✅ Successfully updated Redis storage usage for {tenant_uuid}: {size_bytes} bytes"
            )
        except Exception as e:
            log.error(f"❌ Failed to update Redis storage usage for {tenant_uuid}: {e}")
