from core.messages import CustomMessageCode
from schemas.responses.system_user_schemas import SystemUserProfileSchema
from sqlalchemy import select
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import SystemUser, SystemUserProfile
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError


class SystemUserService:
    def __init__(self, central_db_session: AsyncSession):
        self.central_db_session = central_db_session

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_user_information",
    )
    async def get_user_information(self, system_user_id) -> SystemUserProfileSchema:
        async with self.central_db_session:
            result = await self.central_db_session.execute(
                select(
                    SystemUserProfile.system_user_id.label("id"),
                    SystemUserProfile.first_name,
                    SystemUserProfile.last_name,
                    SystemUserProfile.phone,
                    SystemUserProfile.country_code,
                    SystemUserProfile.email,
                )
                .join(SystemUser, SystemUser.id == SystemUserProfile.system_user_id)
                .where(
                    SystemUserProfile.system_user_id == system_user_id,
                    SystemUser.is_active.is_(True),
                )
            )
            row = result.mappings().first()
            if not row:
                raise CustomValueError(CustomMessageCode.SYSTEM_USER_NOT_FOUND.title)
            return SystemUserProfileSchema(**row)
