import asyncio
import io
import os
from contextlib import redirect_stdout

from alembic import command
from alembic.config import Config
from core.constants import TenantClinicStatus
from db.db_connection import CentralDatabase
from schemas.tenant_clinic_requests import (
    CreateClinicInfoSchema,
    CreateTenantSchema,
    MigrationTenantPayloads,
    TenantPayloads,
)
from services.tenant_clinics_service import TenantClinicService
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy_utils import create_database, database_exists

from gc_dentist_shared.core.logger.config import log


def run_alembic_migration_tenant(db_name: str, db_uri: str | None = None):
    if not db_uri:
        db_uri = CentralDatabase.get_url_db_sync(db_name).render_as_string(
            hide_password=False
        )

    path_alembic = os.path.join(
        os.path.dirname(os.path.dirname(__file__)), "alembic_tenant.ini"
    )
    config = Config(path_alembic)
    config.set_main_option("sqlalchemy.url", db_uri)
    command.upgrade(config, "head")


async def process_migrate_tenant(tenant_uuid: str, data: TenantPayloads):
    try:
        log.info("🚀 Start Migration Database {data.db_name} ")
        await asyncio.to_thread(run_alembic_migration_tenant, data.db_name, data.db_uri)
        async with CentralDatabase.get_instance_db() as session:
            await TenantClinicService(session=session).update_tenant_status(
                tenant_uuid, TenantClinicStatus.SUCCESS
            )
    except Exception as e:
        log.error("❌ Migration DataBase Tenant Error: {}".format(str(e)))
        async with CentralDatabase.get_instance_db() as session:
            await TenantClinicService(session=session).update_tenant_status(
                tenant_uuid, TenantClinicStatus.FAILED
            )


def run_alembic_upgrade_and_capture(db_name: str, db_uri: str | None = None):
    if not db_uri:
        db_uri = CentralDatabase.get_url_db_sync(db_name).render_as_string(
            hide_password=False
        )

    path_alembic = os.path.join(
        os.path.dirname(os.path.dirname(__file__)), "alembic_tenant.ini"
    )
    config = Config(path_alembic)
    config.set_main_option("sqlalchemy.url", db_uri)
    buffer = io.StringIO()
    with redirect_stdout(buffer):
        command.upgrade(config, "head")
    buffer.seek(0)
    return buffer.readlines()


async def process_migrate_tenant_streaming(tenant_uuid: str, data: TenantPayloads):
    try:
        yield f"🚀 Start Migration Database {data.db_name} \n"
        line_logs_migrate = run_alembic_upgrade_and_capture(data.db_name, data.db_uri)
        for line in line_logs_migrate:
            yield line
        # update status migrate to success
        async with CentralDatabase.get_instance_db() as session:
            await TenantClinicService(session=session).update_tenant_status(
                tenant_uuid, TenantClinicStatus.SUCCESS
            )
    except Exception as e:
        yield "❌ Migration DataBase Tenant Error: {}".format(str(e))
        async with CentralDatabase.get_instance_db() as session:
            await TenantClinicService(session=session).update_tenant_status(
                tenant_uuid, TenantClinicStatus.FAILED
            )
        return


async def process_migrate_change_tenant_streaming(  # noqa: ASYNC124
    tenant_uuid: str, db_name: str, db_uri: str
):
    try:
        yield f"🚀 Start Migration Database Changes {db_name} \n"
        line_logs_migrate = run_alembic_upgrade_and_capture(db_name, db_uri)
        for line in line_logs_migrate:
            yield line
    except Exception as e:
        yield "❌ Migration DataBase Changes Tenant Error: {}".format(str(e))
        return


async def create_tenant_stream(data: CreateTenantSchema, db_session: AsyncSession):
    try:
        tenant_service = TenantClinicService(db_session)
        valid_check = await tenant_service.validate_data_create_tenant(data=data)
        if not valid_check:
            yield "❌ Invalid data create tenant. \n"
            return

        tenant_existing = await tenant_service.get_tenant_db_name_by_clinic_name(
            data.clinic_name
        )

        if tenant_existing:
            yield "❌ Tenant already exists. \n"
            return

        db_uri = (
            CentralDatabase()
            .get_url_db_sync(db_name=data.db_name)
            .render_as_string(hide_password=False)
        )

        tenant_obj = await tenant_service.create_tenant(data)
        if not database_exists(db_uri):
            # log.info("🚀 Start Create Database ",db_uri)
            create_database(db_uri)
            yield f"[✓] Created database: {data.db_name}\n"
        else:
            yield f"[i] Database already exists: {data.db_name}\n"

        async for line in process_migrate_tenant_streaming(
            tenant_obj.tenant_uuid, data
        ):
            yield line
        yield "[✓] Migration database successfully.\n"
        yield "[✓] Start create admin clinic.\n"
        try:
            await tenant_service.create_tenant_clinic_settings(
                clinic_data=CreateClinicInfoSchema(
                    **data.clinic_info.model_dump(),
                    clinic_no=data.clinic_no,
                    tenant_uuid=str(tenant_obj.tenant_uuid),
                    clinic_db_name=data.db_name,
                    manager_info=data.manager_info,
                ),
                plan_info=data.plan_info,
                extra_storages_info=data.extra_storages_info,
            )
        except Exception as e:
            log.error("❌ Create Admin Clinic Error: {}".format(str(e)))
            yield "❌ Create Admin Clinic Error. Please try again."
            return
        yield "[✓] Create admin clinic successful.\n"
    except Exception as e:
        log.error("❌ Create Tenant Error: {}".format(str(e)))
        yield "❌ Create Tenant Error. Please try again."
        return


async def migration_multi_database_stream(data: MigrationTenantPayloads):
    try:
        for item in data.databases:
            db_uri = (
                CentralDatabase()
                .get_url_db_sync(db_name=item.db_name)
                .render_as_string(hide_password=False)
            )
            if not database_exists(db_uri):
                yield f"[i] Database not exists: {item.db_name}\n"
            else:
                async for line in process_migrate_change_tenant_streaming(
                    item.tenant_uuid, db_name=item.db_name, db_uri=db_uri
                ):
                    yield line
                yield "[✓] Migration database change successfully.\n"
    except Exception as e:
        log.error("❌ Migration database change error: {}".format(str(e)))
        yield f"❌ Migration database change tenant_uuid is : {item.tenant_uuid} \
            and database is : {item.db_name} error : {str(e)}"
        return


# async def process_apply_changes(databases: list[str]):
#     try:
#         await asyncio.to_thread(run_alembic_migration_tenant, data.db_name, data.db_uri)

#         await update_tenant_status(data.tenant_uuid, TenantClinicStatus.SUCCESS)
#     except Exception as e:
#         await update_tenant_status(data.tenant_uuid, TenantClinicStatus.FAILED)
