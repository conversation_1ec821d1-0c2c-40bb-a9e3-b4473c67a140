from typing import Annotated

from core.common.api_response import ApiResponse
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase
from fastapi import APIRouter, Request
from fastapi.params import Depends
from services.system_user_service import SystemUserService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.get("/me")
@version(1, 0)
@measure_time
async def get_user_information(
    request: Request,
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    try:
        user_service = SystemUserService(db_session)
        data = await user_service.get_user_information(system_user_id=request.user.id)
        return ApiResponse.success(
            data=data.model_dump(mode="json"),
            message=CustomMessageCode.PRICING_PLAN_STORAGE_UPDATED_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error get_user_information CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error("❌ Error in me endpoint: {}".format(str(e)))
        return ApiResponse.error(message=str(e))
