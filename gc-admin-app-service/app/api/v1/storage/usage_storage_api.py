from typing import Annotated, Optional

from configuration.settings import configuration
from core.common.api_response import ApiR<PERSON>ponse
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase
from fastapi import APIRouter, Depends
from services.usage_storage_service import UsageStorageService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common.s3_bucket import S3Client
from gc_dentist_shared.core.constants import LambdaXRequestValue
from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.dependencies.api_verify_key_depend import (
    DependsAPIVerifyKey,
)

router = APIRouter()


@router.post(
    "/tenants/sync",
    summary="[For Lambda] Sync storage usage from S3 to tenants",
    dependencies=[
        DependsAPIVerifyKey(
            configuration.LAMBDA_SECRET_KEY,
            LambdaXRequestValue.LAMBDA_SYNC_STORAGE_USAGE,
        )
    ],
)
@version(1, 0)
@measure_time
async def sync_tenants_storage_usage(
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
    tenant_uuids: Optional[list[str]] = None,
):
    s3_client = await S3Client.get_instance(configuration=configuration)
    service = UsageStorageService(db_session, s3_client)
    result = await service.sync_tenants_storage_usage(tenant_uuids)
    return ApiResponse.success(
        data={"failed_tenant_uuids": result},
        message=CustomMessageCode.SYNC_STORAGE_USAGE_SUCCESS.title,
    )
