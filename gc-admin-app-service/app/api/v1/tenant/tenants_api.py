from typing import Annotated

from core.common.api_response import ApiR<PERSON>ponse
from core.constants import X_TENANT_CLINIC_NO
from core.messages import ERROR_PROCESS, INIT_CLINIC_INPROCESS, CustomMessageCode
from db.db_connection import CentralDatabase
from fastapi import APIRouter, Depends, Request, status
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi_pagination import Page
from schemas.requests.tenant_clinic_schema import UpdateClinicInfoSchema
from schemas.responses.tenant_clinic_schema import TenantInfoResponseSchema
from schemas.tenant_clinic_requests import (
    CreateClinicInfoSchema,
    CreateTenantSchema,
    MigrationTenantPayloads,
)
from services.migration_service import (
    create_tenant_stream,
    migration_multi_database_stream,
    process_migrate_tenant,
)
from services.tenant_clinics_service import TenantClinicService
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy_utils import create_database, database_exists

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post("")
@version(1, 0)
async def create_tenant(
    data: CreateTenantSchema,
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    db_uri = (
        CentralDatabase()
        .get_url_db_sync(db_name=data.db_name)
        .render_as_string(hide_password=False)
    )

    tenant_service = TenantClinicService(db_session)
    valid_check = await tenant_service.validate_data_create_tenant(data=data)
    if not valid_check:
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={"detail": ERROR_PROCESS},
        )
    try:
        tenant_obj = await tenant_service.create_tenant(data)
        if not database_exists(db_uri):
            log.info(f"🚀 Start Create Database {str(db_uri)}")
            create_database(db_uri)

        await process_migrate_tenant(tenant_obj.tenant_uuid, data)
        await tenant_service.create_tenant_clinic_settings(
            clinic_data=CreateClinicInfoSchema(
                **data.clinic_info.model_dump(),
                clinic_no=data.clinic_no,
                tenant_uuid=str(tenant_obj.tenant_uuid),
                clinic_db_name=data.db_name,
                manager_info=data.manager_info,
            ),
            plan_info=data.plan_info,
            extra_storages_info=data.extra_storages_info,
        )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={"detail": INIT_CLINIC_INPROCESS},
        )
    except Exception as e:
        log.error("❌ Create New DataBase Tenant Error: {}".format(str(e)))
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={"detail": ERROR_PROCESS},
        )


@router.post("/streaming", response_class=StreamingResponse)
@version(1, 0)
async def create_tenant_streaming(  # noqa: ASYNC124
    data: CreateTenantSchema,
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    stream = create_tenant_stream(data, db_session)
    return StreamingResponse(stream, media_type="text/plain")


@router.post("/migration/streaming", response_class=StreamingResponse)
@version(1, 0)
async def migration_tenant_streaming(  # noqa: ASYNC124
    data: MigrationTenantPayloads,
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    stream = migration_multi_database_stream(data)
    return StreamingResponse(stream, media_type="text/plain")


@router.get("/clinic-no", dependencies=[])
@version(1, 0)
async def check_clinic_no(
    request: Request,
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    clinic_no = request.headers.get(X_TENANT_CLINIC_NO)
    clinic_no = clinic_no.strip() if clinic_no else None
    if not clinic_no:
        return ApiResponse.error(
            message=CustomMessageCode.X_TENANT_CLINIC_NO_IS_REQUIRED.title,
            message_code=CustomMessageCode.X_TENANT_CLINIC_NO_IS_REQUIRED.code,
        )
    try:
        clinic_service = TenantClinicService(db_session)
        if await clinic_service.check_clinic_no_exists(clinic_no):
            return ApiResponse.success(
                data={"slug": clinic_no, "exists": True},
            )
    except Exception as e:
        log.error("❌ Get Clinic Slugs Error: {}".format(str(e)))

    return ApiResponse.error(
        data={"slug": clinic_no, "exists": False},
        message=CustomMessageCode.CLINIC_NOT_FOUND.title,
    )


@router.post("/retry/step-create-error", dependencies=[])
@version(1, 0)
async def retry_step_error(
    request: Request,
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    clinic_no = request.headers.get(X_TENANT_CLINIC_NO, "").strip()
    if not clinic_no:
        return ApiResponse.error(
            message=CustomMessageCode.X_TENANT_CLINIC_NO_IS_REQUIRED.title,
            message_code=CustomMessageCode.X_TENANT_CLINIC_NO_IS_REQUIRED.code,
        )
    try:
        clinic_service = TenantClinicService(db_session)
        await clinic_service.retry_step_create_tenant_error(clinic_no)
        return ApiResponse.success()
    except CustomValueError as e:
        log.error("❌ Retry Step Error Migration Tenant Error: {}".format(str(e)))
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error("❌ Retry Step Error Migration Tenant Error: {}".format(str(e)))
        return ApiResponse.error(
            message=ERROR_PROCESS,
        )


@router.get(
    "",
    response_model=Page[TenantInfoResponseSchema],
    description="Search tenant clinics",
)
@version(1, 0)
async def search_tenant_clinics(
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
    search: str | None = None,
):
    try:
        clinic_service = TenantClinicService(db_session)
        result = await clinic_service.search_tenant_clinics(search=search)
        return ApiResponse.success(data=result.model_dump(mode="json"))
    except CustomValueError as e:
        log.error("❌ Search Tenant Clinics Error: {}".format(str(e)))
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error("❌ Search Tenant Clinics Error: {}".format(str(e)))
        return ApiResponse.error(
            message=CustomMessageCode.SEARCH_TENANT_CLINIC_FAILED.title,
        )


@router.get(
    "/{tenant_uuid}",
    response_model=TenantInfoResponseSchema,
    description="Get tenant clinic info",
)
@version(1, 0)
async def get_tenant_info(
    tenant_uuid: str,
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    try:
        clinic_service = TenantClinicService(db_session)
        result = await clinic_service.get_tenant_clinic(tenant_uuid)
        return ApiResponse.success(data=result.model_dump(mode="json"))
    except CustomValueError as e:
        log.error("❌ Get Tenant Info Error: {}".format(str(e)))
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error("❌ Get Tenant Info Error: {}".format(str(e)))
        return ApiResponse.error(
            message=CustomMessageCode.CLINIC_INFO_NOT_FOUND.title,
        )


@router.put("/{tenant_uuid}", description="Update tenant clinic")
@version(1, 0)
async def update_tenant_clinics(
    tenant_uuid: str,
    payload: UpdateClinicInfoSchema,
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    try:
        clinic_service = TenantClinicService(db_session)
        await clinic_service.update_tenant_clinic(tenant_uuid, payload)
        return ApiResponse.success(
            message=CustomMessageCode.UPDATE_TENANT_CLINIC_SUCCESS.title
        )
    except CustomValueError as e:
        log.error("❌ Update Tenant Clinic Error: {}".format(str(e)))
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error("❌ Update Tenant Clinic Error: {}".format(str(e)))
        return ApiResponse.error(
            message=CustomMessageCode.UPDATE_TENANT_CLINIC_FAILED.title,
        )
