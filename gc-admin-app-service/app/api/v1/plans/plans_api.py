from typing import Annotated

from configuration.settings import configuration
from core.common.api_response import Api<PERSON><PERSON>ponse
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase
from fastapi import APIRouter, Path, status
from fastapi.params import Depends
from services.plans_service import PlansService
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.responses import JSONResponse

from gc_dentist_shared.core.common.redis import RedisCli
from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.put("/{plan_key_id:int}")
@version(1, 0)
@measure_time
async def update_plan_storage(
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
    plan_key_id: Annotated[
        int,
        Path(
            ...,
            description="Plan Key ID",
            ge=1,
            example=2,
        ),
    ],
    tenant_uuid: str,
):
    try:
        redis_cli = await RedisCli.get_instance(configuration=configuration)
        service = PlansService(db_session, redis_cli)
        result = await service.update_plan_storage(plan_key_id, tenant_uuid)
        return ApiResponse.success(
            data=result.id,
            message=CustomMessageCode.PRICING_PLAN_STORAGE_UPDATED_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error update_plan_storage CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error("❌ Update Plan Storage Error: {}".format(str(e)))
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "detail": CustomMessageCode.PRICING_PLAN_STORAGE_UPDATED_FAILED.title
            },
        )
