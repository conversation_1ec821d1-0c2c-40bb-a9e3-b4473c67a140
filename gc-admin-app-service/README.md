
### 1.8 Internationalization

Get pybabel tools directly within your FastAPI project without hassle.


- Add your own language locale directory, for instance ja_<PERSON>.
    ```sh
    PYTHONPATH=$PYTHONPATH:$(pwd) python core/common/babel_cli.py init -l ja_JP
    PYTHONPATH=$PYTHONPATH:$(pwd) python core/common/babel_cli.py init -l en_US
    ```

- Extract messages with following command
    ```sh
    cd gc-admin-app-service/app
    PYTHONPATH=$PYTHONPATH:$(pwd) python core/common/babel_cli.py extract -d .
    ```
    => When you have "venv" forder
    ```sh
    PYTHONPATH=$PYTHONPATH:$(pwd) python core/common/babel_cli.py extract -d . --ignore-dirs venv
    ```

- If you have already extracted messages and you have an existing `.po` and `.mo` file.
    ```sh
    PYTHONPATH=$PYTHONPATH:$(pwd) python core/common/babel_cli.py update
    ```
    
- Run mapping default language
    ```sh
    python core/common/extract_default_translation.py
    ```

- Compile Message
    ```sh
    PYTHONPATH=$PYTHONPATH:$(pwd) python core/common/babel_cli.py compile -l ja_JP
    PYTHONPATH=$PYTHONPATH:$(pwd) python core/common/babel_cli.py compile -l en_US
    ```

- Lib handle Image Heif
  Mac OS
  ```sh
    brew install libheif
  ```
  
  Debian/Ubuntu
  ```sh
    sudo apt-get update
    sudo apt-get install -y libheif1 libde265-0
  ```

### 2.0 Squash migrations File
1. create database tmp_migrate
2. deleted all version migration
3. config environment database to database tmp_migrate
4. reset alembic revision : ``` delete FROM alembic_version; ```

5. run generate new migrations 
```
  alembic -c alembic_admin.ini  -x dev=true revision --autogenerate -m "release_v1"
```
6. config environment switch to main database
7. get revision of release_v1
8. set alembic revision number
```
  alembic -c alembic_admin.ini stamp ac2f5a18afdf (step7) 
  alembic -c alembic_admin.ini upgrade head 
```
