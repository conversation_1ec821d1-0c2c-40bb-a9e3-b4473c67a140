repos:
  # format Python code
  - repo: https://github.com/psf/black
    rev: 24.4.2
    hooks:
      - id: black
  # Analyze Python code
  - repo: https://github.com/PyCQA/flake8
    rev: 7.0.0
    hooks:
      - id: flake8
        additional_dependencies: [flake8-async]
  # sort imports
  - repo: https://github.com/pre-commit/mirrors-isort
    rev: ''
    hooks:
      - id: isort
  - repo: https://github.com/PyCQA/autoflake
    rev: v2.3.1
    hooks:
      - id: autoflake
        args: ["--in-place", "--remove-unused-variables", "--remove-all-unused-imports"]
  - repo: https://github.com/codespell-project/codespell
    rev: v2.2.6
    hooks:
      - id: codespell
  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.4.4
    hooks:
      - id: ruff
        args: ["--select", "E,F,UP,S,B,N,RUF", "--ignore", "N802,N803,S101,S105,B904,<PERSON><PERSON><PERSON>010,UP035,UP032,RUF100,S311"]
  
  # custom hooks
  - repo: local
    hooks:
      - id: no-eval
        name: Disallow eval()
        entry: python pre_commit_scripts/check_eval.py
        language: system
        types: [python]

      - id: no-nested-control-flow
        name: Disallow nested if/for/while (depth > 1)
        entry: python pre_commit_scripts/check_nesting.py
        language: system
        types: [python]
        args: ["--max-if", "3"]

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.6.4
    hooks:
      - id: ruff
        args: ["--select=T201"]
  # check static types
  # - repo: https://github.com/pre-commit/mirrors-mypy
  #   rev: v1.10.0
  #   hooks:
  #     - id: mypy
  #       additional_dependencies: ["fastapi", "pydantic"]
  # check for security issues
  
  # detect secrets in code
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        # args: ["--skip", "B311"]
  
  # - repo: https://github.com/PyCQA/bandit
  #   rev: 1.7.5
  #   hooks:
  #     - id: bandit
  #       args: ["--skip", "B101,B601,B603,B404"]  # Skip the B311 rule (hardcoded passwords)
