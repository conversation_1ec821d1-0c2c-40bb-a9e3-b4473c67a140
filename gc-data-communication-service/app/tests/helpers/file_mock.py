import io

from PIL import Image


def create_mock_image_file(
    filename: str, content_type: str
) -> tuple[str, io.BytesIO, str]:
    """Helper to create an in-memory image file tuple for testing."""
    return (filename, io.BytesIO(b"fake_image_bytes"), content_type)


def create_dummy_image_bytes(
    width: int = 1024, height: int = 768, fmt: str = "JPEG"
) -> bytes:
    """
    Creates a dummy image of a given size and returns its byte content.
    """
    img = Image.new("RGB", (width, height), color="black")
    buffer = io.BytesIO()
    img.save(buffer, format=fmt)
    return buffer.getvalue()
