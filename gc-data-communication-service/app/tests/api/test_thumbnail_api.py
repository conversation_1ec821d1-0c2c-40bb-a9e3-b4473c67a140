from unittest.mock import AsyncMock
from uuid import uuid4

import pytest
from sqlalchemy.future import select
from tests.helpers.file_mock import create_dummy_image_bytes

from gc_dentist_shared.central_models import ThumbnailGenerationLog
from gc_dentist_shared.core.enums.thumbnail_generation_logs import ThumbnailStatus


@pytest.mark.asyncio
async def test_generate_thumbnail_success(
    async_client,
    bypass_api_key_check,
    mocker,
    async_central_db_session_object,
):
    """
    Tests the successful end-to-end generation of a thumbnail.
    """
    unique_filename = f"some_image_{uuid4()}.jpg"
    original_image_path = f"tenant-uuid/patient/123/documents/main/{unique_filename}"
    expected_thumbnail_path = f"tenant-uuid/patient/123/documents/sub/{unique_filename}"

    api_payload = {
        "original_image_path": original_image_path,
        "thumbnail_width": 128,
        "thumbnail_height": 128,
        "quality": 80,
    }

    # 2. Mock S3 Client
    mock_s3_client = AsyncMock()
    dummy_image_content = create_dummy_image_bytes()

    mock_s3_client.get_object.return_value = dummy_image_content
    mock_s3_client.upload_file = AsyncMock()
    mocker.patch(
        "services.thumbnail_service.S3Client.get_instance", return_value=mock_s3_client
    )

    response = await async_client.post("/v1_0/thumbnails", json=api_payload)

    # ----- ASSERT -----
    # 1. Assert API Response
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["data"]["thumbnail_image_path"] == expected_thumbnail_path

    # 2. Assert S3 Client Calls
    mock_s3_client.get_object.assert_called_once_with(key=original_image_path)
    mock_s3_client.upload_file.assert_called_once()

    upload_args = mock_s3_client.upload_file.call_args.kwargs
    assert upload_args["object_name"] == expected_thumbnail_path
    assert upload_args["content_type"] == "image/webp"
    assert isinstance(upload_args["file"], bytes)

    # 3. Assert Database State
    async with async_central_db_session_object:
        query = select(ThumbnailGenerationLog).where(
            ThumbnailGenerationLog.original_image_path == original_image_path
        )
        result = await async_central_db_session_object.execute(query)
        log_entry = result.scalar_one()

        assert log_entry is not None
        assert log_entry.status == ThumbnailStatus.SUCCESS.value
        assert log_entry.thumbnail_image_path == expected_thumbnail_path


@pytest.mark.asyncio
async def test_generate_thumbnail_s3_download_fails(
    async_client,
    bypass_api_key_check,
    mocker,
    async_central_db_session_object,
):
    """
    Tests failure when the original image download from S3 fails.
    The API should return a 400 error, and the log should be marked as FAILED.
    """
    # Arrange
    original_image_path = f"path/to/non_existent_image_{uuid4()}.jpg"
    api_payload = {"original_image_path": original_image_path}

    # Mock S3 client to raise an exception on get_object
    mock_s3_client = AsyncMock()
    error_message = "S3 GetObject failed: Key does not exist"
    mock_s3_client.get_object.side_effect = Exception(error_message)
    mocker.patch(
        "services.thumbnail_service.S3Client.get_instance", return_value=mock_s3_client
    )

    # Act
    response = await async_client.post("/v1_0/thumbnails", json=api_payload)

    # Assert: API Response
    assert response.status_code == 200

    # Assert: Database State
    async with async_central_db_session_object.begin():
        query = select(ThumbnailGenerationLog).where(
            ThumbnailGenerationLog.original_image_path == original_image_path
        )
        log_entry = (await async_central_db_session_object.execute(query)).scalar_one()

        assert log_entry.status == ThumbnailStatus.FAILED.value
        assert error_message in log_entry.error_message


@pytest.mark.asyncio
async def test_generate_thumbnail_image_processing_fails(
    async_client,
    bypass_api_key_check,
    mocker,
    async_central_db_session_object,
):
    """
    Tests failure when the downloaded file is not a valid image.
    """
    # Arrange
    original_image_path = f"path/to/corrupted_image_{uuid4()}.jpg"
    api_payload = {"original_image_path": original_image_path}

    # Mock S3 client to return corrupted/invalid image bytes
    mock_s3_client = AsyncMock()
    mock_s3_client.get_object.return_value = b"this is not a valid image file"
    mocker.patch(
        "services.thumbnail_service.S3Client.get_instance", return_value=mock_s3_client
    )

    # Act
    response = await async_client.post("/v1_0/thumbnails", json=api_payload)

    # Assert: API Response
    assert response.status_code == 200

    # Assert: Database State
    async with async_central_db_session_object.begin():
        query = select(ThumbnailGenerationLog).where(
            ThumbnailGenerationLog.original_image_path == original_image_path
        )
        log_entry = (await async_central_db_session_object.execute(query)).scalar_one()

        assert log_entry.status == ThumbnailStatus.FAILED.value
        # Check for a typical Pillow error message
        assert "cannot identify image file" in log_entry.error_message


@pytest.mark.asyncio
async def test_generate_thumbnail_s3_upload_fails(
    async_client,
    bypass_api_key_check,
    mocker,
    async_central_db_session_object,
):
    """
    Tests failure when the thumbnail upload to S3 fails.
    """
    # Arrange
    original_image_path = f"path/to/valid_image_{uuid4()}.jpg"
    api_payload = {"original_image_path": original_image_path}

    # Mock S3 client
    mock_s3_client = AsyncMock()
    # get_object succeeds
    mock_s3_client.get_object.return_value = create_dummy_image_bytes()

    # upload_file fails
    error_message = "S3 PutObject failed: Access Denied"
    mock_s3_client.upload_file.side_effect = Exception(error_message)
    mocker.patch(
        "services.thumbnail_service.S3Client.get_instance", return_value=mock_s3_client
    )

    # Act
    response = await async_client.post("/v1_0/thumbnails", json=api_payload)

    # Assert: API Response
    assert response.status_code == 200

    # Assert: Database State
    async with async_central_db_session_object.begin():
        query = select(ThumbnailGenerationLog).where(
            ThumbnailGenerationLog.original_image_path == original_image_path
        )
        log_entry = (await async_central_db_session_object.execute(query)).scalar_one()

        assert log_entry.status == ThumbnailStatus.FAILED.value
        assert error_message in log_entry.error_message


@pytest.mark.asyncio
async def test_generate_thumbnail_invalid_payload_422(
    async_client,
    bypass_api_key_check,
):
    """
    Tests for a 422 Unprocessable Entity error for a malformed payload.
    """
    # Arrange
    invalid_payload = {"thumbnail_width": 100}

    # Act
    response = await async_client.post("/v1_0/thumbnails", json=invalid_payload)

    # Assert
    assert response.status_code == 422
    error_detail = response.json()["data"][0]
    assert "original_image_path" in error_detail["loc"]
    assert error_detail["type"] == "missing"


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "invalid_quality, expected_msg_part",
    [
        (0, "Input should be greater than 0"),
        (101, "Input should be less than or equal to 100"),
    ],
)
async def test_generate_thumbnail_invalid_quality_422(
    async_client,
    bypass_api_key_check,
    invalid_quality,
    expected_msg_part,
):
    """
    Tests for a 422 error when the 'quality' parameter is outside the valid range (1-100).
    """
    # Arrange
    api_payload = {
        "original_image_path": "some/path/image.jpg",
        "quality": invalid_quality,
    }

    # Act
    response = await async_client.post("/v1_0/thumbnails", json=api_payload)

    # Assert
    assert response.status_code == 422
    error_detail = response.json()["data"][0]

    assert "quality" in error_detail["loc"]
    assert expected_msg_part in error_detail["msg"]
