from api.health_check import router as health_check_router
from api.v1.instrumental_examinations.instrumental_examinations_api import (
    router as instrumental_examinations_api,
)
from api.v1.oral_examination.intraoral_examination_api import (
    router as intraoral_examination_api,
)
from api.v1.patients.patients_api import router as patients_router
from api.v1.s3_api import router as s3_router

# from fastapi import APIRouter
from core.api_version_router import VersionedAPIRouter

router = VersionedAPIRouter()

router.include_router(
    intraoral_examination_api,
    prefix="/intraoral-examinations",
    tags=["Oral Examinations"],
)
router.include_router(
    instrumental_examinations_api,
    prefix="/instrumental-examinations",
    tags=["Instrumental Examinations"],
)
router.include_router(health_check_router)
router.include_router(patients_router, prefix="/patients", tags=["Patients"])
router.include_router(s3_router, tags=["S3"])
