from typing import Annotated

from configuration.settings import configuration
from core.common.api_response import Api<PERSON><PERSON>ponse
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase
from fastapi import APIRouter, Depends
from schemas.requests.thumbnail_schemas import CreateThumbnailPayload
from services.thumbnail_service import ThumbnailService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common.s3_bucket import S3Client
from gc_dentist_shared.core.constants import LambdaXRequestValue
from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.dependencies.api_verify_key_depend import (
    DependsAPIVerifyKey,
)
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post(
    path="",
    summary="[For Lambda] Create a thumbnail for a given S3 image",
    dependencies=[
        DependsAPIVerifyKey(
            configuration.LAMBDA_SECRET_KEY,
            LambdaXRequestValue.LAMBDA_CREATE_THUMBNAIL,
        )
    ],
)
@version(1, 0)
@measure_time
async def generate_thumbnail(
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
    obj_request: CreateThumbnailPayload,
):
    try:
        s3_client = await S3Client.get_instance(configuration=configuration)
        thumbnail_service = ThumbnailService(db_session, s3_client)
        thumbnail_image_path = await thumbnail_service.generate_thumbnail(
            obj_request=obj_request
        )

        return ApiResponse.success(
            data={"thumbnail_image_path": thumbnail_image_path},
            message=CustomMessageCode.THUMBNAIL_CREATION_SUCCESS.title,
            message_code=CustomMessageCode.THUMBNAIL_CREATION_SUCCESS.code,
        )
    except Exception as e:
        log.error(f"❌ generate_thumbnail error: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.THUMBNAIL_CREATION_FAILED.title,
            message_code=CustomMessageCode.THUMBNAIL_CREATION_FAILED.code,
        )
