from typing import Annotated

from core.common.api_response import ApiResponse
from core.constants import X_TENANT_CLINIC_CODE
from core.dependencies.api_verify_key_depend import DependsAPIMiddlewareVerifyKey
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase
from fastapi import APIRouter, Depends, Request  # type: ignore
from schemas.requests.instrumental_examination_schema import (
    CreatePatientInstrumentalExaminationRequest,
)
from services.examination.instrumental_examination import InstrumentalExaminationService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post(
    "",
    dependencies=[DependsAPIMiddlewareVerifyKey()],
    summary="Create Patient Instrumental Examination Information",
)
@version(1, 0)
@measure_time
async def create_patient_examination_information(
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
    request: Request,
    payload: CreatePatientInstrumentalExaminationRequest,
):
    try:
        service = InstrumentalExaminationService(db_session)
        # TODO need confirm
        clinic_code = request.headers.get(X_TENANT_CLINIC_CODE)
        if not clinic_code:
            raise CustomValueError(
                message_code=CustomMessageCode.X_TENANT_CLINIC_CODE_IS_REQUIRED.code,
                message=CustomMessageCode.X_TENANT_CLINIC_CODE_IS_REQUIRED.title,
            )

        await service.create_patient_instrumental_examination(clinic_code, payload)
        return ApiResponse.success(
            message=CustomMessageCode.PATIENT_INSTRUMENTAL_EXAMINATION_CREATED_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error create_patient_medical_device CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error create_patient_medical_device: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.PATIENT_INSTRUMENTAL_EXAMINATION_CREATED_FAILED.title,
            message_code=CustomMessageCode.PATIENT_INSTRUMENTAL_EXAMINATION_CREATED_FAILED.code,
        )
