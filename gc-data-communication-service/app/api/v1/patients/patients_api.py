from typing import Annotated

from configuration.settings import configuration
from core.common.api_response import ApiR<PERSON>ponse
from core.dependencies.api_verify_key_depend import DependsAPIMiddlewareVerifyKey
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase
from fastapi import APIRouter, Depends
from schemas.requests.patient_requests import (
    ImportTreatmentImagePayload,
    RegisterPatientRequest,
)
from services.patient_service import PatientService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common.s3_bucket import S3Client
from gc_dentist_shared.core.constants import LambdaXRequestValue
from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.dependencies.api_verify_key_depend import (
    DependsAPIVerifyKey,
)
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post(
    "/register",
    dependencies=[DependsAPIMiddlewareVerifyKey()],
    summary="Register patient information",
)
@version(1, 0)
async def single_register_patient(
    central_db_session: Annotated[
        AsyncSession, Depends(CentralDatabase.get_db_session)
    ],
    request_data: RegisterPatientRequest,
):
    try:
        patient_service = PatientService(
            central_db_session=central_db_session,
        )
        await patient_service.register_patient(request_data)
        return ApiResponse.success(
            message=CustomMessageCode.REGISTER_PATIENT_SUCCESS.title,
            message_code=CustomMessageCode.REGISTER_PATIENT_SUCCESS.code,
        )

    except CustomValueError as e:
        log.error(f"❌ Error register patient information: {str(e)}")
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )

    except Exception as e:
        log.error(f"❌ Error register patient information: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.REGISTER_PATIENT_FAILED.title,
            message_code=CustomMessageCode.REGISTER_PATIENT_FAILED.code,
        )


@router.get(
    "/",
    dependencies=[DependsAPIMiddlewareVerifyKey()],
    summary="Search patient by clinic code and patient number",
)
@version(1, 0)
async def search_patient(
    central_db_session: Annotated[
        AsyncSession, Depends(CentralDatabase.get_db_session)
    ],
    clinic_code: str,
    patient_number: str,
):
    try:
        patient_service = PatientService(
            central_db_session=central_db_session,
        )
        response = await patient_service.search_patient(
            clinic_code=clinic_code, patient_number=patient_number
        )
        return ApiResponse.success(
            data=response.model_dump(),
            message=CustomMessageCode.SEARCH_PATIENT_SUCCESS.title,
            message_code=CustomMessageCode.SEARCH_PATIENT_SUCCESS.code,
        )

    except CustomValueError as e:
        log.error(f"❌ Search patient error: {str(e)}")
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )

    except Exception as e:
        log.error(f"❌ Search patient error: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.SEARCH_PATIENT_FAILED.title,
            message_code=CustomMessageCode.SEARCH_PATIENT_FAILED.code,
        )


@router.post(
    path="/treatment-images",
    summary="Api import treatment images",
    dependencies=[
        DependsAPIVerifyKey(
            configuration.LAMBDA_SECRET_KEY,
            LambdaXRequestValue.LAMBDA_IMPORT_TREATMENT_IMAGE_PATIENT,
        )
    ],
)
@version(1, 0)
@measure_time
async def import_treatment_images(
    central_db_session: Annotated[
        AsyncSession, Depends(CentralDatabase.get_db_session)
    ],
    payload: ImportTreatmentImagePayload,
):
    try:
        s3_client = await S3Client.get_instance(configuration=configuration)
        patient_service = PatientService(
            central_db_session=central_db_session,
            s3_client=s3_client,
        )
        patient_no_data = await patient_service.import_treatment_images(payload=payload)
        return ApiResponse.success(
            data=patient_no_data,
            message=CustomMessageCode.IMPORT_TREATMENT_IMAGE_PATIENT_SUCCESS.title,
            message_code=CustomMessageCode.IMPORT_TREATMENT_IMAGE_PATIENT_SUCCESS.code,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error import_treatment_images CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error("❌ import_treatment_images error: {}".format(str(e)))
        return ApiResponse.error(
            message=CustomMessageCode.IMPORT_TREATMENT_IMAGE_PATIENT_ERROR.title,
            message_code=CustomMessageCode.IMPORT_TREATMENT_IMAGE_PATIENT_ERROR.code,
        )
