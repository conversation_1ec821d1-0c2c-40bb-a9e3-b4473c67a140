from core.common.api_response import ApiResponse
from core.dependencies.api_verify_key_depend import DependsAPIMiddlewareVerifyKey
from core.messages import CustomMessageCode
from fastapi import APIRouter
from schemas.requests.s3_schemas import S3GeneratedPresignedUrlRequest
from services.s3_service import S3Service

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.exception_handler.custom_exception import (
    CustomValueError,
    S3BucketExceptionError,
)
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post(
    path="/generate-presigned-url",
    dependencies=[DependsAPIMiddlewareVerifyKey()],
    summary="Generate a presigned URL for S3 object access",
)
@version(1, 0)
async def generate_presigned_url(
    obj: S3GeneratedPresignedUrlRequest,
):
    """
    Generate a presigned URL for S3 object access.
    """
    try:
        service = S3Service()
        presigned_urls = await service.generate_presigned_url(obj=obj)
        return ApiResponse.success(
            data=presigned_urls,
        )
    except (S3BucketExceptionError, CustomValueError) as e:
        log.error(f"❌ Error: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error API generating presigned URL: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
        )
