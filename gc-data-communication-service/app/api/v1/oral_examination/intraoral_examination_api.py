from typing import Annotated

from core.common.api_response import ApiResponse
from core.dependencies.api_verify_key_depend import DependsAPIMiddlewareVerifyKey
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabaseReadOnly
from fastapi import APIRouter, Depends, status
from fastapi_pagination import Page
from schemas.requests.oral_examination_schema import (
    IntraoralExaminationDetailRequest,
    IntraoralExaminationRequest,
)
from schemas.responses.oral_examination_schema import IntraoralExaminationResponse
from services.examination.intraoral_examination_service import (
    IntraoralExaminationService,
)
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.get(
    path="",
    dependencies=[DependsAPIMiddlewareVerifyKey()],
    summary="Get list of intraoral examinations",
)
@version(1, 0)
@measure_time
async def get_intraoral_examinations(
    db_session: Annotated[
        AsyncSession, Depends(CentralDatabaseReadOnly.get_db_session)
    ],
    request_data: IntraoralExaminationRequest = Depends(),  # noqa: B008
) -> Page[IntraoralExaminationResponse]:
    try:
        service = IntraoralExaminationService(db_session)
        result = await service.get_intraoral_examinations(request_data)
        return ApiResponse.success(
            data=result.model_dump(mode="json"),
            message=CustomMessageCode.INTRAORAL_EXAMINATION_GET_LIST_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(f"❌ CustomValueError intraoral-examination: {e.message}")
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Exception intraoral-examination: {str(e)}")
        return ApiResponse.error(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=CustomMessageCode.INTRAORAL_GET_LIST_FAILED.title,
            message_code=CustomMessageCode.INTRAORAL_GET_LIST_FAILED.code,
        )


@router.get(
    path="/detail",
    dependencies=[DependsAPIMiddlewareVerifyKey()],
    summary="Get intraoral examination detail by id or latest",
)
@version(1, 0)
@measure_time
async def get_intraoral_examination(
    db_session: Annotated[
        AsyncSession, Depends(CentralDatabaseReadOnly.get_db_session)
    ],
    request_data: IntraoralExaminationDetailRequest = Depends(),  # noqa: B008
):
    try:
        service = IntraoralExaminationService(db_session)
        result = await service.get_intraoral_examination_detail(request_data)
        return ApiResponse.success(
            data=result,
            message=CustomMessageCode.INTRAORAL_EXAMINATION_GET_DETAIL_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(f"❌ CustomValueError intraoral-examination: {e.message}")
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Exception intraoral-examination: {str(e)}")
        return ApiResponse.error(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=CustomMessageCode.INTRAORAL_GET_DETAIL_FAILED.title,
            message_code=CustomMessageCode.INTRAORAL_GET_DETAIL_FAILED.code,
        )
