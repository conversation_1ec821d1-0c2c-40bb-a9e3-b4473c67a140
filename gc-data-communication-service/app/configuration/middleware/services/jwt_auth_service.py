from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from db.db_connection import CentralDatabase, TenantDatabase
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import OAuth2Token, TenantClinic
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import Doctor<PERSON><PERSON>, DoctorUser


class JwtAuthService:
    """
    Service for handling JWT authentication.
    """

    async def get_db_name_by_tenant_uuid(
        self, central_session: AsyncSession, tenant_uuid: str
    ):
        """
        Get the database name associated with a tenant UUID.
        """
        query = select(TenantClinic.db_name).where(
            TenantClinic.tenant_uuid == tenant_uuid
        )
        result = await central_session.execute(query)
        db_name = result.scalar_one_or_none()

        if not db_name:
            log.error(f"❌ Database name not found for tenant UUID: {tenant_uuid}")
            return None
        return db_name

    async def validate_token(
        self,
        central_session: AsyncSession,
        access_token: str,
        user_id: int,
        tenant_uuid: str,
    ):
        """
        Validate the JWT token and return the decoded claims.
        """
        try:
            conditions = [
                OAuth2Token.access_token == access_token,
                OAuth2Token.clinic_doctor_id == user_id,
                OAuth2Token.tenant_uuid == tenant_uuid,
            ]
            query = select(OAuth2Token.token_uuid).where(*conditions)
            result = await central_session.execute(query)
            token_uuid = result.scalar_one_or_none()

            if not token_uuid:
                log.error("❌ JWT token not found in the database")
                return None
            return token_uuid
        except Exception as e:
            log.error(f"❌ Error validating JWT token: {e}")
            return None

    async def validate_auth(
        self,
        access_token: str,
        role_key_ids: list[int],
        user_id: int,
        tenant_uuid: str,
    ):
        """
        Validate the role ID against the tenant UUID.
        """
        try:
            async with CentralDatabase().get_instance_db() as central_session:
                async with central_session.begin():
                    token_uuid = await self.validate_token(
                        central_session, access_token, user_id, tenant_uuid
                    )
                    if not token_uuid:
                        return None

                    db_name = await self.get_db_name_by_tenant_uuid(
                        central_session, tenant_uuid
                    )
                    if not db_name:
                        return None

            token_db = set_current_db_name(db_name)
            try:
                async with TenantDatabase.get_instance_tenant_db() as tenant_session:
                    async with tenant_session.begin():
                        result = await tenant_session.execute(
                            select(DoctorUser).where(
                                DoctorUser.id == user_id,
                                DoctorUser.status.is_(True),
                            )
                        )
                        doctor_user = result.scalar_one_or_none()
                        if not doctor_user:
                            log.error("❌ User not found or inactive")
                            return None

                        doctor_role_result = await tenant_session.execute(
                            select(DoctorRole.role_key_id).where(
                                DoctorRole.role_key_id.in_(role_key_ids),
                                DoctorRole.doctor_user_id == user_id,
                                DoctorRole.delete_flag.is_(False),
                            )
                        )
                        doctor_role_key_ids = doctor_role_result.scalars().all()
                        if not doctor_role_key_ids:
                            log.error("❌ Doctor role not found or inactive")
                            return None

                    return doctor_user
            except Exception as e:
                log.error(f"❌ Error setting current database name: {e}")
                return None
            finally:
                reset_current_db_name(token_db)

        except Exception as e:
            log.error(f"❌ Error validating role ID: {e}")
            return None
