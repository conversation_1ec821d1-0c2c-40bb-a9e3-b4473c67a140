from config.logger.config import log
from fastapi import Request
from opentelemetry import trace
from opentelemetry.trace import SpanKind
from starlette.middleware.base import BaseHTTPMiddleware


class OpenTelemetryLogMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        tracer = trace.get_tracer(__name__)

        with tracer.start_as_current_span(
            request.url.path, kind=SpanKind.SERVER
        ) as span:
            response = await call_next(request)

            log.extra_info = {
                "trace_id": format(span.get_span_context().trace_id, "032x"),
            }
            log.info(f"Processed request: {request.url.path}")

            return response
