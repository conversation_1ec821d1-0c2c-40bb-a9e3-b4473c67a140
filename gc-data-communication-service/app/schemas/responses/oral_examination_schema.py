from typing import Optional

from pydantic import BaseModel, Field

from gc_dentist_shared.core.common.timezone import Timestamp


class IntraoralExaminationResponse(BaseModel):
    """Response schema for Intraoral Examination data."""

    id: int = Field(..., description="ID of the oral examination")
    examination_date: Timestamp = Field(..., description="Date of the examination")
    note: Optional[str] = Field(None, description="Note for the oral examination")
