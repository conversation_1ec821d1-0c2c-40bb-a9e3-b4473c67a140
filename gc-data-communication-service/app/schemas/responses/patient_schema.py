import phonenumbers
from configuration.settings import configuration
from core.constants import PATIENT_GENDER_MAPPING
from pydantic import BaseModel, field_serializer, field_validator, model_validator

from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.common.strings import format_phone_number


class PatientProfileItemSchema(BaseModel):
    patient_number: str
    full_name: str
    full_name_kana: str
    gender: str | None = None
    date_of_birth: str
    telephone_number_1: str | None = None
    telephone_number_2: str | None = None
    postal_code: str | None = None
    address: str | None = None

    @field_serializer("telephone_number_1", "telephone_number_2")
    def serialize_phone_number(self, v):
        if not v:
            return None

        try:
            return format_phone_number(
                phone=v, num_format=phonenumbers.PhoneNumberFormat.NATIONAL
            )

        except Exception:
            return v

    @field_serializer("postal_code")
    def serialize_postal_code(self, v: str) -> str:
        if v and len(v) == 7:
            return f"{v[:3]}-{v[3:]}"
        return v

    @field_validator("gender", mode="before")
    @classmethod
    def convert_gender_from_int(cls, v):
        if not v:
            return None

        for gender_name, gender_value in PATIENT_GENDER_MAPPING.items():
            if gender_value == v:
                return gender_name
        return None

    @model_validator(mode="before")
    @classmethod
    def decrypt_fields(cls, values: dict):
        aes_gcm = AesGCMRotation(configuration)
        patient_fields_encrypted = [
            "telephone_number_1",
            "telephone_number_2",
            "date_of_birth",
        ]
        for field in patient_fields_encrypted:
            val = values.get(field)
            if val:
                values[field] = aes_gcm.decrypt_data(val)
        return values


class PatientProfileSearchResponse(BaseModel):
    items: list[PatientProfileItemSchema] = []
