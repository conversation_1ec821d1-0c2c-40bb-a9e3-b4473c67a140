import re
from datetime import datetime

from core.constants import FILENAME_REGEX, IMAGE_EXTENSIONS
from core.messages import CustomMessageCode
from enums.s3_enums import DataTypeGeneratedPresignedEnums
from pydantic import BaseModel, conlist, model_validator

from gc_dentist_shared.core.enums.medical_device import MedicalDeviceType
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError


class ParsedFileNameInfo(BaseModel):
    original_name: str
    clinic_no: str
    patient_no: str | None = None
    examination_date: str | None = None
    timestamps: str
    ext: str
    device_type: str | None = None


class S3GeneratedPresignedUrlRequest(BaseModel):
    file_names: conlist(str, min_length=1, max_length=10)
    data_type: DataTypeGeneratedPresignedEnums

    parsed_files: list[ParsedFileNameInfo] = list

    @model_validator(mode="after")
    def validate_file_names(self):
        name_lowered = set()
        parsed_list = []
        file_regex = FILENAME_REGEX.get(self.data_type.value)
        for name in self.file_names:
            file_name_pattern = re.compile(file_regex).match(name)
            if not file_name_pattern:
                raise CustomValueError(CustomMessageCode.INVALID_FILENAME_FORMAT.title)

            if self.data_type == DataTypeGeneratedPresignedEnums.IMAGE.value:
                clinic_no, patient_no, exam_date_str, ts_str, device_type, ext = (
                    file_name_pattern.groups()
                )
                ext_lower = ext.lower()

                # Extension
                if ext_lower not in IMAGE_EXTENSIONS:
                    raise CustomValueError(
                        CustomMessageCode.S3_GENERATED_PRESIGNEDURL_INVALID_EXTENSION.title
                    )

                # Examination date
                try:
                    datetime.strptime(exam_date_str, "%Y%m%d")
                except ValueError:
                    raise CustomValueError(
                        CustomMessageCode.INVALID_EXAMINATION_DATE.title
                    )

                # Validate device_type
                if device_type and device_type not in MedicalDeviceType.__members__:
                    raise CustomValueError(CustomMessageCode.INVALID_DEVICE_TYPE.title)
            else:
                clinic_no, ts_str, ext = file_name_pattern.groups()
                patient_no = None
                exam_date_str = None
                device_type = None

            parsed_list.append(
                ParsedFileNameInfo(
                    original_name=name,
                    clinic_no=clinic_no,
                    patient_no=patient_no,
                    examination_date=exam_date_str,
                    timestamps=ts_str,
                    ext=ext.lower(),
                    device_type=device_type,
                )
            )

            name_lowered.add(name.lower())

        # Check duplicate
        if len(name_lowered) != len(self.file_names):
            raise CustomValueError(
                CustomMessageCode.S3_GENERATED_PRESIGNEDURL_DUPLICATE_FILE_NAMES.title
            )
        self.parsed_files = parsed_list
        return self
