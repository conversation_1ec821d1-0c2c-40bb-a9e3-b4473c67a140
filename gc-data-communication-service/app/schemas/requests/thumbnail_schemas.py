from typing import Optional

from pydantic import BaseModel, Field


class CreateThumbnailPayload(BaseModel):
    original_image_path: str
    thumbnail_width: Optional[int] = Field(
        256, description="The desired width for the thumbnail in pixels."
    )
    thumbnail_height: Optional[int] = Field(
        360, description="The desired height for the thumbnail in pixels."
    )
    quality: Optional[int] = Field(
        75, gt=0, le=100, description="Image quality for thumbnail (from 1 to 100)"
    )
