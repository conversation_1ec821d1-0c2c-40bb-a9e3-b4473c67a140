from typing import Optional

from core.messages import CustomMessageCode
from pydantic import BaseModel, ConfigDict, Field, field_validator

from gc_dentist_shared.core.common.utils import ValidateDateString
from gc_dentist_shared.core.enums.medical_device import MedicalDeviceType


class FiDataSchema(BaseModel):
    type: str
    id: str
    patient_no: str
    examination_date: str

    @field_validator("examination_date")
    @classmethod
    def validate_examination_date(cls, v: str) -> str:
        if v is not None:
            ValidateDateString.validate(v)
        return v

    @field_validator("type")
    @classmethod
    def validate_device_type(cls, v: str) -> str:
        if v not in MedicalDeviceType.get_member_values():
            raise ValueError(
                CustomMessageCode.PATIENT_INSTRUMENTAL_EXAMINATION_INVALID_DEVICE_TYPE.title
            )
        return v


class RawDataSchema(BaseModel):
    fi_data: FiDataSchema

    model_config = ConfigDict(extra="allow")


class CreatePatientInstrumentalExaminationRequest(BaseModel):
    raw_data: RawDataSchema = Field(..., description="Raw examination data")

    @property
    def image_file_name(self) -> Optional[str]:
        return getattr(self, "image_file_name", None)
