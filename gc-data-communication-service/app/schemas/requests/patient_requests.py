import os
import re
from datetime import date, datetime
from typing import Optional

from core.constants import (
    CSV_DATE_OF_BIRTH_FORMAT,
    IMAGE_EXTENSIONS,
    IMAGE_FILENAME_REGEX,
    KATAKANA_REGEX,
    NUMERIC_HYPHEN_REGEX,
    PATIENT_GENDER_MAPPING,
)
from core.messages import CustomMessageCode
from enums.patient_enum import PatientColumnMapping
from pydantic import BaseModel, Field, ValidationInfo, field_validator, model_validator

from gc_dentist_shared.core.common.strings import format_phone_number
from gc_dentist_shared.core.enums.medical_device import MedicalDeviceType
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError


class RegisterPatientRequest(BaseModel):
    clinic_code: str
    patient_number: str
    full_name: str
    full_name_kana: str
    gender: Optional[str] = None
    date_of_birth: str
    telephone_number_1: Optional[str] = None
    telephone_number_2: Optional[str] = None
    postal_code: Optional[str] = None
    address: Optional[str] = None

    @field_validator("patient_number", mode="before")
    @classmethod
    def validate_patient_number(cls, v):
        if not v:
            raise ValueError(
                CustomMessageCode.REQUIRED_FIELD_ERROR.title.format(
                    column_name=PatientColumnMapping.PATIENT_NUMBER.column_name
                )
            )

        if len(v) > 30:
            raise ValueError(
                CustomMessageCode.MAX_LENGTH_FIELD_ERROR.title.format(
                    column_name=PatientColumnMapping.PATIENT_NUMBER.column_name,
                    max_length=30,
                )
            )

        return v

    @field_validator("full_name", mode="before")
    @classmethod
    def validate_full_name(cls, v):
        if not v:
            raise ValueError(
                CustomMessageCode.REQUIRED_FIELD_ERROR.title.format(
                    column_name=PatientColumnMapping.PATIENT_NUMBER.column_name
                )
            )

        if len(v) > 60:
            raise ValueError(
                CustomMessageCode.MAX_LENGTH_FIELD_ERROR.title.format(
                    column_name=PatientColumnMapping.FULL_NAME.column_name,
                    max_length=60,
                )
            )

        return v

    @field_validator("full_name_kana", mode="before")
    @classmethod
    def validate_full_name_kana(cls, v):
        if not v:
            raise ValueError(
                CustomMessageCode.REQUIRED_FIELD_ERROR.title.format(
                    column_name=PatientColumnMapping.FULL_NAME_KANA.column_name
                )
            )

        if len(v) > 60:
            raise ValueError(
                CustomMessageCode.MAX_LENGTH_FIELD_ERROR.title.format(
                    column_name=PatientColumnMapping.FULL_NAME_KANA.column_name,
                    max_length=60,
                )
            )

        if not re.match(KATAKANA_REGEX, v):
            raise ValueError(CustomMessageCode.INVALID_KATAKANA_ERROR.title)

        return v

    @field_validator("date_of_birth", mode="before")
    @classmethod
    def validate_date_of_birth(cls, v):
        if not v:
            raise ValueError(
                CustomMessageCode.REQUIRED_FIELD_ERROR.title.format(
                    column_name=PatientColumnMapping.DATE_OF_BIRTH.column_name
                )
            )
        try:
            datetime.strptime(v, CSV_DATE_OF_BIRTH_FORMAT)
        except ValueError:
            raise ValueError(CustomMessageCode.INVALID_DATE_ERROR.title)

        return v

    @field_validator("gender", mode="before")
    @classmethod
    def validate_gender(cls, v):
        if not v:
            return v

        if v not in PATIENT_GENDER_MAPPING.keys():
            raise ValueError(CustomMessageCode.INVALID_GENDER_ERROR.title)

        return v

    @field_validator("telephone_number_1", "telephone_number_2", mode="before")
    @classmethod
    def validate_telephone_numbers(cls, v, info: ValidationInfo):
        if not v:
            return v

        if len(v) > 13:
            raise ValueError(
                CustomMessageCode.MAX_LENGTH_FIELD_ERROR.title.format(
                    column_name=info.field_name,
                    max_length=13,
                )
            )

        if not re.match(NUMERIC_HYPHEN_REGEX, v):
            raise ValueError(
                CustomMessageCode.INVALID_VALUE_ERROR.title.format(
                    column_name=info.field_name
                )
            )

        return format_phone_number(phone=v)

    @field_validator("postal_code", mode="before")
    @classmethod
    def validate_postal_code(cls, v):
        if not v:
            return v

        if len(v) > 8:
            raise ValueError(
                CustomMessageCode.MAX_LENGTH_FIELD_ERROR.title.format(
                    column_name=PatientColumnMapping.POSTAL_CODE.column_name,
                    max_length=8,
                )
            )

        if not re.match(NUMERIC_HYPHEN_REGEX, v):
            raise ValueError(
                CustomMessageCode.INVALID_VALUE_ERROR.title.format(
                    column_name=PatientColumnMapping.POSTAL_CODE.column_name
                )
            )

        return v


class ParsedFileInfo(BaseModel):
    file_name: str
    clinic_no: str
    patient_no: str
    examination_date: date
    timestamps: str
    ext: str
    device_type: str | None = None


class ImportTreatmentImagePayload(BaseModel):
    image_file_path: str

    file_info: Optional[ParsedFileInfo] = Field(default=None)

    @model_validator(mode="after")
    def validate_file_path(self):
        file_name = os.path.basename(self.image_file_path)
        file_name_pattern = re.compile(IMAGE_FILENAME_REGEX).match(file_name)
        if not file_name_pattern:
            raise CustomValueError(
                CustomMessageCode.IMPORT_TREATMENT_IMAGE_INVALID_FORMAT.title
            )

        clinic_no, patient_no, exam_date_str, ts_str, device_type, ext = (
            file_name_pattern.groups()
        )
        ext_lower = ext.lower()
        # Extension
        if ext_lower not in IMAGE_EXTENSIONS:
            raise CustomValueError(
                CustomMessageCode.IMPORT_TREATMENT_IMAGE_INVALID_EXTENSION.title
            )

        # Examination date
        try:
            exam_date = datetime.strptime(exam_date_str, "%Y%m%d").date()
        except ValueError:
            raise CustomValueError(CustomMessageCode.INVALID_EXAMINATION_DATE.title)

        # Validate device_type
        if device_type and device_type not in MedicalDeviceType.__members__:
            raise CustomValueError(CustomMessageCode.INVALID_DEVICE_TYPE.title)

        self.file_info = ParsedFileInfo(
            file_name=file_name,
            clinic_no=clinic_no,
            patient_no=patient_no,
            examination_date=exam_date,
            timestamps=ts_str,
            device_type=device_type,
            ext=ext_lower,
        )
        return self


class ImportTreatmentImageValidationResult(BaseModel):
    is_valid: bool
    error: Optional[str] = None
    error_code: Optional[int] = None
    tenant: Optional[object] = None
    patient_user_id: Optional[int] = None
