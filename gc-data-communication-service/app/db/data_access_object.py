from typing import Generic, TypeVar

from sqlalchemy import exists, select
from sqlalchemy.engine import Row
from sqlalchemy.ext.asyncio import AsyncSession

# Define a TypeVar to represent any SQLAlchemy Model class
ModelType = TypeVar("ModelType")


class BaseDAO(Generic[ModelType]):
    def __init__(self, model: type[ModelType], db_session: AsyncSession):
        self.model = model
        self.session = db_session

    async def get_one_by(
        self,
        fields: list[str] | None = None,
        sort_by: dict[str, bool] | None = None,
        **filter_by,
    ) -> ModelType | Row | None:
        """
        Examples:
            form_flow_dao = BaseDAO(FormFlow, self.session)

            form_flow = await form_flow_dao.get_one_by(
                uuid=submission_data.form_flow_uuid,
                is_active=True
            )

            form_flow = await form_flow_dao.get_one_by(
                fields=["flow_name", "flow_type"],
                uuid=submission_data.form_flow_uuid,
                is_active=True
            )

            form_flow = await form_flow_dao.get_one_by(
                sort_by={"created_at": False},  # Sort by created_at descending
                uuid=submission_data.form_flow_uuid,
                is_active=True
            )

            form_flow = await form_flow_dao.get_one_by(
                sort_by={"priority": False, "created_at": True},
                # Sort by priority descending, then by created_at ascending
                is_active=True
            )
        """
        async with self.session:
            # Dynamically build the select statement
            if fields:
                # If a list of fields is provided, select only those columns
                query = select(*(getattr(self.model, field) for field in fields))
            else:
                # Otherwise, select the entire model object
                query = select(self.model)

            # Dynamically build the WHERE clause from filter arguments
            for key, value in filter_by.items():
                query = query.where(getattr(self.model, key) == value)

            if sort_by:
                for field, is_ascending in sort_by.items():
                    column = getattr(self.model, field)
                    if is_ascending:
                        query = query.order_by(column)
                    else:
                        query = query.order_by(column.desc())

            result = await self.session.execute(query)

            return result.scalar_one_or_none() if not fields else result.first()

    async def exists(self, **filter_by) -> bool:
        """
        Examples:
            form_flow_dao = BaseDAO(FormFlow, self.session)

            is_form_flow_exists = form_flow_dao.exists(
                uuid=submission_data.form_flow_uuid, is_active=True
            )
        """
        async with self.session:
            existence_query = exists().where(
                *(getattr(self.model, key) == value for key, value in filter_by.items())
            )
            result = await self.session.execute(select(existence_query))
            return result.scalar()
