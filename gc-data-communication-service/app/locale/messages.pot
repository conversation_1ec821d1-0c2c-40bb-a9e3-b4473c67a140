# Translations template for PROJECT.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-08-29 16:15+0700\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: core/messages.py:53
msgid "Unknown error!"
msgstr ""

#: core/messages.py:85
msgid "X-Tenant-Slug header is required!"
msgstr ""

#: core/messages.py:90
msgid "Tenant not found!"
msgstr ""

#: core/messages.py:96
msgid "Clinic information not found!"
msgstr ""

#: core/messages.py:101
msgid "Clinic created successfully!"
msgstr ""

#: core/messages.py:106
msgid "Clinic creation failed!"
msgstr ""

#: core/messages.py:111
msgid "Clinic not found!"
msgstr ""

#: core/messages.py:165
msgid "Role created successfully!"
msgstr ""

#: core/messages.py:170
msgid "Role creation failed!"
msgstr ""

#: core/messages.py:177
msgid "Master data not found!"
msgstr ""

#: core/messages.py:182
msgid "Master data fetched successfully!"
msgstr ""

#: core/messages.py:187
msgid "Master data fetch failed!"
msgstr ""

#: core/messages.py:192
msgid "Master data column does not exist!"
msgstr ""

#: core/messages.py:197
msgid "Invalid filter condition!"
msgstr ""

#: core/messages.py:204
msgid "Login successful!"
msgstr ""

#: core/messages.py:209
msgid "User not found!"
msgstr ""

#: core/messages.py:214
msgid "Invalid username or password!"
msgstr ""

#: core/messages.py:473
msgid "Patient clinical number exists!"
msgstr ""

#: core/messages.py:478
msgid "Patient creation failed!"
msgstr ""

#: core/messages.py:483
msgid "Patient not found!"
msgstr ""

#: core/messages.py:488
msgid "Patient update failed!"
msgstr ""

#: core/messages.py:493
msgid "Patient deletion failed!"
msgstr ""

#: core/messages.py:498
msgid "Patient deleted successfully!"
msgstr ""

#: core/messages.py:503
msgid "Patient clinical number generation failed!"
msgstr ""

#: core/messages.py:508
msgid "Patient get list failed!"
msgstr ""

#: core/messages.py:513
msgid "Patient get detail failed!"
msgstr ""

#: core/messages.py:518
msgid "Patient phone missing!"
msgstr ""

#: core/messages.py:523
msgid "Patient phone number already exists!"
msgstr ""

#: core/messages.py:530
msgid "Patient waiting created successfully!"
msgstr ""

#: core/messages.py:535
msgid "Patient user not found!"
msgstr ""

#: core/messages.py:540
msgid "Doctor not found!"
msgstr ""

#: core/messages.py:545
msgid "Patient waiting reservation creation failed!"
msgstr ""

#: core/messages.py:550
msgid "Patient waiting reservation not found!"
msgstr ""

#: core/messages.py:555
msgid "Patient waiting get list failed!"
msgstr ""

#: core/messages.py:560
msgid "Patient waiting not found!"
msgstr ""

#: core/messages.py:581
msgid "Doctor creation failed!"
msgstr ""

#: core/messages.py:586
msgid "Email already exists!"
msgstr ""

#: core/messages.py:591
msgid "Phone already exists!"
msgstr ""

#: core/messages.py:596
msgid "Doctor created successfully!"
msgstr ""

#: core/messages.py:601
msgid "Doctor profile retrieval failed!"
msgstr ""

#: core/messages.py:606
msgid "Doctor update failed!"
msgstr ""

#: core/messages.py:611
msgid "Doctor updated successfully!"
msgstr ""

#: core/messages.py:616
msgid "Doctor list retrieval failed!"
msgstr ""

#: core/messages.py:621
msgid "Admin role required!"
msgstr ""

#: core/messages.py:628
msgid "Get list and save reservation failed!"
msgstr ""

#: core/messages.py:633
msgid "Sync data to patient waiting failed!"
msgstr ""

#: core/messages.py:638
msgid "Get and save reservation records with errors!"
msgstr ""

#: core/messages.py:710
msgid "Get info document failed!"
msgstr ""

#: core/messages.py:715
msgid "Get list document failed!"
msgstr ""

#: core/messages.py:720
msgid "Document management not found!"
msgstr ""

#: core/messages.py:725
msgid "Document management creation failed!"
msgstr ""

#: core/messages.py:730
msgid "Document management created successfully!"
msgstr ""

#: core/messages.py:735
msgid "Document management update failed!"
msgstr ""

#: core/messages.py:740
msgid "Document management updated successfully!"
msgstr ""

#: core/messages.py:745
msgid "Document management delete failed!"
msgstr ""

#: core/messages.py:750
msgid "Document management deleted successfully!"
msgstr ""

#: core/messages.py:755
msgid "Invalid document management status!"
msgstr ""

#: core/messages.py:760
msgid "Get preview documents failed!"
msgstr ""

#: core/messages.py:765
msgid "Either document_id or examination_date is required!"
msgstr ""

#: core/messages.py:770
msgid "Document data must not be empty!"
msgstr ""

#: core/messages.py:775
msgid "Preview document data must not be empty!"
msgstr ""

#: core/messages.py:780
msgid "Get list library failed!"
msgstr ""

#: core/messages.py:785
msgid "Get document version failed!"
msgstr ""

