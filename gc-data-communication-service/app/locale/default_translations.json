{"Unknown error!": "「不明なエラーです!", "X-Tenant-Slug header is required!": "X-Tenant-Slug ヘッダーは必須です。", "Tenant not found!": "テナントが見つかりません。", "Clinic information not found!": "クリニック情報が見つかりません。", "Clinic created successfully!": "クリニックが正常に作成されました。", "Clinic creation failed!": "クリニックの作成に失敗しました。", "Clinic not found!": "クリニックが見つかりません。", "Role created successfully!": "ロールが正常に作成されました。", "Role creation failed!": "ロールの作成に失敗しました。", "Master data not found!": "マスターデータが見つかりません。", "Master data fetched successfully!": "マスターデータが正常に取得されました。", "Master data fetch failed!": "マスターデータの取得に失敗しました。", "Master data column does not exist!": "マスターデータに指定されたカラムが存在しません。", "Invalid filter condition!": "無効なフィルター条件です。", "Login successful!": "ログインに成功しました。", "User not found!": "ユーザーが見つかりません。", "Invalid username or password!": "ユーザー名またはパスワードが無効です。", "Patient clinical number exists!": "患者のカルテ番号が既に存在します。", "Patient creation failed!": "患者の作成に失敗しました。", "Patient not found!": "患者が見つかりません。", "Patient update failed!": "患者の更新に失敗しました。", "Patient deletion failed!": "患者の削除に失敗しました。", "Patient deleted successfully!": "患者が正常に削除されました。", "Patient clinical number generation failed!": "患者のカルテ番号の生成に失敗しました。", "Patient get list failed!": "患者一覧の取得に失敗しました。", "Patient get detail failed!": "患者詳細の取得に失敗しました。", "Patient phone missing!": "患者の電話番号が登録されていません。", "Patient phone number already exists!": "患者の電話番号が既に存在します。", "Patient waiting created successfully!": "患者の待機が正常に作成されました。", "Patient user not found!": "患者ユーザーが見つかりません。", "Doctor not found!": "医師が見つかりません。", "Patient waiting reservation creation failed!": "患者待機予約の作成に失敗しました。", "Patient waiting reservation not found!": "患者待機予約が見つかりません。", "Patient waiting get list failed!": "患者待機一覧の取得に失敗しました。", "Patient waiting not found!": "患者待機が見つかりません。", "Doctor creation failed!": "ドクターの作成に失敗しました。", "Email already exists!": "メールアドレスは既に存在します。", "Phone already exists!": "電話番号は既に存在します。", "Doctor created successfully!": "ドクターが正常に作成されました。", "Doctor profile retrieval failed!": "ドクタープロフィールの取得に失敗しました。", "Doctor update failed!": "ドクターの更新に失敗しました。", "Doctor updated successfully!": "ドクターが正常に更新されました。", "Doctor list retrieval failed!": "ドクター一覧の取得に失敗しました。", "Admin role required!": "管理者権限が必要です。", "Get list and save reservation failed!": "予約一覧の取得と保存に失敗しました。", "Sync data to patient waiting failed!": "予約データを患者待機リストに同期できませんでした。", "Get and save reservation records with errors!": "エラー付きの予約記録の取得と保存に失敗しました。", "Get info document failed!": "ドキュメント情報の取得に失敗しました。", "Get list document failed!": "ドキュメント一覧の取得に失敗しました。", "Document management not found!": "ドキュメント管理が見つかりません。", "Document management creation failed!": "ドキュメント管理の作成に失敗しました。", "Document management created successfully!": "ドキュメント管理が正常に作成されました。", "Document management update failed!": "ドキュメント管理の更新に失敗しました。", "Document management updated successfully!": "ドキュメント管理が正常に更新されました。", "Document management delete failed!": "ドキュメント管理の削除に失敗しました。", "Document management deleted successfully!": "ドキュメント管理が正常に削除されました。", "Invalid document management status!": "無効なドキュメント管理ステータスです。", "Get preview documents failed!": "プレビュードキュメントの取得に失敗しました。", "Either document_id or examination_date is required!": "document_id または examination_date が必須です。", "Document data must not be empty!": "ドキュメントデータを空にすることはできません。", "Preview document data must not be empty!": "プレビュードキュメントデータを空にすることはできません。", "Get list library failed!": "ライブラリ一覧の取得に失敗しました。", "Get document version failed!": "ドキュメントバージョンの取得に失敗しました。"}