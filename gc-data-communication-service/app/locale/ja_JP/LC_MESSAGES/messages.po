# Japanese (Japan) translations for PROJECT.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-08-29 16:15+0700\n"
"PO-Revision-Date: 2025-08-29 16:00+0700\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: ja_JP\n"
"Language-Team: ja_<PERSON> <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: core/messages.py:53
msgid "Unknown error!"
msgstr "「不明なエラーです!"

#: core/messages.py:85
msgid "X-Tenant-Slug header is required!"
msgstr "X-Tenant-Slug ヘッダーは必須です。"

#: core/messages.py:90
msgid "Tenant not found!"
msgstr "テナントが見つかりません。"

#: core/messages.py:96
msgid "Clinic information not found!"
msgstr "クリニック情報が見つかりません。"

#: core/messages.py:101
msgid "Clinic created successfully!"
msgstr "クリニックが正常に作成されました。"

#: core/messages.py:106
msgid "Clinic creation failed!"
msgstr "クリニックの作成に失敗しました。"

#: core/messages.py:111
msgid "Clinic not found!"
msgstr "クリニックが見つかりません。"

#: core/messages.py:165
msgid "Role created successfully!"
msgstr "ロールが正常に作成されました。"

#: core/messages.py:170
msgid "Role creation failed!"
msgstr "ロールの作成に失敗しました。"

#: core/messages.py:177
msgid "Master data not found!"
msgstr "マスターデータが見つかりません。"

#: core/messages.py:182
msgid "Master data fetched successfully!"
msgstr "マスターデータが正常に取得されました。"

#: core/messages.py:187
msgid "Master data fetch failed!"
msgstr "マスターデータの取得に失敗しました。"

#: core/messages.py:192
msgid "Master data column does not exist!"
msgstr "マスターデータに指定されたカラムが存在しません。"

#: core/messages.py:197
msgid "Invalid filter condition!"
msgstr "無効なフィルター条件です。"

#: core/messages.py:204
msgid "Login successful!"
msgstr "ログインに成功しました。"

#: core/messages.py:209
msgid "User not found!"
msgstr "ユーザーが見つかりません。"

#: core/messages.py:214
msgid "Invalid username or password!"
msgstr "ユーザー名またはパスワードが無効です。"

#: core/messages.py:473
msgid "Patient clinical number exists!"
msgstr "患者のカルテ番号が既に存在します。"

#: core/messages.py:478
msgid "Patient creation failed!"
msgstr "患者の作成に失敗しました。"

#: core/messages.py:483
msgid "Patient not found!"
msgstr "患者が見つかりません。"

#: core/messages.py:488
msgid "Patient update failed!"
msgstr "患者の更新に失敗しました。"

#: core/messages.py:493
msgid "Patient deletion failed!"
msgstr "患者の削除に失敗しました。"

#: core/messages.py:498
msgid "Patient deleted successfully!"
msgstr "患者が正常に削除されました。"

#: core/messages.py:503
msgid "Patient clinical number generation failed!"
msgstr "患者のカルテ番号の生成に失敗しました。"

#: core/messages.py:508
msgid "Patient get list failed!"
msgstr "患者一覧の取得に失敗しました。"

#: core/messages.py:513
msgid "Patient get detail failed!"
msgstr "患者詳細の取得に失敗しました。"

#: core/messages.py:518
msgid "Patient phone missing!"
msgstr "患者の電話番号が登録されていません。"

#: core/messages.py:523
msgid "Patient phone number already exists!"
msgstr "患者の電話番号が既に存在します。"

#: core/messages.py:530
msgid "Patient waiting created successfully!"
msgstr "患者の待機が正常に作成されました。"

#: core/messages.py:535
msgid "Patient user not found!"
msgstr "患者ユーザーが見つかりません。"

#: core/messages.py:540
msgid "Doctor not found!"
msgstr "医師が見つかりません。"

#: core/messages.py:545
msgid "Patient waiting reservation creation failed!"
msgstr "患者待機予約の作成に失敗しました。"

#: core/messages.py:550
msgid "Patient waiting reservation not found!"
msgstr "患者待機予約が見つかりません。"

#: core/messages.py:555
msgid "Patient waiting get list failed!"
msgstr "患者待機一覧の取得に失敗しました。"

#: core/messages.py:560
msgid "Patient waiting not found!"
msgstr "患者待機が見つかりません。"

#: core/messages.py:581
msgid "Doctor creation failed!"
msgstr "ドクターの作成に失敗しました。"

#: core/messages.py:586
msgid "Email already exists!"
msgstr "メールアドレスは既に存在します。"

#: core/messages.py:591
msgid "Phone already exists!"
msgstr "電話番号は既に存在します。"

#: core/messages.py:596
msgid "Doctor created successfully!"
msgstr "ドクターが正常に作成されました。"

#: core/messages.py:601
msgid "Doctor profile retrieval failed!"
msgstr "ドクタープロフィールの取得に失敗しました。"

#: core/messages.py:606
msgid "Doctor update failed!"
msgstr "ドクターの更新に失敗しました。"

#: core/messages.py:611
msgid "Doctor updated successfully!"
msgstr "ドクターが正常に更新されました。"

#: core/messages.py:616
msgid "Doctor list retrieval failed!"
msgstr "ドクター一覧の取得に失敗しました。"

#: core/messages.py:621
msgid "Admin role required!"
msgstr "管理者権限が必要です。"

#: core/messages.py:628
msgid "Get list and save reservation failed!"
msgstr "予約一覧の取得と保存に失敗しました。"

#: core/messages.py:633
msgid "Sync data to patient waiting failed!"
msgstr "予約データを患者待機リストに同期できませんでした。"

#: core/messages.py:638
msgid "Get and save reservation records with errors!"
msgstr "エラー付きの予約記録の取得と保存に失敗しました。"

#: core/messages.py:710
msgid "Get info document failed!"
msgstr "ドキュメント情報の取得に失敗しました。"

#: core/messages.py:715
msgid "Get list document failed!"
msgstr "ドキュメント一覧の取得に失敗しました。"

#: core/messages.py:720
msgid "Document management not found!"
msgstr "ドキュメント管理が見つかりません。"

#: core/messages.py:725
msgid "Document management creation failed!"
msgstr "ドキュメント管理の作成に失敗しました。"

#: core/messages.py:730
msgid "Document management created successfully!"
msgstr "ドキュメント管理が正常に作成されました。"

#: core/messages.py:735
msgid "Document management update failed!"
msgstr "ドキュメント管理の更新に失敗しました。"

#: core/messages.py:740
msgid "Document management updated successfully!"
msgstr "ドキュメント管理が正常に更新されました。"

#: core/messages.py:745
msgid "Document management delete failed!"
msgstr "ドキュメント管理の削除に失敗しました。"

#: core/messages.py:750
msgid "Document management deleted successfully!"
msgstr "ドキュメント管理が正常に削除されました。"

#: core/messages.py:755
msgid "Invalid document management status!"
msgstr "無効なドキュメント管理ステータスです。"

#: core/messages.py:760
msgid "Get preview documents failed!"
msgstr "プレビュードキュメントの取得に失敗しました。"

#: core/messages.py:765
msgid "Either document_id or examination_date is required!"
msgstr "document_id または examination_date が必須です。"

#: core/messages.py:770
msgid "Document data must not be empty!"
msgstr "ドキュメントデータを空にすることはできません。"

#: core/messages.py:775
msgid "Preview document data must not be empty!"
msgstr "プレビュードキュメントデータを空にすることはできません。"

#: core/messages.py:780
msgid "Get list library failed!"
msgstr "ライブラリ一覧の取得に失敗しました。"

#: core/messages.py:785
msgid "Get document version failed!"
msgstr "ドキュメントバージョンの取得に失敗しました。"
