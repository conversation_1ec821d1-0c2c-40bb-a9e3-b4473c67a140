from configuration.settings import configuration
from firebase_admin import credentials, initialize_app

from gc_dentist_shared.core.logger.config import log


def firebase_init():
    try:
        log.info("Initializing Firebase...")
        cred = credentials.Certificate(cert=configuration.FIREBASE_CERT_PATH)
        initialize_app(credential=cred)
        log.info("✅ Firebase initialized successfully")
    except Exception as e:
        log.error(f"❌ Firebase initialization failed: {e}")
