from enum import Enum

from gc_dentist_shared.core.common.i18n import i18n as _


class CustomMessageCode(Enum):
    """Custom message codes for application-specific errors and messages."""

    def __new__(cls, code, title, description):
        obj = object.__new__(cls)
        obj._value_ = code
        obj._title = title
        obj._description = description
        return obj

    @property
    def code(self):
        return self.value

    @property
    def title(self):
        # Using the i18n instance to translate the title
        return _(self._title)

    @property
    def description(self):
        return self._description

    def __str__(self):
        return f"{self.code} - {self.title}: {self.description}"

    # Tenant Clinic
    X_TENANT_SLUG_IS_REQUIRED = (
        4000,
        _("X-Tenant-Slug header is required!", "X-Tenant-Slug ヘッダーは必須です。"),
        "The X-Tenant-Slug header must be provided in the request.",
    )
    TENANT_NOT_FOUND = (
        4004,
        _("Tenant not found!", "テナントが見つかりません。"),
        "The specified tenant does not exist.",
    )

    CLINIC_INFO_NOT_FOUND = (
        4005,
        _("Clinic information not found!", "クリニック情報が見つかりません。"),
        "The specified clinic information does not exist.",
    )
    CLINIC_CREATED_SUCCESS = (
        4006,
        _("Clinic created successfully!", "クリニックが正常に作成されました。"),
        "The clinic has been created successfully.",
    )
    CLINIC_CREATED_FAILED = (
        4007,
        _("Clinic creation failed!", "クリニックの作成に失敗しました。"),
        "An error occurred while creating the clinic.",
    )
    CLINIC_NOT_FOUND = (
        4008,
        _("Clinic not found!", "クリニックが見つかりません。"),
        "The specified clinic does not exist.",
    )
    TENANT_CONFIGURATION_NOT_FOUND = (
        4009,
        "Tenant configuration not found!",
        "The tenant configuration does not exist.",
    )
    TENANT_CONFIGURATION_GET_FAILED = (
        4010,
        "Tenant configuration get failed!",
        "An error occurred while getting the tenant configuration.",
    )
    PATIENT_NOT_FOUND = (
        4011,
        "Patient not found!",
        "The provided patient_no does not exist in the specified clinic database.",
    )

    INVALID_REQUEST_PAYLOAD = (
        4422,
        "Invalid request payload!",
        "The request payload is invalid or improperly formatted",
    )

    # Server Errors
    UNKNOWN_ERROR = (
        5000,
        _("Unknown error!", "「不明なエラーです!"),
        "An unexpected error occurred.",
    )

    S3_GENERATED_PRESIGNEDURL_INVALID_FILE_NAMES = (
        10001,
        "Invalid file names!",
        "The file names provided are invalid or empty.",
    )

    S3_GENERATED_PRESIGNEDURL_DUPLICATE_FILE_NAMES = (
        10002,
        "Duplicate file names!",
        "Each file name must be unique.",
    )

    S3_GENERATED_PRESIGNEDURL_INVALID_EXTENSION = (
        10003,
        "Invalid extension For data_type 'patient_info', all files must be CSV; otherwise only image types are allowed",
        "For data_type 'patient_info', all files must be CSV; otherwise only image types are allowed.",
    )

    INVALID_EXAMINATION_DATE = (
        10004,
        "Examination date must be in format YYYYMMDD and represent a valid calendar date.",
        "Examination date must be in format YYYYMMDD and represent a valid calendar date.",
    )

    INVALID_FILENAME_FORMAT = (
        10005,
        "The filename format is invalid for the selected data_type",
        "The filename format is invalid for the selected data_type",
    )

    INVALID_DEVICE_TYPE = (
        10006,
        "Invalid device type!",
        "Device type in filename is not supported.",
    )

    BULK_REGISTER_PATIENT_SUCCESS = (
        6000,
        "Register patient successfully!",
        "Register patient successfully!",
    )

    REGISTER_PATIENT_FAILED = (
        6001,
        "Register patient failed!",
        "Register patient failed!",
    )

    FILE_TYPE_NOT_SUPPORTED = (
        6002,
        "File type not supported!",
        "File type not supported!",
    )
    TENANT_CLINIC_NOT_FOUND = (
        6003,
        "Tenant clinic not found!",
        "Tenant clinic not found!",
    )
    PATIENT_CSV_FILE_NOT_FOUND = (
        6004,
        "Patient CSV file not found!",
        "Patient CSV file not found!",
    )
    CSV_FILE_IS_EMPTY = (
        6005,
        "CSV file is empty!",
        "CSV file is empty!",
    )
    CSV_FILE_INVALID_RECORD = (
        6006,
        "CSV file invalid record!",
        "CSV file invalid record!",
    )
    CSV_COLUMNS_MISSING = (
        6007,
        "Your CSV file is malformed. The required column(s) {column_headers} appear to be missing.",
        "CSV missing columns!",
    )
    REGISTER_PATIENT_SUCCESS = (
        6008,
        "Register patient successfully!",
        "Register patient successfully!",
    )

    SEARCH_PATIENT_FAILED = (
        6009,
        "Search patient failed!",
        "Search patient failed!",
    )
    SEARCH_PATIENT_SUCCESS = (
        6010,
        "Search patient successfully!",
        "Search patient successfully!",
    )

    REQUIRED_FIELD_ERROR = (
        6100,
        "Please enter your {column_name}.",
        "The required field is missing a value.",
    )
    MAX_LENGTH_FIELD_ERROR = (
        6101,
        "Please enter {column_name} in {max_length} characters or less.",
        "Exceeded maximum allowed length for the column.",
    )
    INVALID_GENDER_ERROR = (
        6102,
        "Gender is invalid.",
        "The value does not match the valid gender options.",
    )
    INVALID_VALUE_ERROR = (
        6103,
        "Invalid value found in '{column_name}'",
        "Invalid value found in the column.",
    )
    INVALID_DATE_ERROR = (
        6104,
        "Invalid date format. Expected 'yyyy/mm/dd",
        "The date value does not match the required format.",
    )
    INVALID_KATAKANA_ERROR = (
        6105,
        "The full name must only contain katakana characters.",
        "The full name must only contain katakana characters.",
    )
    PHONE_EXISTS_ERROR = (
        6106,
        "Telephone number already exists for another patient profile",
        "Telephone number already exists for another patient profile",
    )

    IMPORT_TREATMENT_IMAGE_PATIENT_SUCCESS = (
        20000,
        "Import treatment image successfully!",
        "Import treatment image successfully!",
    )

    IMPORT_TREATMENT_IMAGE_PATIENT_ERROR = (
        20001,
        "Import treatment image failed!",
        "Import treatment image failed!",
    )

    IMPORT_TREATMENT_IMAGE_INVALID_FORMAT = (
        20002,
        "Treatment image name invalid!",
        "Treatment image name invalid!",
    )

    IMPORT_TREATMENT_IMAGE_NOT_FOUND = (
        20003,
        "Treatment image not found!",
        "Treatment image not found!",
    )

    IMPORT_TREATMENT_IMAGE_INVALID_FILE = (
        20004,
        "Invalid image file!",
        "The provided file is not a valid image or is corrupted.",
    )

    IMPORT_TREATMENT_IMAGE_VALIDATION_FAILED = (
        20005,
        "The provided file is not a valid data",
        "The provided file is not a valid data",
    )

    IMPORT_TREATMENT_IMAGE_INVALID_EXTENSION = (
        20006,
        "Extension image is invalid",
        "Extension image is invalid",
    )

    # Oral Examination
    INTRAORAL_EXAMINATION_GET_LIST_SUCCESS = (
        5010,
        "Successfully retrieved intraoral examination results ordered by closest datetime.",
        "Successfully retrieved intraoral examination results ordered by closest datetime.",
    )
    INTRAORAL_GET_LIST_FAILED = (
        5011,
        _("Intraoral list retrieval failed!", "口腔内検査一覧の取得に失敗しました。"),
        "An error occurred while get list the intraoral.",
    )
    INTRAORAL_EXAMINATION_GET_DETAIL_SUCCESS = (
        5012,
        "Successfully retrieved intraoral examination detail.",
        "Successfully retrieved intraoral examination detail.",
    )
    INTRAORAL_GET_DETAIL_FAILED = (
        5013,
        _("Intraoral detail retrieval failed!", "口腔内検査詳細の取得に失敗しました。"),
        "An error occurred while get detail the intraoral.",
    )
    INTRAORAL_EXAMINATION_NOT_FOUND = (
        5014,
        "Intraoral examination not found!",
        "The specified intraoral examination does not exist.",
    )

    # Examination
    PATIENT_INSTRUMENTAL_EXAMINATION_CREATED_SUCCESS = (
        60001,
        "Patient instrumental examination created successfully!",
        "The patient instrumental examination has been created successfully.",
    )

    PATIENT_INSTRUMENTAL_EXAMINATION_CREATED_FAILED = (
        60002,
        "Patient instrumental examination creation failed!",
        "An error occurred while creating the patient instrumental examination.",
    )
    PATIENT_INSTRUMENTAL_EXAMINATION_INVALID_PAYLOAD = (
        60003,
        "Invalid payload for patient instrumental examination!",
        "The provided payload for patient instrumental examination is invalid.",
    )
    PATIENT_INSTRUMENTAL_EXAMINATION_MISSING_FI_DATA = (
        60004,
        "Missing fi_data in patient instrumental examination payload!",
        "The payload must include the fi_data field.",
    )
    PATIENT_INSTRUMENTAL_EXAMINATION_MISSING_IMAGE_FILE_NAME = (
        60005,
        "Missing image_file_name in patient instrumental examination payload!",
        "The payload must include the image_file_name field.",
    )
    PATIENT_INSTRUMENTAL_EXAMINATION_MISSING_REQUIRED_FIELD = (
        60006,
        "Missing required field in fi_data!",
        "The fi_data object must include type, id, patient_no, and examination_date.",
    )
    PATIENT_INSTRUMENTAL_EXAMINATION_INVALID_DEVICE_TYPE = (
        60007,
        "Invalid device type in patient instrumental examination!",
        "The provided device type is not supported.",
    )
