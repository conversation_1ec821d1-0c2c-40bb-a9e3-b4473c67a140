from enums.base import IntEnum
from enums.patient_enum import Gender
from enums.s3_enums import DataTypeGeneratedPresignedEnums

DISPLAY_DATE_FORMAT = "%Y/%m/%d"

DISPLAY_DATETIME_FORMAT = "%Y/%m/%d %H:%M:%S"

CSV_DATE_OF_BIRTH_FORMAT = "%Y/%m/%d"


MAPPING_LANG_REGION = {
    "ja": "ja-JP",
    "en": "en-US",
}

X_TENANT_UUID = "X-Tenant-UUID"
X_TENANT_CLINIC_CODE = "X-Tenant-Clinic-Code"

COUNTRY_CODE_JP = "+81"

IAPO_RESULT_SUCCESS = 0

DOCUMENT_MANAGEMENT_DEFAULT_VERSION = 1

PATIENT_CSV_ENCODING = "cp932"

NUMERIC_HYPHEN_REGEX = r"^[0-9-]+$"
KATAKANA_REGEX = r"^[\u30a0-\u30ff\uff65-\uff9fー]+$"
IMAGE_FILENAME_REGEX = r"^([A-Za-z0-9]+)_([A-Za-z0-9]+)_([0-9]{8})_([0-9]{13})(?:_([A-Za-z]+))?\.([A-Za-z]+)$"
PATIENT_INFO_FILENAME_REGEX = r"^([A-Za-z0-9]+)_([0-9]{13})\.(csv)$"
FILENAME_REGEX = {
    DataTypeGeneratedPresignedEnums.IMAGE.value: IMAGE_FILENAME_REGEX,
    DataTypeGeneratedPresignedEnums.PATIENT_INFO.value: PATIENT_INFO_FILENAME_REGEX,
}
IMAGE_EXTENSIONS = {
    "jpg",
    "jpeg",
    "png",
    "bmp",
    "gif",
    "webp",
    "tiff",
    "heic",
    "heif",
}

PATIENT_GENDER_MAPPING = {
    "男": Gender.MALE.value,
    "女": Gender.FEMALE.value,
}

PATIENT_FIELDS_ENCRYPTED = [
    "date_of_birth",
    "phone",
    "home_phone",
]

PATIENT_FIELDS_HASHED = [
    "date_of_birth",
    "phone",
    "home_phone",
]


class TenantClinicStatus(IntEnum):
    INPROCESS = 20
    SUCCESS = 40
    FAILED = 90
    FAILED_CREATED_CLINIC_SETTING = 91
    CANCELED = 99
