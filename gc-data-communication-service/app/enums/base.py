#!/usr/bin/env python3
from enum import Enum
from enum import IntEnum as SourceIntEnum


class _EnumBase:
    @classmethod
    def get_member_keys(cls: type[Enum]) -> list[str]:
        return [name for name in cls.__members__.keys()]

    @classmethod
    def get_member_values(cls: type[Enum]) -> list:
        return [item.value for item in cls.__members__.values()]


class IntEnum(_EnumBase, SourceIntEnum):
    """Integer enum"""


class StrEnum(_EnumBase, str, Enum):
    """String enum"""


class DictEnum(_EnumBase, dict, Enum):
    """Dict enum"""


class CSVColumnEnum(_EnumBase, tuple, Enum):
    """CSV enum"""

    @property
    def header_name(self):
        return self.value[0]

    @property
    def column_name(self):
        return self.value[1]

    @classmethod
    def get_column_headers(cls) -> list[str]:
        return [member.header_name for member in cls]

    @classmethod
    def get_column_names(cls) -> list[str]:
        return [member.column_name for member in cls]
