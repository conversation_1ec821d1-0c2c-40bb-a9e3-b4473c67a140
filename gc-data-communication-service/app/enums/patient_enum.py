from enums.base import CSVColumnEnum, IntEnum


class Gender(IntEnum):
    MALE = 1
    FEMALE = 2
    OTHER = 0


class PatientWaitingStatus(IntEnum):
    NEW = 1
    SCHEDULED = 3
    RESOLVED = 5
    CANCELLED = 9


class EmergencyFlag(IntEnum):
    NORMAL = 0
    EMERGENCY = 1


class PatientColumnMapping(CSVColumnEnum):
    PATIENT_NUMBER = ("患者番号", "patient_number")
    FULL_NAME_KANA = ("カナ氏名", "full_name_kana")
    FULL_NAME = ("漢字氏名", "full_name")
    GENDER = ("性別", "gender")
    DATE_OF_BIRTH = ("生年月日", "date_of_birth")
    TELEPHONE_NUMBER_1 = ("電話番号１", "telephone_number_1")
    TELEPHONE_NUMBER_2 = ("電話番号２", "telephone_number_2")
    POSTAL_CODE = ("郵便番号", "postal_code")
    ADDRESS = ("住所", "address")
