from io import By<PERSON><PERSON>
from typing import Optional
from uuid import uuid4

from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from configuration.settings import configuration
from core.constants import (
    PATIENT_FIELDS_ENCRYPTED,
    PATIENT_FIELDS_HASHED,
    PATIENT_GENDER_MAPPING,
)
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from PIL import Image, UnidentifiedImageError
from schemas.requests.patient_requests import (
    ImportTreatmentImagePayload,
    ImportTreatmentImageValidationResult,
    RegisterPatientRequest,
)
from schemas.responses.patient_schema import (
    PatientProfileItemSchema,
    PatientProfileSearchResponse,
)
from services.tenant_clinics_service import TenantClinicService
from sqlalchemy import case, func, select, update
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import (
    GlobalUser,
    ProcessFileLog,
    TenantClinic,
    TenantPatientUserMapping,
)
from gc_dentist_shared.core.common import utils
from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.common.s3_bucket import S3Client
from gc_dentist_shared.core.common.utils import generate_thumbnail_image_path
from gc_dentist_shared.core.constants import (
    MAPPING_MEDICAL_DEVICE_TYPE_AND_DOCUMENT_GROUP_KEY,
)
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.enums.document import (
    DocumentDataType,
    DocumentDisplayMode,
    DocumentExtension,
    DocumentGroupKeyName,
    DocumentStatus,
)
from gc_dentist_shared.core.enums.process_file_enums import (
    FileTypeProcessFileEnum,
    SourceProcessFileLog,
    StatusProcessFileEnum,
)
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import (
    DocumentGroup,
    DocumentManagement,
    PatientProfile,
    PatientUser,
)
from gc_dentist_shared.tenant_models.medical_device.patient_treatment_image import (
    PatientTreatmentImage,
)


class PatientService:
    def __init__(
        self, central_db_session: AsyncSession = None, s3_client: S3Client = None
    ):
        self.s3_client = s3_client
        self.central_db_session = central_db_session

    async def register_patient(self, request_data: RegisterPatientRequest):
        token_db = None
        try:
            tenant_clinic = await self._get_tenant_clinic(request_data.clinic_code)
            tenant_uuid = str(tenant_clinic.tenant_uuid)
            token_db = set_current_db_name(db_name=tenant_clinic.db_name)
            aes_gcm = AesGCMRotation(configuration)

            patient_profile_data = {
                "full_name": request_data.full_name,
                "full_name_kana": request_data.full_name_kana,
                "date_of_birth": request_data.date_of_birth,
                "gender": PATIENT_GENDER_MAPPING.get(request_data.gender),
                "phone": request_data.telephone_number_1,
                "home_phone": request_data.telephone_number_2,
                "address": request_data.address,
                "postal_code": request_data.postal_code,
            }
            encrypted_patient_profile_data = aes_gcm.encrypt_and_hash_selected_fields(
                patient_profile_data,
                PATIENT_FIELDS_ENCRYPTED,
                PATIENT_FIELDS_HASHED,
            )

            async with TenantDatabase.get_instance_tenant_db() as tenant_db_session:
                async with tenant_db_session.begin():
                    patient_user_id = await self._get_patient_user_id(
                        tenant_db_session, request_data.patient_number
                    )
                    await self._validate_patient_profile_phone(
                        aes_gcm,
                        tenant_db_session,
                        request_data.telephone_number_1,
                        patient_user_id,
                    )

                    # Flow updates existing patient profile if exists
                    if patient_user_id:
                        await tenant_db_session.execute(
                            update(PatientProfile)
                            .where(PatientProfile.patient_user_id == patient_user_id)
                            .values(encrypted_patient_profile_data)
                        )
                        return

                    # Flow creates a new patient
                    await self.create_new_patient(
                        aes_gcm,
                        encrypted_patient_profile_data,
                        request_data,
                        tenant_db_session,
                        tenant_uuid,
                    )

        except CustomValueError as e:
            log.error(f"❌ Error register patient with custom error: {e.message}")
            raise e

        except Exception as e:
            log.error(f"❌ Error register patient: {str(e)}")
            raise e

        finally:
            if token_db:
                reset_current_db_name(token_db)

    async def create_new_patient(
        self,
        aes_gcm: AesGCMRotation,
        encrypted_patient_profile_data: dict,
        request_data: RegisterPatientRequest,
        tenant_db_session: AsyncSession,
        tenant_uuid: str,
    ):
        patient_user = PatientUser(
            username=request_data.patient_number,
            patient_no=request_data.patient_number,
        )
        plain_password = utils.generate_password()
        patient_user.set_password(plain_password, tenant_uuid)
        tenant_db_session.add(patient_user)
        await tenant_db_session.flush()

        patient_profile = PatientProfile(**encrypted_patient_profile_data)
        patient_profile.patient_user_id = patient_user.id
        tenant_db_session.add(patient_profile)
        await tenant_db_session.flush()

        if patient_profile.phone:
            async with self.central_db_session.begin():
                global_user_id = await self._get_global_user_id(
                    patient_profile.phone_hash
                )
                if not global_user_id:
                    global_user = GlobalUser(
                        username=patient_profile.phone,
                        username_hash=patient_profile.phone_hash,
                        day_of_birth=patient_profile.date_of_birth,
                        day_of_birth_hash=patient_profile.date_of_birth_hash,
                    )
                    global_user.set_password(plain_password, tenant_uuid)
                    self.central_db_session.add(global_user)
                    await self.central_db_session.flush()
                    global_user_id = global_user.id

                user_mapping = TenantPatientUserMapping(
                    tenant_uuid=tenant_uuid,
                    patient_no=aes_gcm.encrypt_data(request_data.patient_number),
                    global_user_id=global_user_id,
                    patient_user_id=patient_user.id,
                )
                self.central_db_session.add(user_mapping)
                await self.central_db_session.flush()

    async def search_patient(
        self, clinic_code: str, patient_number: str
    ) -> PatientProfileSearchResponse:
        token_db = None
        try:
            tenant_clinic = await self._get_tenant_clinic(clinic_code)
            token_db = set_current_db_name(tenant_clinic.db_name)

            query = (
                select(
                    PatientUser.patient_no.label("patient_number"),
                    case(
                        (
                            PatientProfile.full_name.isnot(None),
                            PatientProfile.full_name,
                        ),
                        else_=func.concat(
                            PatientProfile.last_name,
                            " ",
                            PatientProfile.first_name,
                        ),
                    ).label("full_name"),
                    case(
                        (
                            PatientProfile.full_name_kana.isnot(None),
                            PatientProfile.full_name_kana,
                        ),
                        else_=func.concat(
                            PatientProfile.last_name_kana,
                            " ",
                            PatientProfile.first_name_kana,
                        ),
                    ).label("full_name_kana"),
                    PatientProfile.gender,
                    PatientProfile.date_of_birth,
                    PatientProfile.phone.label("telephone_number_1"),
                    PatientProfile.home_phone.label("telephone_number_2"),
                    PatientProfile.postal_code,
                    case(
                        (
                            PatientProfile.address.isnot(None),
                            PatientProfile.address,
                        ),
                        else_=func.concat(
                            PatientProfile.address_1,
                            " ",
                            PatientProfile.address_2,
                            " ",
                            PatientProfile.address_3,
                        ),
                    ).label("address"),
                )
                .join(
                    PatientProfile,
                    PatientUser.id == PatientProfile.patient_user_id,
                )
                .where(
                    PatientUser.patient_no == patient_number,
                    PatientUser.status.is_(True),
                )
            )

            async with TenantDatabase.get_instance_tenant_db() as tenant_session:
                async with tenant_session.begin():
                    result = await tenant_session.execute(query)
                    patient_profiles = result.mappings().all()

                return PatientProfileSearchResponse(
                    items=[
                        PatientProfileItemSchema(**item) for item in patient_profiles
                    ]
                )

        except CustomValueError as e:
            log.error(f"❌ Search patient error: {e.message}")
            raise e

        except Exception as e:
            log.error(f"❌ Search patient error: {str(e)}")
            raise e

        finally:
            if token_db:
                reset_current_db_name(token_db)

    async def _get_tenant_clinic(self, clinic_no: str):
        async with self.central_db_session.begin():
            result = await self.central_db_session.execute(
                select(TenantClinic).where(TenantClinic.clinic_no == clinic_no)
            )
            tenant_clinic = result.scalar_one_or_none()
            if not tenant_clinic:
                log.error(f"❌ Tenant clinic not found for clinic no: {clinic_no}")
                raise CustomValueError(
                    message=CustomMessageCode.CLINIC_NOT_FOUND.title,
                    message_code=CustomMessageCode.CLINIC_NOT_FOUND.code,
                )

            return tenant_clinic

    @staticmethod
    async def get_patient_id_by_patient_no(db_name, patient_no):
        token_db = set_current_db_name(db_name)
        try:
            async with TenantDatabase.get_instance_tenant_db() as tenant_session:
                async with tenant_session.begin():
                    result = await tenant_session.execute(
                        select(PatientUser.id).where(
                            PatientUser.patient_no == patient_no,
                            PatientUser.status.is_(True),
                        )
                    )
                    doctor_user = result.scalar_one_or_none()
                    if not doctor_user:
                        log.error("❌ User not found or inactive")
                        return None
                return doctor_user
        except Exception as e:
            log.error(f"❌ Error get_patient_id_by_patient_no : {e}")
            return None
        finally:
            reset_current_db_name(token_db)

    async def _get_global_user_id(self, phone_hash: str) -> int | None:
        result = await self.central_db_session.execute(
            select(GlobalUser.id).where(GlobalUser.username_hash == phone_hash)
        )

        global_user_id = result.scalar_one_or_none()
        return global_user_id

    async def _get_patient_user_id(
        self, tenant_db_session: AsyncSession, patient_no: str
    ) -> int | None:
        result = await tenant_db_session.execute(
            select(PatientUser.id).where(PatientUser.patient_no == patient_no)
        )
        patient_user_id = result.scalar_one_or_none()
        return patient_user_id

    async def _validate_patient_profile_phone(
        self,
        aes_gcm,
        tenant_db_session: AsyncSession,
        phone: str,
        patient_user_id: Optional[int] = None,
    ):
        if not phone:
            return

        query = select(PatientProfile.patient_user_id).where(
            PatientProfile.phone_hash == aes_gcm.sha256_hash(phone),
            PatientProfile.patient_user_id != patient_user_id,
        )
        result = await tenant_db_session.execute(query)
        phone_exists = result.scalars().all()
        if phone_exists:
            raise CustomValueError(
                message=CustomMessageCode.PHONE_EXISTS_ERROR.title,
                message_code=CustomMessageCode.PHONE_EXISTS_ERROR.code,
            )

    async def import_treatment_images(self, payload: ImportTreatmentImagePayload):
        # Step 1: create log
        process_log_id = await self._create_process_file_logs(
            file_path=payload.image_file_path,
            file_type=FileTypeProcessFileEnum.TREATMENT_IMAGE,
        )

        # Step 2: validate
        validation = await self._validate_import_treatment_images(payload)
        if not validation.is_valid:
            log.error(f"❌ Error _validate_import_treatment_images: {validation.error}")
            await self._update_process_file_logs(
                process_log_id=process_log_id,
                status=StatusProcessFileEnum.FAILED,
                error_message=validation.error,
            )
            raise CustomValueError(
                message=validation.error,
                message_code=validation.error_code,
            )

        # Step 3: insert data
        result = await self._insert_treatment_images_and_documents(
            process_log_id=process_log_id,
            db_name=validation.tenant.db_name,
            patient_user_id=validation.patient_user_id,
            payload=payload,
        )

        return result

    async def _validate_import_treatment_images(
        self, payload: ImportTreatmentImagePayload
    ) -> ImportTreatmentImageValidationResult:
        file_content = None
        try:
            file_content = await self.s3_client.get_object(payload.image_file_path)
        except Exception as e:
            log.error(f"❌ S3 get_object error: {e}")

        if not file_content:
            return ImportTreatmentImageValidationResult(
                is_valid=False,
                error=CustomMessageCode.IMPORT_TREATMENT_IMAGE_NOT_FOUND.title,
                error_code=CustomMessageCode.IMPORT_TREATMENT_IMAGE_NOT_FOUND.code,
            )

        try:
            Image.open(BytesIO(file_content)).verify()
        except UnidentifiedImageError:
            return ImportTreatmentImageValidationResult(
                is_valid=False,
                error=CustomMessageCode.IMPORT_TREATMENT_IMAGE_INVALID_FILE.title,
                error_code=CustomMessageCode.IMPORT_TREATMENT_IMAGE_INVALID_FILE.code,
            )

        tenant = await TenantClinicService.get_tenant_by_clinic_no(
            clinic_no=payload.file_info.clinic_no
        )

        if not tenant:
            return ImportTreatmentImageValidationResult(
                is_valid=False,
                error=CustomMessageCode.CLINIC_NOT_FOUND.title,
                error_code=CustomMessageCode.CLINIC_NOT_FOUND.code,
            )

        patient_user_id = await PatientService.get_patient_id_by_patient_no(
            db_name=tenant.db_name,
            patient_no=payload.file_info.patient_no,
        )

        if not patient_user_id:
            return ImportTreatmentImageValidationResult(
                is_valid=False,
                error=CustomMessageCode.PATIENT_NOT_FOUND.title,
                error_code=CustomMessageCode.PATIENT_NOT_FOUND.code,
            )

        # 5) Valid
        return ImportTreatmentImageValidationResult(
            is_valid=True,
            tenant=tenant,
            patient_user_id=patient_user_id,
        )

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="_create_process_file_logs",
    )
    async def _create_process_file_logs(
        self, file_path: str, file_type: int
    ) -> Optional[int]:
        try:
            async with self.central_db_session.begin():
                process_log = ProcessFileLog(
                    source=SourceProcessFileLog.MIDDLEWARE_MEDICAL_APP.value,
                    file_path=file_path,
                    file_type=file_type,
                    status=StatusProcessFileEnum.PROCESSING.value,
                )
                self.central_db_session.add(process_log)
            return process_log.id

        except Exception as e:
            err_message = f"❌ Failed to _create_process_file_logs Error: {e}"
            log.error(err_message)
            raise CustomValueError(
                message=err_message,
            )

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="_update_process_file_logs",
    )
    async def _update_process_file_logs(
        self,
        process_log_id: int,
        status: int,
        result: Optional[dict] = None,
        error_message: Optional[str] = None,
    ):
        try:
            values = {
                "status": status,
                "error_message": error_message,
            }
            if result is not None:
                values["result"] = result

            async with self.central_db_session.begin():
                await self.central_db_session.execute(
                    update(ProcessFileLog)
                    .where(ProcessFileLog.id == process_log_id)
                    .values(**values)
                )
                await self.central_db_session.flush()
        except Exception as e:
            err_message = f"❌ Failed to _update_process_file_logs Error: {e}"
            log.error(err_message)
            raise CustomValueError(
                message=err_message,
            )

    async def _insert_treatment_images_and_documents(
        self,
        process_log_id,
        db_name,
        patient_user_id,
        payload: ImportTreatmentImagePayload,
    ):
        token_db = set_current_db_name(db_name=db_name)
        status_to_update = None
        result_to_update = None
        error_to_update = None
        try:
            async with TenantDatabase.get_instance_tenant_db() as tenant_db_session:
                async with tenant_db_session.begin():
                    object_insert = {
                        "external_patient_no": payload.file_info.patient_no,
                        "examination_date": payload.file_info.examination_date,
                        "patient_user_id": patient_user_id,
                        "image_file_name": payload.file_info.file_name,
                        "image_file_path": payload.image_file_path,
                        "preview_image_file_path": generate_thumbnail_image_path(
                            configuration=configuration,
                            original_image_path=payload.image_file_path,
                        ),
                    }
                    treatment_image_id = await self._insert_treatment_image(
                        tenant_db_session, object_insert
                    )
                    document_management_id = await self._insert_document_management(
                        tenant_db_session,
                        treatment_image_id,
                        device_type=payload.file_info.device_type,
                        object_insert=object_insert,
                    )
                    status_to_update = StatusProcessFileEnum.SUCCESS.value
                    result_to_update = {
                        "clinic_no": payload.file_info.clinic_no,
                        "file_name": payload.file_info.file_name,
                        "treatment_image_id": treatment_image_id,
                        "document_management_id": document_management_id,
                    }
        except Exception as e:
            log.error(f"❌ Error _insert_treatment_images patient: {str(e)}")
            status_to_update = StatusProcessFileEnum.FAILED.value
            error_to_update = str(e)
        finally:
            reset_current_db_name(token_db)

        await self._update_process_file_logs(
            process_log_id=process_log_id,
            status=status_to_update,
            result=result_to_update,
            error_message=error_to_update,
        )
        if status_to_update == StatusProcessFileEnum.FAILED.value:
            raise CustomValueError(
                message=error_to_update,
            )

        return result_to_update

    async def _insert_treatment_image(self, tenant_db_session, object_insert: dict):
        stmt = insert(PatientTreatmentImage).values(**object_insert)
        constraint_columns = [
            "patient_user_id",
            "image_file_name",
        ]
        immutable_cols = ["id", "created_at"]
        column_to_update = {
            c.name: getattr(stmt.excluded, c.name)
            for c in PatientTreatmentImage.__table__.columns
            if c.name not in (constraint_columns + immutable_cols)
        }
        column_to_update["updated_at"] = func.now()
        final_stmt = stmt.on_conflict_do_update(
            index_elements=constraint_columns, set_=column_to_update
        ).returning(PatientTreatmentImage.id)
        result = await tenant_db_session.execute(final_stmt)
        return result.scalar_one()

    async def _insert_document_management(
        self, tenant_db_session, treatment_image_id, device_type, object_insert
    ):
        document_group_key_name = (
            MAPPING_MEDICAL_DEVICE_TYPE_AND_DOCUMENT_GROUP_KEY.get(
                device_type, DocumentGroupKeyName.OTHER.value
            )
        )
        document_group_mapping = await self.get_document_group_mapping(
            tenant_db_session
        )
        document_group_id = document_group_mapping.get(document_group_key_name)

        document_management = DocumentManagement(
            patient_user_id=object_insert.get("patient_user_id"),
            name=object_insert.get("image_file_name"),
            status=DocumentStatus.ACTIVATED.value,
            data_type=DocumentDataType.ORIGINAL.value,
            document_data={
                "1": object_insert.get("image_file_path"),
            },
            preview_document_data={
                "1": object_insert.get("preview_image_file_path"),
            },
            examination_date=object_insert.get("examination_date"),
            medical_history_id=None,
            document_uuid=str(uuid4()),
            display_mode=DocumentDisplayMode.SINGLE_IMAGE.value,
            document_extension=DocumentExtension.IMAGE.value,
            extra_data={
                "treatment_image_id": treatment_image_id,
            },
            document_group_id=document_group_id,
        )
        tenant_db_session.add(document_management)
        await tenant_db_session.flush()
        return document_management.id

    @staticmethod
    async def get_document_group_mapping(db_session: AsyncSession) -> dict[str, int]:
        stmt = select(DocumentGroup.id, DocumentGroup.key_name)
        result = await db_session.execute(stmt)
        rows = result.all()
        mapping = {row.key_name: row.id for row in rows}
        return mapping
