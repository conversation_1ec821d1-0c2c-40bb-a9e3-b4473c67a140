from datetime import datetime

from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from schemas.requests.instrumental_examination_schema import (
    CreatePatientInstrumentalExaminationRequest,
    FiDataSchema,
    RawDataSchema,
)
from services.central_db.base_service import BaseCentralService
from sqlalchemy import func, select
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import PatientMedicalDevice
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import (
    PatientMedicalDevice as TenantPatientMedicalDevice,
)
from gc_dentist_shared.tenant_models import PatientUser


class InstrumentalExaminationService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def create_patient_instrumental_examination(
        self, clinic_code: str, payload: CreatePatientInstrumentalExaminationRequest
    ):
        """Create or update patient instrumental examination data."""
        log.info(
            f"Start create patient instrumental examination for clinic code: {clinic_code}"
        )

        raw_data = payload.raw_data
        fi_data = raw_data.fi_data
        examination_date = self._parse_examination_date(fi_data.examination_date)

        async with self.session.begin():
            # Get tenant information
            base_central_service = BaseCentralService(self.session)
            tenant_clinic = await base_central_service.get_tenant_by_clinic_no(
                clinic_code
            )

            # Sync data to tenant database
            tenant_patient_medical_device = (
                await self._sync_patient_medical_device_to_tenant(
                    tenant_clinic.tenant_uuid,
                    tenant_clinic.db_name,
                    fi_data,
                    raw_data,
                )
            )

            # Save or update data in central database
            # TODO(TuanTC) The system hasn't yet handled the rollback scenario when an update
            #  to the tenant database succeeds but fails in the central database.
            await self._upsert_central_patient_medical_device(
                tenant_clinic.tenant_uuid,
                clinic_code,
                tenant_patient_medical_device.patient_user_id,
                examination_date,
                fi_data,
                raw_data,
            )

            log.info(
                f"End create patient instrumental examination for clinic code {clinic_code}"
            )

    # region Private Methods
    def _parse_examination_date(self, date_str: str):
        return datetime.strptime(date_str, "%Y-%m-%d").date()

    async def _upsert_central_patient_medical_device(
        self,
        tenant_uuid,
        clinic_no,
        patient_user_id,
        examination_date,
        fi_data: FiDataSchema,
        raw_data: RawDataSchema,
    ):
        """Insert or update patient medical device in central database."""
        device_data = {
            "tenant_uuid": tenant_uuid,
            "clinic_no": clinic_no,
            "patient_user_id": patient_user_id,
            "device_type": fi_data.type,
            "device_data_id": fi_data.id,
            "external_patient_no": fi_data.patient_no,
            "examination_date": examination_date,
            "raw_data": raw_data.model_dump(),
            "image_file_name": (
                raw_data.image_file_name if raw_data.image_file_name else None
            ),
        }

        insert_stmt = insert(PatientMedicalDevice).values(device_data)

        # Define conflict columns based on unique constraint
        conflict_columns = [
            "clinic_no",
            "patient_user_id",
            "device_type",
            "device_data_id",
        ]
        immutable_cols = [
            "id",
            "created_at",
            "tenant_uuid",
            "clinic_no",
            "patient_user_id",
            "device_type",
            "device_data_id",
        ]

        # Build update values excluding immutable columns
        update_values = {
            col: getattr(insert_stmt.excluded, col)
            for col in device_data.keys()
            if col not in immutable_cols
        }
        update_values["updated_at"] = func.now()

        upsert_stmt = insert_stmt.on_conflict_do_update(
            index_elements=conflict_columns, set_=update_values
        )

        await self.session.execute(upsert_stmt)

    async def _sync_patient_medical_device_to_tenant(
        self,
        tenant_uuid: str,
        tenant_db_name: str,
        fi_data: FiDataSchema,
        raw_data: RawDataSchema,
    ):
        """Sync patient medical device data to tenant database."""
        token = set_current_db_name(tenant_db_name)
        try:
            async with TenantDatabase.get_instance_tenant_db() as tenant_db_session:
                async with tenant_db_session.begin():
                    log.info(
                        f"Start sync patient medical device for tenant: {tenant_uuid}"
                    )

                    examination_date = self._parse_examination_date(
                        fi_data.examination_date
                    )
                    patient_user = await self._get_and_validate_patient_user(
                        tenant_db_session, fi_data
                    )

                    tenant_patient_medical_device = (
                        await self._upsert_tenant_patient_medical_device(
                            tenant_db_session,
                            patient_user.id,
                            examination_date,
                            fi_data,
                            raw_data,
                        )
                    )

                    log.info(
                        f"End sync patient medical device for tenant: {tenant_uuid}"
                    )
                    return tenant_patient_medical_device
        except Exception as e:
            log.error(f"❌ Error sync patient medical device to tenant: {str(e)}")
            raise
        finally:
            reset_current_db_name(token)

    async def _get_and_validate_patient_user(
        self, tenant_db_session: AsyncSession, fi_data: FiDataSchema
    ):
        query = select(PatientUser).where(
            PatientUser.patient_no == fi_data.patient_no,
            PatientUser.status.is_(True),
        )
        result = await tenant_db_session.execute(query)
        patient_user = result.scalar_one_or_none()

        if not patient_user:
            raise CustomValueError(
                message=CustomMessageCode.PATIENT_NOT_FOUND.title,
                message_code=CustomMessageCode.PATIENT_NOT_FOUND.code,
            )
        return patient_user

    async def _upsert_tenant_patient_medical_device(
        self,
        tenant_db_session: AsyncSession,
        patient_user_id,
        examination_date,
        fi_data: FiDataSchema,
        raw_data: RawDataSchema,
    ):
        """Insert or update patient medical device in tenant database."""
        device_data = {
            "external_patient_no": fi_data.patient_no,
            "patient_user_id": patient_user_id,
            "device_type": fi_data.type,
            "device_data_id": fi_data.id,
            "examination_date": examination_date,
            "raw_data": raw_data.model_dump(),
            "image_file_name": (
                raw_data.image_file_name if raw_data.image_file_name else None
            ),
        }

        insert_stmt = insert(TenantPatientMedicalDevice).values(device_data)

        # Define conflict columns based on unique constraint
        conflict_columns = ["patient_user_id", "device_type", "device_data_id"]
        immutable_cols = [
            "id",
            "created_at",
            "patient_user_id",
            "device_type",
            "device_data_id",
        ]

        # Build update values excluding immutable columns
        update_values = {
            col: getattr(insert_stmt.excluded, col)
            for col in device_data.keys()
            if col not in immutable_cols
        }
        update_values["updated_at"] = func.now()

        upsert_stmt = insert_stmt.on_conflict_do_update(
            index_elements=conflict_columns, set_=update_values
        ).returning(TenantPatientMedicalDevice)

        result = await tenant_db_session.execute(upsert_stmt)
        return result.scalar_one()
