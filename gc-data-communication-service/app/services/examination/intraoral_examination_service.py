from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabaseReadOnly
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from schemas.requests.oral_examination_schema import (
    IntraoralExaminationDetailRequest,
    IntraoralExaminationRequest,
)
from schemas.responses.oral_examination_schema import IntraoralExaminationResponse
from services.central_db.base_service import BaseCentralService
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import OralExamination, PatientUser


class IntraoralExaminationService:
    def __init__(self, central_db_session: AsyncSession):
        self.central_db_session = central_db_session

    async def get_intraoral_examinations(
        self, request_data: IntraoralExaminationRequest
    ) -> Page[IntraoralExaminationResponse]:
        log.info(
            f" START Search intraoral examination for clinic_no: {request_data.clinic_code}, "
            f"patient_number: {request_data.patient_number}"
        )

        clinic_no = request_data.clinic_code
        base_central_service = BaseCentralService(self.central_db_session)
        tenant = await base_central_service.get_tenant_by_clinic_no(clinic_no)

        if not tenant:
            raise CustomValueError(
                message=CustomMessageCode.TENANT_NOT_FOUND.title,
                message_code=CustomMessageCode.TENANT_NOT_FOUND.code,
            )

        result = await self._get_intraoral_examination_from_tenant(
            request_data, tenant.db_name
        )

        log.info(" END Search intraoral examination")

        return result

    async def get_intraoral_examination_detail(
        self, request_data: IntraoralExaminationDetailRequest
    ):
        examination_id = (
            request_data.examination_id if request_data.examination_id else "latest"
        )
        log.info(
            f" START Get intraoral examination detail for intraoral_examination_id: {examination_id}"
        )

        base_central_service = BaseCentralService(self.central_db_session)
        tenant = await base_central_service.get_tenant_by_clinic_no(
            request_data.clinic_code
        )

        if not tenant:
            raise CustomValueError(
                message=CustomMessageCode.TENANT_NOT_FOUND.title,
                message_code=CustomMessageCode.TENANT_NOT_FOUND.code,
            )

        # TODO: return all data, need confirm
        result = await self._get_intraoral_examination_detail_from_tenant(
            tenant.db_name, request_data.examination_id, request_data.patient_number
        )
        log.info(" END Get intraoral examination detail")

        return result

    # Region Private Methods

    async def _get_intraoral_examination_from_tenant(
        self,
        request_data: IntraoralExaminationRequest,
        tenant_db_name: str,
    ):
        token = set_current_db_name(tenant_db_name)
        try:
            async with (
                TenantDatabaseReadOnly.get_instance_tenant_db() as tenant_db_session
            ):
                query = await self._build_search_intraoral_examination(
                    request_data.patient_number
                )
                return await paginate(tenant_db_session, query, unique=False)
        except Exception as e:
            log.error(f"❌ Failed to get intraoral examination from tenant: {str(e)}")
            raise e
        finally:
            reset_current_db_name(token)

    async def _build_search_intraoral_examination(self, patient_number: int):
        log.info(
            f" START Building query for intraoral examination for patient_number: {patient_number}"
        )

        field = [
            OralExamination.id,
            OralExamination.examination_date,
            OralExamination.note,
        ]

        query = (
            select(*field)
            .select_from(OralExamination)
            .join(PatientUser, OralExamination.patient_user_id == PatientUser.id)
            .where(PatientUser.patient_no == patient_number)
            .order_by(OralExamination.examination_date.desc())
        )

        return query

    async def _get_intraoral_examination_detail_from_tenant(
        self, tenant_db_name: str, examination_id: int | None, patient_number: str
    ):
        token = set_current_db_name(tenant_db_name)
        try:
            async with (
                TenantDatabaseReadOnly.get_instance_tenant_db() as tenant_db_session
            ):
                where_clause = [PatientUser.patient_no == patient_number]
                if examination_id:
                    where_clause.append(OralExamination.id == examination_id)

                query = (
                    select(OralExamination.intraoral_examination)
                    .join(
                        PatientUser, OralExamination.patient_user_id == PatientUser.id
                    )
                    .where(
                        *where_clause,
                    )
                    .order_by(OralExamination.examination_date.desc())
                    .limit(1)
                )
                result = await tenant_db_session.execute(query)
                intraoral_examination = result.scalar_one_or_none()

                if not intraoral_examination:
                    raise CustomValueError(
                        message=CustomMessageCode.INTRAORAL_EXAMINATION_NOT_FOUND.title,
                        message_code=CustomMessageCode.INTRAORAL_EXAMINATION_NOT_FOUND.code,
                    )

                return intraoral_examination

        except Exception as e:
            log.error(f"❌ Failed to get oral examination detail from tenant: {str(e)}")
            raise e
        finally:
            reset_current_db_name(token)

    # Endregion
