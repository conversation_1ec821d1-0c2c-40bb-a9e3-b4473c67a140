from db.db_connection import CentralDatabase
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import TenantClinic
from gc_dentist_shared.core.logger.config import log


class TenantClinicService:
    def __init__(self, session: AsyncSession):
        self.session = session

    @staticmethod
    async def get_tenant_by_clinic_no(clinic_no):
        try:
            async with CentralDatabase.get_instance_db() as central_db_session:
                async with central_db_session:
                    result = await central_db_session.execute(
                        select(TenantClinic).where(TenantClinic.clinic_no == clinic_no)
                    )
                    tenant = result.scalar_one_or_none()
                    return tenant
        except Exception as e:
            log.error(f"❌ Error get_patient_id_by_patient_no: {e}")
            return None
