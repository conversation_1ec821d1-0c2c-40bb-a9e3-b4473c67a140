import hashlib
from datetime import date
from typing import Literal

import polars as pl
from configuration.settings import configuration
from core.constants import (
    ALPHANUMERIC_REGEX,
    CSV_DATE_OF_BIRTH_FORMAT,
    KATAKANA_REGEX,
    NUMERIC_HYPHEN_REGEX,
    PATIENT_CSV_ENCODING,
    PATIENT_GENDER_MAPPING,
)
from core.messages import CustomMessageCode
from enums.patient_enum import Gender, PatientColumnMapping
from schemas.responses.patient_schema import PatientCSVData, ProcessedLog
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import GlobalUser
from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.common.strings import format_phone_number
from gc_dentist_shared.core.common.utils import generate_password
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import Patient<PERSON>rofile, PatientUser


class CSVService:
    def __init__(
        self,
        central_db_session: AsyncSession = None,
        tenant_db_session: AsyncSession = None,
    ):
        self.central_db_session = central_db_session
        self.tenant_db_session = tenant_db_session

        self.data_df: pl.DataFrame | None = None
        self.errors: list = []

    async def process_patient_csv(
        self, file_content: bytes, tenant_uuid: str, max_records: int
    ) -> PatientCSVData:
        try:
            # 1. Validate file format
            self.data_df = pl.read_csv(
                file_content,
                encoding=PATIENT_CSV_ENCODING,
                infer_schema=False,
            )
            self._validate_file_format(
                max_records=max_records,
                column_headers=PatientColumnMapping.get_column_headers(),
            )
            total_counts = self.data_df.height

            # 2. Validate data format
            self._rename_columns(dict(PatientColumnMapping.get_member_values()))
            self.data_df = self.data_df.with_row_index(
                "row_num", offset=1
            )  # 1-based index
            self._validate_data_format()

            # 3. Normalize data
            self._normalize_phone_numbers(
                column_names=[
                    PatientColumnMapping.TELEPHONE_NUMBER_1.column_name,
                    PatientColumnMapping.TELEPHONE_NUMBER_2.column_name,
                ],
            )

            self._remove_hyphen_characters(
                column_names=[PatientColumnMapping.POSTAL_CODE.column_name]
            )
            self._normalize_gender()

            aes_gcm = AesGCMRotation(configuration)
            self._encrypt_columns(
                aes_gcm=aes_gcm,
                column_names=[
                    PatientColumnMapping.DATE_OF_BIRTH.column_name,
                    PatientColumnMapping.TELEPHONE_NUMBER_1.column_name,
                    PatientColumnMapping.TELEPHONE_NUMBER_2.column_name,
                    PatientColumnMapping.PATIENT_NUMBER.column_name,
                ],
            )
            self._hash_columns(
                aes_gcm=aes_gcm,
                column_names=[
                    PatientColumnMapping.DATE_OF_BIRTH.column_name,
                    PatientColumnMapping.TELEPHONE_NUMBER_1.column_name,
                    PatientColumnMapping.TELEPHONE_NUMBER_2.column_name,
                ],
            )

            await self.append_patient_user_id()
            await self.append_global_user_info()
            self._append_password(tenant_uuid=tenant_uuid)

            skip_counts = self._remove_duplicates(
                column_name=PatientColumnMapping.PATIENT_NUMBER.column_name
            )
            duplicate_phone_counts = self._remove_duplicates(
                column_name="telephone_number_1_hash", keep="none"
            )
            skip_counts += duplicate_phone_counts

            await self._validate_existing_phone_numbers(
                phone_column_name="telephone_number_1_hash"
            )

            create_counts = self.data_df["patient_user_id"].is_null().sum()
            update_counts = self.data_df.height - create_counts

            return PatientCSVData(
                data_df=self.data_df,
                processed_log=ProcessedLog(
                    total_counts=total_counts,
                    skip_counts=skip_counts,
                    create_counts=create_counts,
                    update_counts=update_counts,
                    failure_counts=len(self.errors),
                    errors=self.errors,
                ),
            )

        except CustomValueError as e:
            log.error(f"❌ Process validation error: {e.message}")
            raise e

        except Exception as e:
            log.error(f"❌ Process csv error: {str(e)}")
            raise e

        finally:
            self.data_df = None

    def _validate_file_format(self, max_records: int, column_headers: list[str]):
        missing_headers = set(column_headers) - set(self.data_df.columns)
        if missing_headers:
            log.error(f"❌ CSV missing columns: {', '.join(missing_headers)}")
            raise CustomValueError(
                message_code=CustomMessageCode.CSV_COLUMNS_MISSING.code,
                message=CustomMessageCode.CSV_COLUMNS_MISSING.title.format(
                    column_headers=", ".join(missing_headers)
                ),
            )

        if self.data_df.is_empty():
            log.error("❌ CSV is empty")
            raise CustomValueError(
                message_code=CustomMessageCode.CSV_FILE_IS_EMPTY.code,
                message=CustomMessageCode.CSV_FILE_IS_EMPTY.title,
            )

        df_total_records = self.data_df.height
        if df_total_records > max_records:
            log.error(
                f"❌ CSV total records: {df_total_records} exceeds max records: {max_records}"
            )
            raise CustomValueError(
                message_code=CustomMessageCode.CSV_FILE_INVALID_RECORD.code,
                message=CustomMessageCode.CSV_FILE_INVALID_RECORD.title,
            )

    def _validate_data_format(self):
        """
        Validates the data format of the DataFrame.

        Format: (column_name, validation_rule, error_message)
        """

        validation_rules = [
            (
                "patient_number",
                lambda x: x.is_not_null(),
                CustomMessageCode.REQUIRED_FIELD_ERROR.title.format(
                    column_name="patient_number"
                ),
            ),
            (
                "patient_number",
                lambda x: x.str.len_chars() <= 30,
                CustomMessageCode.MAX_LENGTH_FIELD_ERROR.title.format(
                    column_name="patient_number", max_length=30
                ),
            ),
            (
                "patient_number",
                lambda x: x.is_null() | x.str.contains(ALPHANUMERIC_REGEX),
                CustomMessageCode.INVALID_VALUE_ERROR.title.format(
                    column_name="patient_number"
                ),
            ),
            (
                "full_name",
                lambda x: x.is_not_null(),
                CustomMessageCode.REQUIRED_FIELD_ERROR.title.format(
                    column_name="full_name"
                ),
            ),
            (
                "full_name",
                lambda x: x.str.len_chars() <= 60,
                CustomMessageCode.MAX_LENGTH_FIELD_ERROR.title.format(
                    column_name="full_name", max_length=60
                ),
            ),
            (
                "full_name_kana",
                lambda x: x.is_not_null(),
                CustomMessageCode.REQUIRED_FIELD_ERROR.title.format(
                    column_name="full_name_kana"
                ),
            ),
            (
                "full_name_kana",
                lambda x: x.str.len_chars() <= 60,
                CustomMessageCode.MAX_LENGTH_FIELD_ERROR.title.format(
                    column_name="full_name_kana", max_length=60
                ),
            ),
            (
                "full_name_kana",
                lambda x: x.str.contains(KATAKANA_REGEX),
                CustomMessageCode.INVALID_KATAKANA_ERROR.title,
            ),
            (
                "gender",
                lambda x: x.is_null() | x.is_in(PATIENT_GENDER_MAPPING.keys()),
                CustomMessageCode.INVALID_GENDER_ERROR.title,
            ),
            (
                "date_of_birth",
                lambda x: x.is_not_null(),
                CustomMessageCode.REQUIRED_FIELD_ERROR.title.format(
                    column_name="date_of_birth"
                ),
            ),
            (
                "date_of_birth",
                lambda x: x.is_null()
                | x.str.strptime(
                    pl.Date, CSV_DATE_OF_BIRTH_FORMAT, strict=False
                ).is_not_null(),
                CustomMessageCode.INVALID_DATE_ERROR.title,
            ),
            (
                "date_of_birth",
                lambda x: x.is_null()
                | (
                    x.str.strptime(pl.Date, CSV_DATE_OF_BIRTH_FORMAT, strict=False)
                    <= pl.lit(date.today(), dtype=pl.Date)
                ),
                CustomMessageCode.FUTURE_DATE_OF_BIRTH_ERROR.title,
            ),
            (
                "postal_code",
                lambda x: x.is_null() | x.str.len_chars() <= 8,
                CustomMessageCode.MAX_LENGTH_FIELD_ERROR.title.format(
                    column_name="postal_code", max_length=8
                ),
            ),
            (
                "postal_code",
                lambda x: x.is_null() | x.str.contains(NUMERIC_HYPHEN_REGEX),
                CustomMessageCode.INVALID_VALUE_ERROR.title.format(
                    column_name="postal_code"
                ),
            ),
            (
                "telephone_number_1",
                lambda x: x.is_null()
                | ((x.str.len_chars() <= 13) & x.str.contains(NUMERIC_HYPHEN_REGEX)),
                CustomMessageCode.INVALID_VALUE_ERROR.title.format(
                    column_name="telephone_number_2"
                ),
            ),
            (
                "telephone_number_2",
                lambda x: x.is_null()
                | ((x.str.len_chars() <= 13) & x.str.contains(NUMERIC_HYPHEN_REGEX)),
                CustomMessageCode.INVALID_VALUE_ERROR.title.format(
                    column_name="telephone_number_2"
                ),
            ),
        ]

        validation_expressions = [
            pl.when(~validation_rule(pl.col(column_name))).then(pl.lit(error_message))
            for column_name, validation_rule, error_message in validation_rules
        ]

        # Gather errors
        df_with_errors = self.data_df.with_columns(
            errors=pl.concat_list(validation_expressions).list.drop_nulls()
        )

        invalid_df = df_with_errors.filter(pl.col("errors").list.len() > 0)
        for row in invalid_df.rows(named=True):
            row_validation_error = {
                "line": row["row_num"],
                "patient_number": row.get("patient_number", ""),
                "messages": row["errors"],
            }
            self.errors.append(row_validation_error)

        # Filter valid rows
        self.data_df = df_with_errors.filter(pl.col("errors").list.len() == 0).drop(
            ["errors"]
        )

    async def _validate_existing_phone_numbers(self, phone_column_name: str):
        """
        Validates if telephone numbers in the CSV already exist for different patients
        """
        if phone_column_name not in self.data_df.columns:
            return

        rows_to_check = self.data_df.filter(pl.col(phone_column_name).is_not_null())
        if rows_to_check.is_empty():
            return

        phone_hashes = rows_to_check.get_column(phone_column_name).to_list()
        existing_profiles_data = await self._query_patient_profile_by_phone_hash(
            phone_hashes
        )
        if not existing_profiles_data:
            return

        existing_profiles_df = pl.DataFrame(existing_profiles_data).rename(
            {"phone_hash": phone_column_name}
        )

        conflicts_df = rows_to_check.join(
            existing_profiles_df, on=phone_column_name, how="inner"
        ).filter(
            (pl.col("patient_user_id").is_null())
            | (pl.col("patient_user_id") != pl.col("existing_patient_user_id"))
        )

        if conflicts_df.is_empty():
            return

        conflict_errors = conflicts_df.select(
            pl.col("row_num").alias("line"),
            pl.col("patient_number").fill_null(""),
            pl.lit([CustomMessageCode.PHONE_EXISTS_ERROR.title]).alias("messages"),
        ).to_dicts()
        self.errors.extend(conflict_errors)

        conflict_rows = conflicts_df.get_column("row_num")
        self.data_df = self.data_df.filter(~pl.col("row_num").is_in(conflict_rows))

    def _remove_hyphen_characters(self, column_names: list[str]):
        columns_to_normalize = set(column_names) & set(self.data_df.columns)
        for column_name in columns_to_normalize:
            self.data_df = self.data_df.with_columns(
                pl.col(column_name)
                .str.replace("-", "", literal=True)
                .alias(column_name)
            )

    def _normalize_phone_numbers(self, column_names: list[str]):
        columns_to_normalize = set(column_names) & set(self.data_df.columns)
        for column_name in columns_to_normalize:
            self.data_df = self.data_df.with_columns(
                pl.col(column_name)
                .map_elements(lambda x: self.format_phone_number_helper(x))
                .alias(column_name)
            )

    def _normalize_gender(self):
        gender_column = PatientColumnMapping.GENDER.column_name

        if gender_column not in self.data_df.columns:
            return

        self.data_df = self.data_df.with_columns(
            pl.col(gender_column)
            .replace(PATIENT_GENDER_MAPPING, default=Gender.OTHER.value)
            .cast(pl.Int64)
            .alias(gender_column)
        )

    def _encrypt_columns(self, aes_gcm: AesGCMRotation, column_names: list[str]):
        columns_to_normalize = set(column_names) & set(self.data_df.columns)
        for column_name in columns_to_normalize:
            self.data_df = self.data_df.with_columns(
                pl.col(column_name)
                .map_elements(lambda x: aes_gcm.encrypt_data(x), return_dtype=pl.Utf8)
                .alias(f"{column_name}_encrypt")
            )

    def _hash_columns(self, aes_gcm: AesGCMRotation, column_names: list[str]):
        columns_to_normalize = set(column_names) & set(self.data_df.columns)
        for column_name in columns_to_normalize:
            self.data_df = self.data_df.with_columns(
                pl.col(column_name)
                .map_elements(lambda x: aes_gcm.sha256_hash(x), return_dtype=pl.Utf8)
                .alias(f"{column_name}_hash")
            )

    def _remove_duplicates(
        self, column_name: str, keep: Literal["first", "last", "any", "none"] = "last"
    ) -> int:
        if column_name not in self.data_df.columns:
            return 0

        initial_count = self.data_df.height

        non_null_df = self.data_df.filter(pl.col(column_name).is_not_null())
        self.data_df = self.data_df.filter(~pl.col(column_name).is_not_null())

        deduplicated_df = non_null_df.unique(subset=[column_name], keep=keep)
        self.data_df = pl.concat([self.data_df, deduplicated_df])

        return initial_count - self.data_df.height  # skip counts

    def _rename_columns(self, columns_mapping: dict):
        columns_to_rename = set(columns_mapping.keys()) & set(self.data_df.columns)
        if not columns_to_rename:
            return

        valid_columns_mapping = {
            col_name: columns_mapping[col_name] for col_name in columns_to_rename
        }
        self.data_df = self.data_df.rename(mapping=valid_columns_mapping)

    async def _query_patient_user_ids(self, patient_numbers: list[str]) -> dict:
        if not patient_numbers:
            return {}

        stmt = select(
            PatientUser.id.label("patient_user_id"), PatientUser.patient_no
        ).where(
            PatientUser.patient_no.in_(patient_numbers), PatientUser.status.is_(True)
        )
        result = await self.tenant_db_session.execute(stmt)

        return result.mappings().all()

    async def _query_patient_profile_by_phone_hash(
        self, phone_hashes: list[str]
    ) -> list[dict]:
        if not phone_hashes or not self.tenant_db_session:
            return []

        stmt = select(
            PatientProfile.patient_user_id.label("existing_patient_user_id"),
            PatientProfile.phone_hash,
        ).where(PatientProfile.phone_hash.in_(phone_hashes))
        result = await self.tenant_db_session.execute(stmt)

        return result.mappings().all()

    async def _query_global_user(self, hash_phone_numbers: list[str]) -> dict:
        if not hash_phone_numbers:
            return {}

        stmt = select(
            GlobalUser.id.label("global_user_id"),
            GlobalUser.username_hash,
            GlobalUser.day_of_birth_hash,
        ).where(GlobalUser.username_hash.in_(hash_phone_numbers))
        result = await self.central_db_session.execute(stmt)
        return result.mappings().all()

    async def append_patient_user_id(self):
        """
        Append patient_user_id to current data_df
        """

        patient_numbers = self.data_df.get_column(
            PatientColumnMapping.PATIENT_NUMBER.column_name
        ).to_list()

        patient_mapping = await self._query_patient_user_ids(patient_numbers)
        if not patient_mapping:
            self.data_df = self.data_df.with_columns(
                pl.lit(None, pl.Int64).alias("patient_user_id")
            )
            return

        patient_user_df = pl.DataFrame(patient_mapping)

        self.data_df = self.data_df.join(
            patient_user_df, how="left", left_on="patient_number", right_on="patient_no"
        )

    async def append_global_user_info(self):
        """
        Append global_user_id, day_of_birth_hash to current data_df
        """

        hash_phone_numbers = self.data_df.get_column(
            "telephone_number_1_hash"
        ).to_list()
        global_user_mapping = await self._query_global_user(hash_phone_numbers)
        if not global_user_mapping:
            self.data_df = self.data_df.with_columns(
                pl.lit(None, pl.Int64).alias("global_user_id"),
                pl.lit(None, pl.Utf8).alias("day_of_birth_hash"),
            )
            return

        global_user_df = pl.DataFrame(global_user_mapping)

        # Overwrite global_user_id with None if date_of_birth_hash mismatches the value in DB (reset user mapping)
        self.data_df = self.data_df.join(
            global_user_df,
            how="left",
            left_on="telephone_number_1_hash",
            right_on="username_hash",
        ).with_columns(
            pl.when(pl.col("date_of_birth_hash") != pl.col("day_of_birth_hash"))
            .then(pl.lit(None, dtype=pl.Int64))
            .otherwise(pl.col("global_user_id"))
            .alias("global_user_id")
        )

    def _append_password(self, tenant_uuid: str):
        mask = self.data_df.get_column("patient_user_id").is_null()
        null_count = int(mask.sum())

        plain_passwords = [generate_password() for _ in range(null_count)]
        hashed_iter = (
            hashlib.sha256((pwd + tenant_uuid).encode("utf-8")).hexdigest()
            for pwd in plain_passwords
        )
        hashed_full = [next(hashed_iter) if is_null else None for is_null in mask]

        self.data_df = self.data_df.with_columns(
            pl.Series("password", hashed_full).cast(pl.Utf8)
        )

    @staticmethod
    def format_phone_number_helper(phone: str) -> str | None:
        if phone is None or phone == "":
            return None

        return format_phone_number(phone)
