from configuration.settings import configuration
from core.messages import CustomMessageCode
from enums.s3_enums import DataTypeGeneratedPresignedEnums
from schemas.requests.s3_schemas import S3GeneratedPresignedUrlRequest
from services.patient_service import PatientService
from services.tenant_clinics_service import TenantClinicService

from gc_dentist_shared.core.common.s3_bucket import S3Client
from gc_dentist_shared.core.enums.s3_enums import S3Folder, S3RoleEnum, S3TypeObject
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError


class S3Service:
    async def generate_presigned_url(
        self,
        obj: S3GeneratedPresignedUrlRequest,
    ):
        results = {}
        s3_client = await S3Client.get_instance(configuration)
        for file_info in obj.parsed_files:
            tenant = await TenantClinicService.get_tenant_by_clinic_no(
                clinic_no=file_info.clinic_no
            )
            if not tenant:
                raise CustomValueError(
                    message=CustomMessageCode.CLINIC_INFO_NOT_FOUND.title,
                    message_code=CustomMessageCode.CLINIC_INFO_NOT_FOUND.code,
                )
            patient_id = None
            if obj.data_type == DataTypeGeneratedPresignedEnums.IMAGE.value:
                patient_id = await PatientService.get_patient_id_by_patient_no(
                    db_name=tenant.db_name, patient_no=file_info.patient_no
                )
                if not patient_id:
                    raise CustomValueError(
                        message=CustomMessageCode.PATIENT_NOT_FOUND.title,
                        message_code=CustomMessageCode.PATIENT_NOT_FOUND.code,
                    )

            path_file = self._build_path_file(
                s3_client=s3_client,
                data_type=obj.data_type,
                original_name=file_info.original_name,
                tenant_uuid=tenant.tenant_uuid,
                patient_id=patient_id,
            )

            presigned_url = await s3_client.generate_presigned_url(
                key=path_file,
                type_object=S3TypeObject.PUT_OBJECT.value,
            )
            results[file_info.original_name] = {
                "presigned_url": presigned_url,
                "file_path": path_file,
            }

        return results

    def _build_path_file(
        self, s3_client, data_type, original_name, tenant_uuid, patient_id
    ) -> str:
        prefix = (
            f"{S3Folder.PATIENT_INFO.value}/{tenant_uuid}"
            if data_type == DataTypeGeneratedPresignedEnums.PATIENT_INFO.value
            else f"{S3Folder.MAIN.value}/{tenant_uuid}/{S3RoleEnum.PATIENT.value}/"
            f"{patient_id}/{S3Folder.DOCUMENTS.value}"
        )

        return s3_client.generate_key_s3(
            prefix=prefix, object_name=original_name, add_date_path=False
        )
