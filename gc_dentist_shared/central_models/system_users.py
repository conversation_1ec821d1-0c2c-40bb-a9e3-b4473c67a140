import hashlib

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class SystemUser(CentralBase, DateTimeMixin):
    __tablename__ = "system_users"
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String, nullable=False, unique=True)
    password = Column(String, nullable=False)
    is_active = Column(Boolean, nullable=False, default=True)
    last_login = Column(TIMESTAMP(timezone=True), nullable=True)
    otp_lock_expires_at = Column(TIMESTAMP(timezone=True), nullable=True)

    def set_password(self, plain_password):
        if plain_password is None:
            raise Exception("plain_password cannot be None")

        if plain_password is not None:
            self.password = hashlib.sha256((plain_password).encode("utf-8")).hexdigest()
        else:
            self.password = None

    def validate_password(self, plain_password):
        if plain_password is None or self.password is None:
            return False
        hashed = hashlib.sha256((plain_password).encode("utf-8")).hexdigest()
        return hashed == self.password
