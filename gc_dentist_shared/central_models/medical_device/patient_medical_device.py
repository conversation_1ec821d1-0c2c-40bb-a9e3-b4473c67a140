from sqlalchemy import <PERSON>umn, Date, Foreign<PERSON><PERSON>, Integer, String, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB, UUID

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class PatientMedicalDevice(CentralBase, DateTimeMixin):
    """
    Model to store measurement data from medical device.
    """

    __tablename__ = "patient_medical_devices"

    id = Column(Integer, primary_key=True, autoincrement=True)
    tenant_uuid = Column(
        UUID(as_uuid=True),
        ForeignKey("tenant_clinics.tenant_uuid"),
        nullable=False,
        comment="Foreign key to the TenantClinic table",
    )
    clinic_no = Column(
        String,
        nullable=False,
        comment="Identifier for the business establishment/clinic",
    )
    patient_user_id = Column(
        Integer,
        nullable=False,
        comment="ID of the patient in the internal system (if available)",
    )
    device_type = Column(
        String(), nullable=False, comment="Enums: MedicalDeviceType, BFA, EMG,...."
    )
    device_data_id = Column(
        String, nullable=False, comment="Unique data identifier from the device"
    )
    external_patient_no = Column(
        String,
        nullable=False,
        comment="Patient number from the external system or device",
    )
    examination_date = Column(
        Date, nullable=False, comment="Date of the examination/measurement"
    )
    raw_data = Column(
        JSONB,
        nullable=True,
        comment="Data medical device",
    )
    image_file_name = Column(String(), nullable=True, comment="File image name")

    __table_args__ = (
        UniqueConstraint(
            "clinic_no",
            "patient_user_id",
            "device_type",
            "device_data_id",
            name="uq_patient_medical_device_per_device",
        ),
    )
