from sqlalchemy import Column, Integer, String

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class MasterMedicalDevice(CentralBase, DateTimeMixin):
    """Master permissions table in central database"""

    __tablename__ = "m_medical_devices"

    id = Column(Integer, primary_key=True, autoincrement=True)
    device_type = Column(
        String(),
        nullable=False,
        unique=True,
        comment="Enums: MedicalDeviceType, BFA, EMG,....",
    )
    name = Column(String(), nullable=False, comment="Name medical device")
    description = Column(String(), comment="Description Medical Device")
