from sqlalchemy import Column, Integer, String
from sqlalchemy.dialects.postgresql import JSONB

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class ProcessFileLog(CentralBase, DateTimeMixin):
    """
    table process_file_logs
    """

    __tablename__ = "process_file_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    source = Column(
        String(),
        nullable=False,
        comment="Enums: SourceProcessFileLog (MIDDLEWARE_MEDICAL_APP, ...)",
    )

    file_path = Column(String(), nullable=False, comment="S3 path or local path")
    file_type = Column(
        Integer,
        nullable=False,
        comment="Enums: FileTypeProcessFileEnum",
    )
    status = Column(
        Integer,
        nullable=False,
        comment="Enums: StatusProcessFileEnum",
    )

    result = Column(
        JSONB, nullable=True, comment="JSONB result payload (parsed rows, keys, etc.)"
    )
    error_message = Column(String(), nullable=True, comment="Error message if FAILED")
