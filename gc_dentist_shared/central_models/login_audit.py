from datetime import datetime, timezone

from sqlalchemy import TEXT, TIMESTAMP, Column, Integer, String
from sqlalchemy.sql import func

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class LoginAudit(CentralBase, DateTimeMixin):
    __tablename__ = "login_audit"
    id = Column(Integer, primary_key=True, autoincrement=True)
    client_id = Column(String(48), nullable=True)
    global_user_id = Column(Integer, nullable=True)
    clinic_patient_id = Column(Integer, nullable=True)
    clinic_doctor_id = Column(Integer, nullable=True)
    system_user_id = Column(Integer, nullable=True)
    tenant_uuid = Column(TEXT, nullable=True)
    ip_address = Column(String(50), nullable=True)
    user_agent = Column(TEXT, nullable=True)
    login_at = Column(
        TIMESTAMP(timezone=True),
        nullable=True,
        default=datetime.now(timezone.utc),
        server_default=func.now(),
    )
    status = Column(Integer, nullable=False)
    failure_reason = Column(TEXT, nullable=True)
