from uuid import uuid4

from sqlalchemy import J<PERSON><PERSON>, Column, Integer, String
from sqlalchemy.dialects.postgresql import UUID

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class TenantClinic(CentralBase, DateTimeMixin):
    __tablename__ = "tenant_clinics"

    tenant_uuid = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    clinic_name = Column(String, nullable=False, unique=True)
    clinic_no = Column(String, nullable=False, unique=True)
    db_name = Column(String, nullable=False)
    db_uri = Column(String, nullable=True)
    status = Column(Integer, default=20)
    step_data_errors = Column(JSON, nullable=True)
    phone_number = Column(String, nullable=True)
    email = Column(String, nullable=True)
    address_1 = Column(String, nullable=True)
    address_2 = Column(String, nullable=True)
    address_3 = Column(String, nullable=True)
    latitude = Column(String, nullable=True)
    longitude = Column(String, nullable=True)
    logo_url = Column(String, nullable=True)
    opening_hours = Column(JSON, nullable=True)
