from sqlalchemy import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.dialects.postgresql import UUID

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class TenantExtraStorage(CentralBase, DateTimeMixin):
    """Tenant extra storage table in central database"""

    __tablename__ = "tenant_extra_storages"

    id = Column(Integer, primary_key=True, autoincrement=True)
    tenant_uuid = Column(UUID(as_uuid=True), ForeignKey("tenant_clinics.tenant_uuid"))
    storage_key_id = Column(
        Integer, ForeignKey("m_pricing_storages.storage_key_id"), nullable=True
    )
    expired_at = Column(
        TIMESTAMP(timezone=True),
        nullable=True,
        comment="Expiration date of the storage plan",
    )
    status = Column(
        Integer,
        nullable=False,
        default=3,
        comment="Enums TenantExtraStorageStatus",
    )
