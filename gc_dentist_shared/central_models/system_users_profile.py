from sqlalchemy import Column, <PERSON><PERSON><PERSON>, Integer, String

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class SystemUserProfile(CentralBase, DateTimeMixin):
    __tablename__ = "system_user_profiles"

    id = Column(Integer, primary_key=True)
    system_user_id = Column(Integer, ForeignKey("system_users.id"), nullable=False)
    email = Column(String, nullable=False, unique=True)
    email_hash = Column(String, nullable=False, unique=True)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    first_name_kana = Column(String, nullable=True)
    last_name_kana = Column(String, nullable=True)
    date_of_birth = Column(String, nullable=True, comment="Date of birth")
    phone = Column(String, nullable=True, unique=True)
    phone_hash = Column(String, nullable=True, unique=True)
    country_code = Column(String, nullable=True)
    postal_code = Column(String, nullable=True)
    address_1 = Column(String, nullable=True)
    address_2 = Column(String, nullable=True)
    address_3 = Column(String, nullable=True)
