import time
from uuid import uuid4

from authlib.integrations.sqla_oauth2 import OAuth2TokenMixin
from sqlalchemy import TEXT, UUID, Column, ForeignKey, Integer

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class OAuth2Token(CentralBase, DateTimeMixin, OAuth2TokenMixin):
    __tablename__ = "oauth_tokens"

    token_uuid = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    global_user_id = Column(Integer, ForeignKey("global_users.id"), nullable=True)
    clinic_patient_id = Column(Integer, nullable=True)
    clinic_doctor_id = Column(Integer, nullable=True)
    system_user_id = Column(ForeignKey("system_users.id"), nullable=True)
    tenant_uuid = Column(TEXT, nullable=True)
    refresh_token_expires_at = Column(Integer, nullable=True)
    access_token = Column(TEXT, nullable=False)
    refresh_token = Column(TEXT, nullable=False)
    # token = Column(String, unique=True, nullable=False)
    # expires_at = Column(DateTime, nullable=False)

    # revoked = Column(Boolean, default=False)

    def is_refresh_token_expired(self):
        if not self.refresh_token_expires_at:
            return False
        return self.refresh_token_expires_at < int(time.time())


# from authlib.integrations.sqla_oauth2 import OAuth2AuthorizationCodeMixin

# class OAuthAuthorizationCode(Base, OAuth2AuthorizationCodeMixin):
#     __tablename__ = "oauth_authorization_codes"

#     id = Column(Integer, primary_key=True)
#     user_id = Column(Integer, ForeignKey("users.id"))
#     client_id = Column(Integer, ForeignKey("oauth_clients.id"))

#     user = relationship("User")
#     client = relationship("OAuthClient")
