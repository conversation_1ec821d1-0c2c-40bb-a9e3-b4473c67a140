from enum import Enum

from gc_dentist_shared.core.common.i18n import i18n as _

INVALID_EMAIL_ADDRESS_NEWLINES = (
    "Invalid address; address parts cannot contain newlines."
)


class CustomMessageCode(Enum):
    """Custom message codes for application-specific errors and messages."""

    def __new__(cls, code, title, description):
        obj = object.__new__(cls)
        obj._value_ = code
        obj._title = title
        obj._description = description
        return obj

    @property
    def code(self):
        return self.value

    @property
    def title(self):
        # Using the i18n instance to translate the title
        return _(self._title)

    @property
    def description(self):
        return self._description

    def __str__(self):
        return f"{self.code} - {self.title}: {self.description}"

    # Invalid Input Errors
    INVALID_REQUEST_PAYLOAD = (
        4422,
        "Invalid request payload!",
        "The request payload is invalid or improperly formatted",
    )
    INVALID_DATE_STRING_TYPE = (
        4423,
        "Invalid date string type!",
        "The provided date string is not in the correct format or type.",
    )
    INVALID_DATETIME_TYPE = (
        4424,
        "Invalid datetime type!",
        "The provided datetime is not in the correct format or type.",
    )
    INVALID_UUID_STRING = (
        4425,
        "Invalid UUID string!",
        "The provided UUID string is not valid.",
    )
    NEGATIVE_FILE_SIZE = (4426, "Negative file size!", "File size cannot be negative.")

    REQUEST_OBJECT_NOT_FOUND = (
        4427,
        "Request object not found!",
        "Request object not found in function arguments",
    )

    TENANT_UUID_HEADER_REQUIRED = (
        4428,
        "Tenant UUID header required!",
        "X-Tenant-UUID header is required for this operation",
    )

    TENANT_STORAGE_LIMIT_EXCEEDED = (
        4429,
        "Storage limit exceeded!",
        "Tenant has exceeded the allocated storage limit and cannot store additional data",
    )

    TENANT_STORAGE_LIMIT_WARNING = (
        4430,
        "Storage limit warning!",
        "Tenant is approaching the allocated storage limit, please consider freeing up space",
    )

    TENANT_STORAGE_LIMIT_FREE = (
        4431,
        "Storage space available",
        "Tenant has sufficient storage space available for use",
    )

    STORAGE_MANAGEMENT_ERROR = (
        4432,
        "Storage management error!",
        "Error occurred while checking storage management",
    )
    INVALID_STORAGE_UNIT = (
        4433,
        "Invalid storage unit!",
        "The provided storage unit is not supported.",
    )

    TENANT_NOT_FOUND = (
        4004,
        "Missing tenant UUID!",
        "The request is missing the tenant UUID in the headers.",
    )

    # Authentication Errors
    UNAUTHORIZED_ERROR = (
        4401,
        "Unauthorized!",
        "You must be authenticated to access this resource.",
    )
    TOKEN_EXPIRED = (
        4402,
        "Token expired!",
        "The authentication token has expired.",
    )
    FORBIDDEN_ERROR = (
        4403,
        "Tenant not found!",
        "The specified tenant does not exist.",
    )

    # Server Errors
    UNKNOWN_ERROR = (
        5000,
        _("Unknown error!", "「不明なエラーです!"),
        "An unexpected error occurred.",
    )
    TWILIO_ERROR = (
        5001,
        "Twilio error!",
        "An error occurred while processing the Twilio request.",
    )
    TWILIO_SEND_MESSAGE_ERROR = (
        5002,
        "Twilio send message error!",
        "An error occurred while sending a message via Twilio.",
    )
    S3_BUCKET_ERROR = (
        5003,
        "S3 bucket error!",
        "An error occurred while interacting with the S3 bucket.",
    )
    S3_BUCKET_KEY_NOT_FOUND = (
        5004,
        "S3 bucket key not found!",
        "The specified key does not exist in the S3 bucket.",
    )

    INVALID_FILE_EXTENSION = (
        6001,
        "Invalid File Extension",
        "The provided file does not have a valid extension."
        "Allowed extensions are: json, csv, pdf, xlsx, jpg, jpeg, png, gif, heic, heif, webp, avif, svg",
    )

    INVALID_LENGTH_FILE_NAME = (
        6002,
        "Invalid File Name Length",
        "The file name exceeds the maximum allowed length. "
        "Please rename the file to be shorter and try again.",
    )

    MISSING_AUTH_HEADERS = (
        7000,
        "Missing authentication headers",
        "Authentication headers X-Timestamp or X-Signature are missing.",
    )

    INVALID_SIGNATURE = (
        7001,
        "Invalid HMAC signature",
        "The provided HMAC signature does not match the expected value.",
    )

    SIGNATURE_MISSING = (
        7002,
        "HMAC signature missing",
        "The request did not include the required X-Signature header.",
    )

    TIMESTAMP_MISSING = (
        7003,
        "Timestamp header missing",
        "The request did not include the required X-Timestamp header.",
    )
