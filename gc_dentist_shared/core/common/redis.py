#!/usr/bin/env python3
import asyncio
import sys  # noqa: F401
from typing import Optional

from redis.asyncio.client import Redis
from redis.exceptions import AuthenticationError, TimeoutError

from gc_dentist_shared.core.logger.config import log


class RedisCli(Redis):
    _instance = None
    _lock = asyncio.Lock()

    def __init__(self, configuration):
        super().__init__(
            host=configuration.REDIS_HOST,
            port=configuration.REDIS_PORT,
            password=configuration.REDIS_PASSWORD,
            db=configuration.REDIS_DATABASE,
            socket_timeout=configuration.REDIS_TIMEOUT,
            decode_responses=True,  # Transcode utf-8
            ssl=configuration.REDIS_SSL,
            ssl_cert_reqs=None,
        )
        self.configuration = configuration

    @classmethod
    async def get_instance(cls, configuration):
        async with cls._lock:
            if cls._instance is None:
                cls._instance = RedisCli(configuration=configuration)
        return cls._instance

    async def open(self):
        """
        Trigger initial connection

        :return:
        """
        try:
            await self.ping()
        except TimeoutError:
            log.error("❌ Database redis Connection timed out")
            sys.exit()
        except AuthenticationError:
            log.error("❌ Database redis Connection authentication failed")
            sys.exit()
        except Exception as e:
            log.error(f"❌ Database redis Connection abnormality {e}")
            sys.exit()

    async def delete_prefix(self, prefix: str, exclude: Optional[str | list] = None):
        """
        Delete all keys with the specified prefix

        :param prefix:
        :param exclude:
        :return:
        """
        keys = []
        async for key in self.scan_iter(match=f"{prefix}*"):
            if isinstance(exclude, str):
                if key != exclude:
                    keys.append(key)
            elif isinstance(exclude, list):
                if key not in exclude:
                    keys.append(key)
            else:
                keys.append(key)
        for key in keys:
            await self.delete(key)
