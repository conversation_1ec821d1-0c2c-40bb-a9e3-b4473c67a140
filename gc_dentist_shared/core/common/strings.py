import re

import phonenumbers

from gc_dentist_shared.core.constants import (
    PARAGRAPH_WITH_BREAK_OR_END_REGEX,
    REMOVE_HTML_TAGS_REGEX,
)
from gc_dentist_shared.core.logger.config import log


def format_phone_number(
    phone: str,
    country_code: str = "+81",
    num_format: int = phonenumbers.PhoneNumberFormat.E164,
):
    try:
        try:
            parsed_number = phonenumbers.parse(phone, None)
        except phonenumbers.NumberParseException:
            parsed_number = phonenumbers.parse(f"{country_code}{phone}")

        return phonenumbers.format_number(parsed_number, num_format)
    except phonenumbers.NumberParseException as exc:
        log.error(f"Error formatting phone number '{phone}': {exc}")
        return None


def extract_short_notification_body_to_mobile(html_string):
    if not html_string:
        return html_string
    try:
        first_tag_match = re.search(
            PARAGRAPH_WITH_BREAK_OR_END_REGEX, html_string, re.DOTALL
        )
        if first_tag_match:
            inner_content = re.sub(
                REMOVE_HTML_TAGS_REGEX, "", first_tag_match.group(1)
            ).strip()
            return inner_content
    except Exception as ex:
        log.error(
            f"Process extract short notification body to mobile has an error {str(ex)}"
        )

    return html_string
