from enum import IntEnum, StrEnum


class MailTemplateCategoryEnum(IntEnum):
    """Enum for mail template categories."""

    LOGIN_FOR_DOCTOR = 1
    INIT_PASSWORD_FOR_DOCTOR = 2
    RESET_PASSWORD_FOR_DOCTOR = 3

    RESET_PASSWORD_SUCCESS = 4
    INIT_PASSWORD_SUCCESS = 5

    LOGIN_FOR_SYSTEM_ADMIN = 6


class MessageType(StrEnum):
    """Enum for message types."""

    HTML = "html"
    PLAIN = "plain"


class MultipartSubtypeEnum(StrEnum):
    mixed = "mixed"
    alternative = "alternative"
    related = "related"
