from enum import IntEnum, StrEnum


class S3Folder(StrEnum):
    SUB = "sub"
    MAIN = "main"
    DOCUMENTS = "documents"
    PATIENT_INFO = "patient_info"


class S3RoleEnum(StrEnum):
    ADMIN = "admin"
    DOCTOR = "doctor"
    PATIENT = "patient"


class S3RestoreTier(StrEnum):
    """
    Represents the retrieval tier options when restoring objects from Amazon S3 Glacier storage.

    These tiers determine the speed and cost of retrieving archived data:

    - EXPEDITED: FASTEST retrieval (1-5 minutes), HIGHEST cost
    - STANDARD: MODERATE retrieval speed (3-5 hours), MEDIUM cost
    - BULK: SLOWEST retrieval (5-12 hours), LOWEST cost
    """

    EXPEDITED = "Expedited"
    STANDARD = "Standard"
    BULK = "Bulk"


class S3RestoreStatus(IntEnum):
    RESTORING = 2
    RESTORED = 4
    EXPIRED = 6


class S3StorageClass(StrEnum):
    STANDARD = "STANDARD"
    STANDARD_IA = "STANDARD_IA"
    ONEZONE_IA = "ONEZONE_IA"
    GLACIER = "GLACIER"
    DEEP_ARCHIVE = "DEEP_ARCHIVE"


class S3TypeObject(StrEnum):
    GET_OBJECT = "get_object"
    PUT_OBJECT = "put_object"


class S3PrefixNameEnum(StrEnum):
    INTRAORAL_EXAMINATION = "intraoral_examination"
    DOCUMENT_MANAGEMENT = "document_management"
    MEDICAL_TEMPLATE = "medical_template"
