"""
Enums for documents sent functionality.
"""

from enum import IntEnum


class DocumentReadStatus(IntEnum):
    """Enum for document read status."""

    UNREAD = 2
    READ = 4

    @classmethod
    def get_display_name(cls, status: int) -> str:
        """Get display name for status."""
        status_map = {cls.UNREAD: "Unread", cls.READ: "Read"}
        return status_map.get(status, "Unknown")
