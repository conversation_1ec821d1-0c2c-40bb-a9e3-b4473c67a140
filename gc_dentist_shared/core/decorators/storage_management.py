from functools import wraps
from typing import Awaitable, Callable, Optional

from gc_dentist_shared.core.common.redis import RedisCli
from gc_dentist_shared.core.common.s3_bucket import S3Client
from gc_dentist_shared.core.common.utils import gigabyte_to_bytes
from gc_dentist_shared.core.constants import X_TENANT_UUID, StorageRedis
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.core.messages import CustomMessageCode


# Decorator check limit storage management base on tenant_uuid
def check_tenant_storage_limit(
    configuration, db_fallback_func: Optional[Callable[[str], Awaitable[int]]] = None
):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            request = None
            for arg in args:
                if hasattr(arg, "headers"):
                    request = arg
                    break

            if not request:
                request = kwargs.get("request")

            if not request:
                raise CustomValueError(
                    message=CustomMessageCode.REQUEST_OBJECT_NOT_FOUND.title,
                    message_code=CustomMessageCode.REQUEST_OBJECT_NOT_FOUND.code,
                )

            tenant_uuid = request.headers.get(X_TENANT_UUID)
            if not tenant_uuid:
                raise CustomValueError(
                    message=CustomMessageCode.TENANT_UUID_HEADER_REQUIRED.title,
                    message_code=CustomMessageCode.TENANT_UUID_HEADER_REQUIRED.code,
                )

            try:
                redis_cli = await RedisCli.get_instance(configuration)

                # Get current usage
                prefix_current_usage = (
                    StorageRedis.STORAGE_CURRENT_USAGE.value % tenant_uuid
                )
                current_usage_bytes_from_redis = await redis_cli.get(
                    prefix_current_usage
                )

                if current_usage_bytes_from_redis:
                    current_usage_bytes = int(current_usage_bytes_from_redis)
                else:
                    s3_client = await S3Client.get_instance(configuration)
                    tenant_folder = f"{configuration.S3_FOLDER_NAME}/{tenant_uuid}"
                    current_usage_bytes_from_s3 = await s3_client.get_size_folder(
                        prefix=tenant_folder
                    )
                    await redis_cli.set(
                        prefix_current_usage, current_usage_bytes_from_s3
                    )
                    current_usage_bytes = current_usage_bytes_from_s3

                # Get storage limit with Redis cache and database fallback
                tenant_max_storage_gb = await get_storage_limit_with_fallback(
                    redis_cli, tenant_uuid, db_fallback_func
                )

                tenant_max_storage_bytes = gigabyte_to_bytes(tenant_max_storage_gb)

                if current_usage_bytes >= tenant_max_storage_bytes:
                    raise CustomValueError(
                        message=CustomMessageCode.TENANT_STORAGE_LIMIT_EXCEEDED.title,
                        message_code=CustomMessageCode.TENANT_STORAGE_LIMIT_EXCEEDED.code,
                    )

            except CustomValueError:
                raise
            except Exception as e:
                raise CustomValueError(
                    message=f"{CustomMessageCode.STORAGE_MANAGEMENT_ERROR.title}: {e}",
                    message_code=CustomMessageCode.STORAGE_MANAGEMENT_ERROR.code,
                )

            return await func(*args, **kwargs)

        return wrapper

    return decorator


async def get_storage_limit_with_fallback(
    redis_cli,
    tenant_uuid: str,
    db_fallback_func: Optional[Callable[[str], Awaitable[int]]],
) -> int:
    storage_limit_key = StorageRedis.STORAGE_LIMIT.value % tenant_uuid

    # Step 1: Try to get from Redis cache
    try:
        cached_limit = await redis_cli.get(storage_limit_key)
        if cached_limit:
            log.info(
                f"✅ Storage limit found in Redis cache for tenant {tenant_uuid}: {cached_limit}GB"
            )
            return int(cached_limit)
        else:
            log.warning(f"⚠️ No storage limit cache found for tenant {tenant_uuid}")
    except Exception as e:
        log.error(
            f"❌ Redis error when getting storage limit for tenant {tenant_uuid}: {e}"
        )

    # Step 2: Fallback to database query if provided
    if db_fallback_func:
        try:
            log.info(f"🔄 Falling back to database query for tenant {tenant_uuid}")
            storage_limit_gb = await db_fallback_func(tenant_uuid)

            # Try to cache the result for future use (best effort)
            try:
                set_result = await redis_cli.set(storage_limit_key, storage_limit_gb)
                if set_result:
                    log.info(
                        f"✅ Cached storage limit {storage_limit_gb}GB for tenant {tenant_uuid}"
                    )
                else:
                    log.error(
                        f"❌ Failed to cache storage limit for tenant {tenant_uuid}: Redis SET returned False"
                    )
            except Exception as cache_error:
                log.warning(f"⚠️ Failed to cache storage limit: {cache_error}")

            return storage_limit_gb

        except Exception as db_error:
            log.error(
                f"❌ Database fallback failed for tenant {tenant_uuid}: {db_error}"
            )
            raise CustomValueError(
                message=f"Failed to retrieve storage limit from database: {str(db_error)}",
                message_code=CustomMessageCode.STORAGE_MANAGEMENT_ERROR.code,
            )

    # Step 3: No fallback function provided and Redis failed
    raise CustomValueError(
        message=f"Storage limit not available for tenant {tenant_uuid}. "
        f"Redis cache empty and no database fallback provided.",
        message_code=CustomMessageCode.STORAGE_MANAGEMENT_ERROR.code,
    )
