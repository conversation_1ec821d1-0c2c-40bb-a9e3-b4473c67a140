from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Column, String

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class ClinicInformation(TenantBase, DateTimeMixin):
    __tablename__ = "clinic_informations"

    tenant_uuid = Column(String, primary_key=True)
    clinic_name = Column(String, nullable=False)
    clinic_no = Column(String, nullable=False)
    phone_number = Column(String, nullable=False)
    email = Column(String, nullable=False)
    address_1 = Column(String, nullable=False)
    address_2 = Column(String, nullable=False)
    address_3 = Column(String, nullable=False)
    latitude = Column(String, nullable=True)
    longitude = Column(String, nullable=True)
    logo_url = Column(String, nullable=True)
    opening_hours = Column(JSON, nullable=True)
    is_active = Column(Boolean, nullable=False, default=True)
