from uuid import uuid4

from sqlalchemy import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>umn,
    <PERSON><PERSON><PERSON>,
    Integer,
    String,
    UniqueConstraint,
    text,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func

from gc_dentist_shared.base.datetime_mixin import utc_now
from gc_dentist_shared.base.tenant_declarative_base import TenantBase
from gc_dentist_shared.core.enums.document_sent_enum import DocumentReadStatus


class DocumentSent(TenantBase):
    __tablename__ = "documents_sent"

    id = Column(Integer, primary_key=True, autoincrement=True)

    document_uuid = Column(
        String,
        nullable=False,
        default=lambda: str(uuid4()),
        comment="UUID represents a document.",
    )

    patient_user_id = Column(
        Integer,
        ForeignKey("patient_users.id"),
        nullable=False,
        comment="ID of the patient who received the document",
    )

    document_url = Column(
        String(500), nullable=False, comment="URL where the document can be accessed"
    )

    preview_document_data = Column(
        JSONB,
        nullable=True,
        comment="JSON data containing document preview information (thumbnail, metadata, etc.)",
    )

    read_status = Column(
        Integer,
        nullable=False,
        server_default=text(str(DocumentReadStatus.UNREAD.value)),
        comment="Read status",
    )

    read_at = Column(
        TIMESTAMP(timezone=True),
        nullable=True,
        comment="Timestamp when the document was read by the patient",
    )

    sent_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        default=utc_now,
        server_default=func.now(),
        comment="Timestamp when the document was sent",
    )

    sent_by = Column(
        Integer,
        ForeignKey("doctor_users.id"),
        nullable=False,
        comment="ID of the doctor/staff who sent the document",
    )

    __table_args__ = (
        UniqueConstraint(
            "document_uuid",
            "patient_user_id",
            name="uq_documents_sent_document_patient",
        ),
    )
