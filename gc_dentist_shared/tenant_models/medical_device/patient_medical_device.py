from sqlalchemy import <PERSON>umn, Date, <PERSON><PERSON><PERSON>, Integer, String, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class PatientMedicalDevice(TenantBase, DateTimeMixin):
    """
    Model to store measurement data from medical device.
    """

    __tablename__ = "patient_medical_devices"

    id = Column(Integer, primary_key=True, autoincrement=True)

    external_patient_no = Column(
        String,
        nullable=False,
        comment="Patient number from the external system or device",
    )
    patient_user_id = Column(Integer, ForeignKey("patient_users.id"), nullable=False)
    device_type = Column(
        String(), nullable=False, comment="Enums: MedicalDeviceType, BFA, EMG,...."
    )
    device_data_id = Column(
        String, nullable=False, comment="Unique data identifier from the device"
    )

    examination_date = Column(
        Date, nullable=False, comment="Date of the examination/measurement"
    )
    raw_data = Column(
        JSONB,
        comment="Data medical device",
    )
    image_file_name = Column(String(), comment="File image name")

    __table_args__ = (
        UniqueConstraint(
            "patient_user_id",
            "device_type",
            "device_data_id",
            name="uq_patient_medical_device_per_device",
        ),
    )
