from sqlalchemy import <PERSON>umn, Date, <PERSON><PERSON><PERSON>, Integer, String, UniqueConstraint

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class PatientTreatmentImage(TenantBase, DateTimeMixin):
    """
    Model to store treatment image Patient.
    """

    __tablename__ = "patient_treatment_images"

    id = Column(Integer, primary_key=True, autoincrement=True)

    external_patient_no = Column(
        String,
        nullable=False,
        comment="Patient number from the external system or device",
    )
    patient_user_id = Column(Integer, ForeignKey("patient_users.id"), nullable=False)
    examination_date = Column(
        Date, nullable=False, comment="Date of the examination/measurement"
    )

    image_file_name = Column(String(), nullable=False, comment="File image name")
    image_file_path = Column(
        String(), nullable=False, comment="Path File image original"
    )
    preview_image_file_path = Column(String(), comment="Path File image thumbnail")

    __table_args__ = (
        UniqueConstraint(
            "patient_user_id",
            "image_file_name",
            name="uq_patient_treatment_image_user_id_image_file_name",
        ),
    )
