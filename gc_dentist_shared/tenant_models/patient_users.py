import hashlib
from uuid import uuid4

from sqlalchemy import (
    TIMESTAMP,
    Boolean,
    Column,
    ForeignKey,
    Integer,
    String,
    UniqueConstraint,
)

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class PatientUser(TenantBase, DateTimeMixin):
    __tablename__ = "patient_users"

    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String, nullable=False, unique=True)
    password = Column(String, nullable=True)
    patient_no = Column(String, nullable=False, unique=True)
    is_adult = Column(Boolean, nullable=False, default=False)
    status = Column(
        Boolean, nullable=False, default=True, comment="Status active of patient"
    )
    parent_id = ForeignKey("patient_users.id", nullable=True)
    external_patient_id = Column(String, nullable=True)
    source = Column(String, nullable=True, comment="System name")
    required_change_password = Column(Boolean, nullable=False, default=False)
    last_login = Column(TIMESTAMP(timezone=True), nullable=True)
    uuid = Column(String, default=lambda: str(uuid4()))
    created_by = Column(Integer, nullable=True)
    updated_by = Column(Integer, nullable=True)

    __table_args__ = (
        UniqueConstraint(
            "external_patient_id", "source", name="uq_external_patient_source"
        ),
    )

    def set_password(self, plain_password, tenant_uuid):
        if tenant_uuid is None and plain_password is None:
            raise Exception("tenant_uuid and plain_password cannot be None")

        if plain_password is not None and tenant_uuid is not None:
            self.password = hashlib.sha256(
                (plain_password + tenant_uuid).encode("utf-8")
            ).hexdigest()
        else:
            self.password = None

    def validate_password(self, plain_password, tenant_uuid):
        if plain_password is None or self.password is None or tenant_uuid is None:
            return False
        hashed = hashlib.sha256(
            (plain_password + tenant_uuid).encode("utf-8")
        ).hexdigest()
        return hashed == self.password
