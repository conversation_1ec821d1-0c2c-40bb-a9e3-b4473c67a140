# GC Admin Guide

## Guide Setup
### 1. Install Shared Models
1. go to folder contain file pyproject.toml
2. ```bash
    pip install -e .
    ```

### 2. Install Dependencies
```bash
cd gc-admin-app-service &&
pip install -r requirements.txt
```

### 3. Environment Configuration
1. Create `.env` file in `gc-admin-app-service/app/configuration` directory
2. Add the following environment variables:
```env
ENVIRONMENT='develop'
PROJECT_NAME='GC Admin Service'

POSTGRES_SERVER = 'localhost'
POSTGRES_USER='postgres'
POSTGRES_PASSWORD='postgres' # pragma: allowlist secret
POSTGRES_GLOBAL_DB_NAME='central_local'
POSTGRES_PORT=5432
DB_ECHO=false
DB_INIT=false

READ_ONLY_POSTGRES_SERVER = 'localhost'
READ_ONLY_POSTGRES_USER='postgres'
READ_ONLY_POSTGRES_PASSWORD='postgres' # pragma: allowlist secret
READ_ONLY_POSTGRES_GLOBAL_DB_NAME='central_local'
READ_ONLY_POSTGRES_PORT=5432

# config db name using check migration is correct
POSTGERS_TENANT_TEMPLATE_DB_NAME = "tenant_template"

# Template Database for clinic tenant using build migration files 
DB_NAME_TEMPLATE = 'clinic_template'

COMMUNICATE_SECRET_KEY = 

AES_SECRET_ID_ROTATION=
AWS_SECRET_ROTATION_KEY_MAPPING=
AWS_SECRET_CURRENT_VERSION=
AES_SECRET_KEY_MAPPING=
```

### 4. Migration Databases
```bash
alembic -c alembic_admin.ini upgrade head
```
### 5. New Model or Change Model
1. Model need defined at location gc_dentist_shared/tenant_models

### 6. New Migration
```bash
alembic -c alembic_admin.ini revision --autogenerate -m "description migration here"
```

# GC Dentist Guide

## Guide Setup
### 1. Install Shared Models
1. go to folder contain file pyproject.toml
2. ```bash
    pip install -e .
    ```

### 2. Install Dependencies
```bash
cd gc-admin-app-service &&
pip install -r requirements.txt
```

### 3. Environment Configuration
1. Create `.env` file in `gc-dentist-app-service/app/configuration` directory
2. Add the following environment variables:
```env
ENVIRONMENT='develop'
PROJECT_NAME='GC Dentist Service'

POSTGRES_SERVER = 'localhost'
POSTGRES_USER='postgres'
POSTGRES_PASSWORD='postgres' # pragma: allowlist secret
POSTGRES_PORT=5432
DB_ECHO=false
DB_INIT=false

READ_ONLY_POSTGRES_SERVER = 'localhost'
READ_ONLY_POSTGRES_USER='postgres'
READ_ONLY_POSTGRES_PASSWORD='postgres' # pragma: allowlist secret
READ_ONLY_POSTGRES_PORT=5432

# Center Database Name
POSTGRES_GLOBAL_DB_NAME = 'central_local'
READ_ONLY_POSTGRES_GLOBAL_DB_NAME = 'central_local'
# Template Database for clinic tenant using build migration files 
DB_NAME_TEMPLATE = 'clinic_template'

COMMUNICATE_SECRET_KEY = 

AES_SECRET_ID_ROTATION=
AWS_SECRET_ROTATION_KEY_MAPPING=
AWS_SECRET_CURRENT_VERSION=
AES_SECRET_KEY_MAPPING=
```

### 4. Migration Databases
```bash
cd gc-admin-app-service/app &&
alembic -c alembic_tenant.ini -x dev=true upgrade head
```
### 5. New Model or Change Model
1. Model need defined at location gc_dentist_shared/central_models

### 6. New Migration
```bash
cd gc-admin-app-service/app &&
alembic -c alembic_tenant.ini -x dev=true revision --autogenerate -m "description migration here"
```

# GC OAuth Guide

## Guide Setup
### 1. Install Shared Models
1. go to folder contain file pyproject.toml
2. ```bash
    pip install -e .
    ```

### 2. Install Dependencies
```bash
cd gc-oauth2-service &&
pip install -r requirements.txt
```

### 3. Environment Configuration
1. Create `.env` file in `gc-oauth2-service/app/configuration` directory
2. Add the following environment variables:
```env
ENVIRONMENT='develop'
PROJECT_NAME='GC OAuth2 Service'

POSTGRES_SERVER = 'localhost'
POSTGRES_USER='postgres'
POSTGRES_PASSWORD='postgres' # pragma: allowlist secret
POSTGRES_GLOBAL_DB_NAME='central_local'
POSTGRES_PORT=5432
DB_ECHO=false
DB_INIT=false

READ_ONLY_POSTGRES_SERVER = 'localhost'
READ_ONLY_POSTGRES_USER='postgres'
READ_ONLY_POSTGRES_PASSWORD='postgres' # pragma: allowlist secret
READ_ONLY_POSTGRES_GLOBAL_DB_NAME='central_local'
READ_ONLY_POSTGRES_PORT=5432

COMMUNICATE_SECRET_KEY = 

AES_SECRET_ID_ROTATION=
AWS_SECRET_ROTATION_KEY_MAPPING=
AWS_SECRET_CURRENT_VERSION=
AES_SECRET_KEY_MAPPING=
```

### 4. Generate RSA Keys for JWT Signing
```bash
cd gc-oauth2-service/app/scripts &&
python generate_rsa_keys.py
```
This will generate RSA key pairs in `gc-oauth2-service/app/configuration/.keys/` directory.