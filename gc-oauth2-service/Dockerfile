FROM python:3.12-slim as builder

WORKDIR /build

RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    libffi-dev \
    libssl-dev \
    libpq-dev \
    python-dev-is-python3 \
    git \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*
# Copy gc_dentist_share module 
COPY ./gc_dentist_shared ./gc_dentist_shared
# Pyproject.toml of dentist_shared
COPY ./pyproject.toml .
# Pyproject.toml of oauth-service
RUN mkdir gc_oauth_dependency

COPY ./gc-oauth2-service/pyproject.toml ./gc_oauth_dependency/

RUN pip install --upgrade pip build poetry poetry-plugin-export
#Build file whl for c_dentist_share module 
RUN python -m build
# Export requirements.txt
RUN poetry config virtualenvs.create false &&\
    poetry --directory ./gc_oauth_dependency/ export -f requirements.txt --without-hashes --output /build/requirements.txt
# Install dependency from poetry_export's requirement.txt
RUN pip install --upgrade pip\
    &&pip install --no-cache -r requirements.txt\
    &&pip install /build/dist/*.whl && rm -rf ./dist

###############################
FROM python:3.12-slim as runner

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

WORKDIR /app
# Copy python lib from Builder
COPY --from=builder /usr/local/lib/python3.12 /usr/local/lib/python3.12
COPY --from=builder /usr/local/bin /usr/local/bin

# Copy application files
COPY gc-oauth2-service/app/ .
# Set ENV FLASK
ENV FLASK_APP=main.py
ENV FLASK_RUN_HOST=0.0.0.0
ENV FLASK_RUN_PORT=5000

CMD ["flask", "run"]

