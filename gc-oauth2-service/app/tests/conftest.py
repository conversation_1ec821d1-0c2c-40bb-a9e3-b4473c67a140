import json
import os
import time
import uuid
import warnings
from datetime import datetime
from os.path import dirname, join
from pathlib import Path
from unittest.mock import patch

import psycopg2
import pytest
from alembic import command
from alembic.config import Config as AlembicConfig
from authlib.common.security import generate_token as gen_salt
from dotenv import load_dotenv
from pydantic import PostgresDsn
from sqlalchemy import text
from sqlalchemy.engine import URL, Engine, create_engine
from sqlalchemy.orm import Session, sessionmaker

from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.core.permissions.load_file_yaml import load_tenant_roles_yaml

BASE_DIR = dirname(__file__)
ENV_FILE = os.environ.get("USE_ENV_FILE", ".env")

if ENV_FILE and len(ENV_FILE) > 0:
    load_dotenv(join(BASE_DIR, ENV_FILE), verbose=True, override=True)
else:
    load_dotenv(verbose=True, override=True)

POSTGRES_SERVER = os.getenv("POSTGRES_SERVER_UNIT_TEST")
POSTGRES_PORT = os.getenv("POSTGRES_PORT_UNIT_TEST")
POSTGRES_USER = os.getenv("POSTGRES_USER_UNIT_TEST")
POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD_UNIT_TEST")

DATABASE_NAME = (
    os.getenv("USE_TENANT_DBNAME_UNIT_TEST")
    or f"tenant_db_unittest_{datetime.now().strftime('%Y%m%d%H%M%S')}"
)
CENTRAL_DATABASE_NAME = (
    os.getenv("USE_CENTRAL_DBNAME_UNIT_TEST")
    or f"central_db_unittest_{datetime.now().strftime('%Y%m%d%H%M%S')}"
)

SQLALCHEMY_TENANT_DATABASE_URL = str(
    PostgresDsn.build(
        scheme="postgresql",
        username=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=int(POSTGRES_PORT),
        path=DATABASE_NAME,
    )
)

SQLALCHEMY_CENTRAL_DATABASE_URL = str(
    PostgresDsn.build(
        scheme="postgresql",
        username=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=int(POSTGRES_PORT),
        path=CENTRAL_DATABASE_NAME,
    )
)

warnings.filterwarnings("ignore", category=DeprecationWarning, module="swigvarlink")


@pytest.fixture(scope="session", autouse=True)
def override_tenant_db_url():
    from db.db_connection import TenantDatabase

    TenantDatabase._sync_engines = {}
    TenantDatabase._sync_sessionmakers = {}

    test_url = URL.create(
        drivername="postgresql",
        username=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=int(POSTGRES_PORT),
        database=DATABASE_NAME,
    )

    with patch.object(TenantDatabase, "get_url_db_sync", return_value=test_url):
        yield


@pytest.fixture(scope="session", autouse=True)
def override_central_db_url():
    from db.db_connection import CentralDatabase

    CentralDatabase._engine = None
    CentralDatabase._sessionmaker = None

    test_url_central = URL.create(
        drivername="postgresql",
        username=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=int(POSTGRES_PORT),
        database=CENTRAL_DATABASE_NAME,
    )

    with patch.object(
        CentralDatabase, "get_url_db_central_sync", return_value=test_url_central
    ):
        yield


@pytest.fixture(scope="session")
def create_new_tenant_database():
    if os.getenv("USE_TENANT_DBNAME_UNIT_TEST"):
        log.info(f"🔁 Skipping tenant DB creation. Using existing: {DATABASE_NAME}")
        return

    log.info(f"🛠️ Creating test tenant database: {DATABASE_NAME}")
    conn = psycopg2.connect(
        user=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=POSTGRES_PORT,
    )
    conn.autocommit = True
    cur = conn.cursor()
    cur.execute(f'CREATE DATABASE "{DATABASE_NAME}";')
    cur.close()
    conn.close()
    return DATABASE_NAME


@pytest.fixture(scope="session")
def create_new_central_database():
    if os.getenv("USE_CENTRAL_DBNAME_UNIT_TEST"):
        log.info(
            f"🔁 Skipping central DB creation. Using existing: {CENTRAL_DATABASE_NAME}"
        )
        return

    log.info(f"🛠️ Creating test central database: {CENTRAL_DATABASE_NAME}")
    conn = psycopg2.connect(
        user=POSTGRES_USER,
        password=POSTGRES_PASSWORD,
        host=POSTGRES_SERVER,
        port=POSTGRES_PORT,
    )
    conn.autocommit = True
    cur = conn.cursor()
    cur.execute(f'CREATE DATABASE "{CENTRAL_DATABASE_NAME}";')
    cur.close()
    conn.close()
    return CENTRAL_DATABASE_NAME


@pytest.fixture(scope="session")
def alembic_config_tenant(override_tenant_db_url, create_new_tenant_database):
    base_dir_alembic = (
        Path(__file__).resolve().parent.parent.parent.parent
        / "gc-admin-app-service/app/"
    )
    alembic_tenant_ini_path = str(base_dir_alembic / "alembic_tenant.ini")

    config_tenant = AlembicConfig(alembic_tenant_ini_path)
    config_tenant.set_main_option(
        "script_location", str(base_dir_alembic / "alembic_tenant")
    )
    config_tenant.set_main_option("sqlalchemy.url", SQLALCHEMY_TENANT_DATABASE_URL)

    return config_tenant


@pytest.fixture(scope="session")
def alembic_config_central(override_central_db_url, create_new_central_database):
    base_dir_alembic = (
        Path(__file__).resolve().parent.parent.parent.parent
        / "gc-admin-app-service/app/"
    )
    alembic_central_ini_path = str(base_dir_alembic / "alembic_admin.ini")

    config_cental = AlembicConfig(alembic_central_ini_path)
    config_cental.set_main_option(
        "script_location", str(base_dir_alembic / "alembic_admin")
    )
    config_cental.set_main_option("sqlalchemy.url", SQLALCHEMY_CENTRAL_DATABASE_URL)

    return config_cental


@pytest.fixture(scope="session", autouse=True)
def apply_migrations_tenant(alembic_config_tenant):
    log.info("🛠️ Applying tenant migrations...")
    command.upgrade(alembic_config_tenant, "head")


@pytest.fixture(scope="session", autouse=True)
def apply_migrations_central(alembic_config_central):
    log.info("🛠️ Applying central migrations...")
    command.upgrade(alembic_config_central, "head")


@pytest.fixture(scope="session")
def setup_test_db(apply_migrations_tenant, apply_migrations_central):
    yield


@pytest.fixture(scope="session")
def app():
    from main import create_app

    application = create_app()
    application.config.update({"TESTING": True})
    return application


@pytest.fixture(scope="session")
def client(app):
    return app.test_client()


@pytest.fixture(scope="session")
def sync_tenant_db_session_object() -> Session:
    engine = create_engine(
        SQLALCHEMY_TENANT_DATABASE_URL, pool_pre_ping=True, future=True
    )
    sync_session_maker = sessionmaker(
        autocommit=False, autoflush=False, expire_on_commit=False, bind=engine
    )
    yield sync_session_maker()


@pytest.fixture(scope="session")
def sync_central_db_session_object() -> Session:
    engine = create_engine(
        SQLALCHEMY_CENTRAL_DATABASE_URL, pool_pre_ping=True, future=True
    )
    sync_session_maker = sessionmaker(
        autocommit=False, autoflush=False, expire_on_commit=False, bind=engine
    )
    yield sync_session_maker()


@pytest.fixture(scope="session")
def tenant_uuid(sync_central_db_session_object: Session):
    with sync_central_db_session_object.begin():
        result = sync_central_db_session_object.execute(
            text(
                """
                INSERT INTO tenant_clinics (
                    tenant_uuid, clinic_name, clinic_no, db_name, status
                ) VALUES (
                             :tenant_uuid,
                             :clinic_name,
                             :clinic_no,
                             :db_name,
                             :status
                         )
                    RETURNING tenant_uuid;
                """
            ),
            {
                "tenant_uuid": str(uuid.uuid4()),
                "clinic_name": str(uuid.uuid4()),
                "clinic_no": str(uuid.uuid4()),
                "db_name": DATABASE_NAME,
                "status": 40,
            },
        )
        value = result.scalar_one()
    return str(value)


@pytest.fixture(scope="session")
def clinic_no(sync_central_db_session_object: Session, tenant_uuid):
    with sync_central_db_session_object.begin():
        result = sync_central_db_session_object.execute(
            text(
                """
                SELECT clinic_no FROM tenant_clinics
                WHERE tenant_uuid = :tenant_uuid
            """
            ),
            {"tenant_uuid": tenant_uuid},
        )
        clinic_no = result.scalar_one_or_none()
    return clinic_no


@pytest.fixture(scope="session", autouse=True)
def default_roles(sync_tenant_db_session_object: Session):
    roles_data = load_tenant_roles_yaml()

    with sync_tenant_db_session_object.begin():
        for role in roles_data.get("roles", []):
            sql_query = text(
                """
                INSERT INTO roles (role_key_id, name_json, is_active, is_system, delete_flag)
                VALUES (:role_key_id, :name_json, :is_active, :is_system, :delete_flag)
                    ON CONFLICT (role_key_id) DO NOTHING;
                """
            )

            sync_tenant_db_session_object.execute(
                sql_query,
                {
                    "role_key_id": role.get("role_key_id"),
                    "name_json": json.dumps(role.get("name_json", {})),
                    "is_active": role.get("is_active", True),
                    "is_system": role.get("is_system", False),
                    "delete_flag": role.get("delete_flag", False),
                },
            )


@pytest.fixture(scope="session")
def internal_oauth_client(
    sync_central_db_session_object: Session,
):
    client_name: str = (f"Noda-Client-{(str(uuid.uuid4()))}",)
    client_uri: str = ("https://example.com",)
    grant_types: list = ["refresh_token", "authorization_code", "access_token"]
    redirect_uris: list = (["https://example.com/callback"],)
    response_types: list = (["code"],)
    scope: str = ("profile+mail",)
    token_endpoint_auth_method: str = ("client_secret_post",)

    client_metadata = {
        "client_name": client_name,
        "client_uri": client_uri,
        "grant_types": grant_types,
        "redirect_uris": redirect_uris,
        "response_types": response_types,
        "scope": scope,
        "token_endpoint_auth_method": token_endpoint_auth_method,
    }

    client_secret = "" if token_endpoint_auth_method == "none" else gen_salt(48)
    client_id = gen_salt(24)
    client_id_issued_at = int(time.time())

    insert_sql = text(
        """
        INSERT INTO oauth_clients (
            name, client_id, client_id_issued_at, is_active,
            client_secret, client_metadata, client_secret_expires_at
        ) VALUES (
            :name, :client_id, :client_id_issued_at, :is_active,
            :client_secret, :client_metadata, 0
        )
    """
    )
    with sync_central_db_session_object.begin():
        sync_central_db_session_object.execute(
            insert_sql,
            {
                "name": client_name,
                "client_id": client_id,
                "client_id_issued_at": client_id_issued_at,
                "is_active": True,
                "client_secret": client_secret,
                "client_metadata": json.dumps(client_metadata),
            },
        )
        sync_central_db_session_object.commit()
    return {
        "client_id": client_id,
        "client_secret": client_secret,
        "client_metadata": client_metadata,
    }


@pytest.fixture(scope="session")
def _headers_tenant_uuid(tenant_uuid):
    return {"X-Tenant-UUID": tenant_uuid}


@pytest.fixture(scope="session")
def truncate_table():
    def _truncate(engine: Engine, table_names: list[str]):
        with engine.begin() as conn:
            for table in table_names:
                conn.execute(text(f"TRUNCATE TABLE {table} RESTART IDENTITY CASCADE;"))

    return _truncate
