import random
import string


def unittest_generate_password(
    length=12,
    include_uppercase=True,
    include_lowercase=True,
    include_digits=True,
    include_special=True,
):
    password_chars = []
    char_sets = []
    if include_uppercase:
        char_sets.append(string.ascii_uppercase)
        password_chars.append(random.choice(string.ascii_uppercase))

    if include_lowercase:
        char_sets.append(string.ascii_lowercase)
        password_chars.append(random.choice(string.ascii_lowercase))

    if include_digits:
        char_sets.append(string.digits)
        password_chars.append(random.choice(string.digits))

    if include_special:
        special_chars = '!@#$%^&*(),.?":{}|<>'
        char_sets.append(special_chars)
        password_chars.append(random.choice(special_chars))

    if not char_sets:
        char_sets = [string.ascii_lowercase]
        password_chars = [random.choice(string.ascii_lowercase)]

    all_allowed_chars = "".join(char_sets)

    remaining_length = max(0, length - len(password_chars))
    password_chars.extend(
        random.choice(all_allowed_chars) for _ in range(remaining_length)
    )

    return "".join(password_chars)
