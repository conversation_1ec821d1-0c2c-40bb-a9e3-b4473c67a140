from contextlib import ExitStack
from unittest.mock import patch
from uuid import uuid4

import pytest
from core.messages import CustomMessageCode
from sqlalchemy.exc import DBAPIError
from tests.helpers.insert_data.insert_system_user import (
    unittest_insert_unittest_system_user,
)
from tests.helpers.redis_mock import mock_redis_client


def get_valid_payload(overrides=None):
    payload = {}
    if overrides:
        payload.update(overrides)
    return payload


@pytest.fixture(scope="class")
def setup_data(
    sync_central_db_session_object,
):
    with sync_central_db_session_object as db_session:
        with db_session.begin():
            password = str(uuid4())
            system_user = unittest_insert_unittest_system_user(
                sync_central_db_session_object,
                custom_user_fields={
                    "password": password,
                },
            )

            return {
                "system_user": system_user,
                "system_user_id": system_user.id,
                "username": system_user.username,
                "password": password,
            }


def test_api_doctor_send_otp_success(client, internal_oauth_client, setup_data):
    payload = get_valid_payload(
        {
            "username": setup_data["username"],
            "password": setup_data["password"],
            "client_id": internal_oauth_client["client_id"],
        }
    )

    with (
        patch(
            "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
            return_value=mock_redis_client,
        ),
        patch(
            "services.system_user_auth_service.SystemUserAuthService._send_otp_to_mail",
            side_effect=lambda *args, **kwargs: None,
        ) as mock_send_otp_to_mail,
    ):
        response = client.post(
            "/login/system-user/send-otp",
            json=payload,
        )

    assert response.status_code == 200
    result = response.json
    assert result["data"] == {"user_id": setup_data["system_user_id"]}
    assert result["success"] is True
    assert result["message"] == CustomMessageCode.SEND_MAIL_OTP_SUCCESS.title
    assert result["messageCode"] == CustomMessageCode.SEND_MAIL_OTP_SUCCESS.code
    assert mock_send_otp_to_mail.call_count == 1


@pytest.mark.parametrize(
    "payload_func, status_code, expected_message, expected_code",
    [
        (
            lambda setup_data, client: {
                "username": "invalid_username",
                "password": setup_data["password"],
                "client_id": client["client_id"],
            },
            401,
            CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.title,
            CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.code,
        ),
        (
            lambda setup_data, client: {
                "username": setup_data["username"],
                "password": "invalid_password",  # pragma: allowlist secret
                "client_id": client["client_id"],
            },
            401,
            CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.title,
            CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.code,
        ),
        (
            lambda setup_data, client: {
                "username": setup_data["username"],
                "password": setup_data["password"],
                "client_id": "invalid_client_id",
            },
            401,
            CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title,
            CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.code,
        ),
        (
            lambda setup_data, client: {"invalid_field": "invalid_value"},
            400,
            CustomMessageCode.INVALID_REQUEST_DATA.title,
            CustomMessageCode.INVALID_REQUEST_DATA.code,
        ),
    ],
)
def test_api_system_user_send_otp_fail(
    client,
    internal_oauth_client,
    setup_data,
    payload_func,
    status_code,
    expected_message,
    expected_code,
):
    payload = get_valid_payload(payload_func(setup_data, internal_oauth_client))

    with (
        patch(
            "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
            return_value=mock_redis_client,
        ),
        patch(
            "services.system_user_auth_service.SystemUserAuthService._send_otp_to_mail",
            side_effect=lambda *args, **kwargs: None,
        ),
    ):
        response = client.post(
            "/login/system-user/send-otp",
            json=payload,
        )

    assert response.status_code == status_code
    result = response.json
    assert result["message"] == expected_message
    assert result["messageCode"] == expected_code


@pytest.mark.parametrize(
    "status_code, expected_message, expected_code, patchs_func, call_counts",
    [
        (
            401,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "services.system_user_auth_service.SystemUserAuthService.authenticate_user_with_otp",
                    side_effect=Exception("Unknown error occurred"),
                ),
            ],
            [1],
        ),
        (
            401,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "db.db_connection.CentralDatabase.get_sync_db_session_instance",
                    side_effect=DBAPIError("Database error!", None, None),
                ),
            ],
            [1],
        ),
        (
            401,
            CustomMessageCode.SEND_MAIL_OTP_FAILED.title,
            CustomMessageCode.SEND_MAIL_OTP_FAILED.code,
            [
                patch(
                    "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
                    return_value=mock_redis_client,
                ),
                patch(
                    "services.system_user_auth_service.SystemUserAuthService._get_mail_template",
                    side_effect=Exception("Mail template exception!"),
                ),
            ],
            [1, 1],
        ),
        (
            401,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
                    return_value=mock_redis_client,
                ),
                patch(
                    "services.system_user_auth_service.SystemUserAuthService._send_otp_to_mail",
                    side_effect=Exception("Send mail exception!"),
                ),
            ],
            [1, 1],
        ),
    ],
)
def test_api_system_user_case_exeption(
    client,
    internal_oauth_client,
    setup_data,
    status_code,
    expected_message,
    expected_code,
    patchs_func,
    call_counts,
):
    payload = get_valid_payload(
        {
            "username": setup_data["username"],
            "password": setup_data["password"],
            "client_id": internal_oauth_client["client_id"],
        }
    )

    patch_objs = []
    with ExitStack() as stack:
        for patch_func in patchs_func:
            mock_obj = stack.enter_context(patch_func)
            patch_objs.append(mock_obj)

        response = client.post(
            "/login/system-user/send-otp",
            json=payload,
        )

    assert response.status_code == status_code
    result = response.json
    assert result["success"] is False
    assert result["message"] == expected_message
    assert result["messageCode"] == expected_code
    for idx, patch_obj in enumerate(patch_objs):
        assert patch_obj.call_count == call_counts[idx]
