from contextlib import ExitStack
from http import H<PERSON><PERSON>tatus
from unittest.mock import patch

import pytest
from core.messages import CustomMessageCode
from sqlalchemy.exc import DBAPIError
from tests.helpers.insert_data.insert_doctor import unittest_insert_doctor_response_info
from tests.helpers.redis_mock import mock_redis_client

from gc_dentist_shared.core.constants import X_TENANT_CLINIC_NO


def get_valid_payload(overrides=None):
    payload = {}
    if overrides:
        payload.update(overrides)
    return payload


def get_headers(clinic_no, valid_headers=None):
    headers = {X_TENANT_CLINIC_NO: clinic_no}
    if valid_headers:
        headers.update(valid_headers)
    return headers


@pytest.fixture(scope="class")
def setup_data(
    sync_tenant_db_session_object,
    tenant_uuid,
):
    with sync_tenant_db_session_object.begin():
        doctor = unittest_insert_doctor_response_info(
            sync_tenant_db_session_object,
            tenant_uuid=tenant_uuid,
        )
    sync_tenant_db_session_object.commit()
    return doctor


def test_api_forgot_password_success(
    client,
    setup_data,
    tenant_uuid,
    clinic_no,
    internal_oauth_client,
):
    doctor_user = setup_data["doctor_user"]
    payload = get_valid_payload(
        {
            "username": doctor_user["username"],
            "client_id": internal_oauth_client["client_id"],
        }
    )
    headers = get_headers(clinic_no)

    with (
        patch(
            "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
            return_value=mock_redis_client,
        ),
        patch(
            "services.doctor_auth_service.DoctorAuthService.send_otp_to_mail",
            side_effect=lambda *args, **kwargs: None,
        ) as mock_send_otp_to_mail,
    ):
        response = client.post(
            "/forgot-password/doctor/send-otp",
            json=payload,
            headers=headers,
        )

    assert response.status_code == HTTPStatus.OK.value
    result = response.json
    assert result["success"] is True
    assert result["message"] == CustomMessageCode.FORGOT_PASSWORD_OTP_SENT_SUCCESS.title
    assert (
        result["messageCode"] == CustomMessageCode.FORGOT_PASSWORD_OTP_SENT_SUCCESS.code
    )
    assert mock_send_otp_to_mail.call_count == 1


@pytest.mark.parametrize(
    "payload_func, status_code, result_flag, expected_message, expected_code, headers_func",
    [
        # Invalid payload
        (
            lambda doctor, client: {"invalid_field": "invalid_value"},
            HTTPStatus.BAD_REQUEST.value,
            False,
            CustomMessageCode.INVALID_REQUEST_DATA.title,
            CustomMessageCode.INVALID_REQUEST_DATA.code,
            lambda clinic_no: get_headers(clinic_no),
        ),
        # Invalid username
        (
            lambda doctor, client: {
                "username": "invalid_username",
                "client_id": client["client_id"],
            },
            HTTPStatus.UNAUTHORIZED.value,
            False,
            CustomMessageCode.DOCTOR_USER_NOT_FOUND.title,
            CustomMessageCode.DOCTOR_USER_NOT_FOUND.code,
            lambda clinic_no: get_headers(clinic_no),
        ),
        # Invalid client id
        (
            lambda doctor, client: {
                "username": doctor["username"],
                "client_id": "invalid_client_id",
            },
            HTTPStatus.UNAUTHORIZED.value,
            False,
            CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title,
            CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.code,
            lambda clinic_no: get_headers(clinic_no),
        ),
        # Missing X-Tenant-Clinic-No header
        (
            lambda doctor, client: {
                "username": doctor["username"],
                "client_id": client["client_id"],
            },
            HTTPStatus.UNAUTHORIZED.value,
            False,
            CustomMessageCode.X_TENANT_CLINIC_NO_IS_REQUIRED.title,
            CustomMessageCode.X_TENANT_CLINIC_NO_IS_REQUIRED.code,
            lambda clinic_no: {},  # Empty headers
        ),
        # Invalid X-Tenant-Clinic-No header
        (
            lambda doctor, client: {
                "username": doctor["username"],
                "client_id": client["client_id"],
            },
            HTTPStatus.UNAUTHORIZED.value,
            False,
            CustomMessageCode.X_TENANT_NOT_FOUND.title,
            CustomMessageCode.X_TENANT_NOT_FOUND.code,
            lambda clinic_no: get_headers("invalid-clinic-no"),
        ),
    ],
)
def test_api_forgot_password_case_invalid(
    client,
    clinic_no,
    internal_oauth_client,
    setup_data,
    payload_func,
    status_code,
    result_flag,
    expected_message,
    expected_code,
    headers_func,
):
    doctor_user = setup_data["doctor_user"]
    payload = get_valid_payload(payload_func(doctor_user, internal_oauth_client))
    headers = headers_func(clinic_no)

    with (
        patch(
            "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
            return_value=mock_redis_client,
        ),
        patch(
            "services.doctor_auth_service.DoctorAuthService.send_otp_to_mail",
            side_effect=lambda *args, **kwargs: None,
        ),
    ):
        response = client.post(
            "/forgot-password/doctor/send-otp",
            json=payload,
            headers=headers,
        )

    assert response.status_code == status_code
    result = response.json
    assert result["success"] == result_flag
    assert result["message"] == expected_message
    assert result["messageCode"] == expected_code


@pytest.mark.parametrize(
    "status_code, expected_message, expected_code, patch_funcs, call_counts",
    [
        (
            HTTPStatus.BAD_REQUEST.value,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "services.doctor_auth_service.DoctorAuthService.forgot_password_send_otp",
                    side_effect=Exception("Unknown error occurred"),
                ),
            ],
            [1],
        ),
        (
            HTTPStatus.BAD_REQUEST.value,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "db.db_connection.CentralDatabase.get_sync_db_session_instance",
                    side_effect=DBAPIError("Database error!", None, None),
                ),
            ],
            [1],
        ),
        (
            HTTPStatus.BAD_REQUEST.value,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
                    return_value=mock_redis_client,
                ),
                patch(
                    "services.doctor_auth_service.DoctorAuthService.send_otp_to_mail",
                    side_effect=Exception("Send mail exception!"),
                ),
            ],
            [1, 1],
        ),
    ],
)
def test_api_forgot_password_case_exception(
    client,
    clinic_no,
    internal_oauth_client,
    setup_data,
    status_code,
    expected_message,
    expected_code,
    patch_funcs,
    call_counts,
):
    doctor_user = setup_data["doctor_user"]
    payload = get_valid_payload(
        {
            "username": doctor_user["username"],
            "client_id": internal_oauth_client["client_id"],
        }
    )
    headers = get_headers(clinic_no)

    patch_objs = []
    with ExitStack() as stack:
        for patch_func in patch_funcs:
            mock_obj = stack.enter_context(patch_func)
            patch_objs.append(mock_obj)

        response = client.post(
            "/forgot-password/doctor/send-otp",
            json=payload,
            headers=headers,
        )

    assert response.status_code == status_code
    result = response.json
    assert result["success"] is False
    assert result["message"] == expected_message
    assert result["messageCode"] == expected_code
    for idx, patch_obj in enumerate(patch_objs):
        assert patch_obj.call_count == call_counts[idx]
