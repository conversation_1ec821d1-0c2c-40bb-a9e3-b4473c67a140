import json
import random
import string
from contextlib import ExitStack
from http import HTTPStatus
from unittest.mock import patch

import pytest
from configuration.settings import configuration
from core.constants import DOCTOR_FORGOT_PASSWORD_REDIS_PREFIX, OTP_LENGTH
from core.messages import CustomMessageCode
from sqlalchemy.exc import DBAP<PERSON>rror
from tests.helpers.insert_data.create_oauth_token import unittest_query_oauth_tokens
from tests.helpers.insert_data.create_password import unittest_generate_password
from tests.helpers.insert_data.insert_doctor import unittest_insert_doctor_response_info
from tests.helpers.redis_mock import mock_redis_client

from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation


def get_valid_payload(
    internal_oauth_client,
    setup_data,
    overrides=None,
):
    valid_password = unittest_generate_password()
    payload = {
        "client_id": internal_oauth_client["client_id"],
        "otp": setup_data["otp_hash"],
        "username": setup_data["username_encrypt"],
        "clinic_no": setup_data["clinic_no_encrypt"],
        "new_password": valid_password,
        "confirm_password": valid_password,
    }

    if overrides:
        payload.update(overrides)
    return payload


def set_otp_redis_mock(tenant_uuid, username_hash, otp_hash):
    otp_key = f"{DOCTOR_FORGOT_PASSWORD_REDIS_PREFIX}_{tenant_uuid}_{username_hash}"
    mock_redis_client.setex(
        name=otp_key,
        time=configuration.OTP_SEND_FORGOT_PASSWORD_MINUTES * 60,
        value=json.dumps(
            {
                "otp_hash": otp_hash,
            }
        ),
    )


@pytest.fixture(scope="class")
def setup_data(sync_tenant_db_session_object, tenant_uuid: str, clinic_no: str):
    with sync_tenant_db_session_object.begin():
        data = unittest_insert_doctor_response_info(
            sync_tenant_db_session_object,
            tenant_uuid=tenant_uuid,
        )
        doctor = data["doctor_user"]

    otp = "".join(random.choices(string.digits, k=OTP_LENGTH))
    aes_gcm = AesGCMRotation(configuration)

    return {
        "doctor_id": data["doctor_id"],
        "username_hash": aes_gcm.sha256_hash(doctor["username"]),
        "username_encrypt": aes_gcm.encrypt_data(doctor["username"]),
        "clinic_no_encrypt": aes_gcm.encrypt_data(clinic_no),
        "otp_hash": aes_gcm.sha256_hash(otp),
    }


def test_forgot_password_reset_success(
    client,
    setup_data,
    tenant_uuid,
    clinic_no,
    internal_oauth_client,
    sync_central_db_session_object,
):
    payload = get_valid_payload(internal_oauth_client, setup_data)
    set_otp_redis_mock(tenant_uuid, setup_data["username_hash"], setup_data["otp_hash"])

    with patch(
        "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
        return_value=mock_redis_client,
    ):
        response = client.post(
            "/forgot-password/doctor/reset",
            json=payload,
        )
        assert response.status_code == HTTPStatus.OK.value
        assert response.json["success"] is True
        assert response.json["data"]["clinic_no"] == clinic_no
        with sync_central_db_session_object.begin():
            oauth_tokens = unittest_query_oauth_tokens(
                sync_central_db_session_object,
                internal_oauth_client["client_id"],
                setup_data["doctor_id"],
                tenant_uuid,
            )
            assert len(oauth_tokens) == 0


@pytest.mark.parametrize(
    "new_password, confirm_password",
    [
        # new_password != confirm_password
        (
            unittest_generate_password(),
            unittest_generate_password(),
        ),
        # new_password: too short
        (
            unittest_generate_password(length=7),
            unittest_generate_password(),
        ),
        # new_password: missing uppercase letter
        (
            unittest_generate_password(include_uppercase=False),
            unittest_generate_password(),
        ),
        # new_password: missing lowercase letter
        (
            unittest_generate_password(include_lowercase=False),
            unittest_generate_password(),
        ),
        # new_password: missing digit
        (
            unittest_generate_password(include_digits=False),
            unittest_generate_password(),
        ),
        # new_password: missing special character
        (
            unittest_generate_password(include_special=False),
            unittest_generate_password(),
        ),
        # confirm_password: too short
        (
            unittest_generate_password(),
            unittest_generate_password(length=7),
        ),
        # confirm_password: missing uppercase letter
        (
            unittest_generate_password(),
            unittest_generate_password(include_uppercase=False),
        ),
        # confirm_password: missing lowercase letter
        (
            unittest_generate_password(),
            unittest_generate_password(include_lowercase=False),
        ),
        # confirm_password: missing digit
        (
            unittest_generate_password(),
            unittest_generate_password(include_digits=False),
        ),
        # confirm_password: missing special character
        (
            unittest_generate_password(),
            unittest_generate_password(include_special=False),
        ),
    ],
)
def test_forgot_password_reset_invalid_password(
    client,
    setup_data,
    tenant_uuid,
    clinic_no,
    internal_oauth_client,
    new_password,
    confirm_password,
):
    payload = get_valid_payload(
        internal_oauth_client,
        setup_data,
        overrides={
            "new_password": new_password,
            "confirm_password": confirm_password,
        },
    )
    set_otp_redis_mock(tenant_uuid, setup_data["username_hash"], setup_data["otp_hash"])

    with patch(
        "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
        return_value=mock_redis_client,
    ):
        response = client.post(
            "/forgot-password/doctor/reset",
            json=payload,
        )

    assert response.status_code == HTTPStatus.BAD_REQUEST.value
    result = response.json
    assert result["success"] is False


@pytest.mark.parametrize(
    "payload_func, status_code, expected_message, expected_code",
    [
        # Invalid Payload
        (
            lambda client, otp_hash, username_encrypt, clinic_no_encrypt: {
                "invalid_field": "invalid_value"
            },
            HTTPStatus.BAD_REQUEST.value,
            CustomMessageCode.INVALID_REQUEST_DATA.title,
            CustomMessageCode.INVALID_REQUEST_DATA.code,
        ),
        # Invalid Clinic No Encrypted
        (
            lambda client, otp_hash, username_encrypt, clinic_no_encrypt: {
                "client_id": client["client_id"],
                "otp": otp_hash,
                "username": username_encrypt,
                "clinic_no": "invalid_clinic_no_encrypt",
            },
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.X_TENANT_NOT_FOUND.title,
            CustomMessageCode.X_TENANT_NOT_FOUND.code,
        ),
        # Invalid Client Id
        (
            lambda client, otp_hash, username_encrypt, clinic_no_encrypt: {
                "client_id": "invalid_client_id",
                "otp": otp_hash,
                "username": username_encrypt,
                "clinic_no": clinic_no_encrypt,
            },
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title,
            CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.code,
        ),
        # Invalid Username Encrypt
        (
            lambda client, otp_hash, username_encrypt, clinic_no_encrypt: {
                "client_id": client["client_id"],
                "otp": otp_hash,
                "username": "invalid_username_encrypt",
                "clinic_no": clinic_no_encrypt,
            },
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.DOCTOR_USER_NOT_FOUND.title,
            CustomMessageCode.DOCTOR_USER_NOT_FOUND.code,
        ),
        # Wrong OTP Hash
        (
            lambda client, otp_hash, username_encrypt, clinic_no_encrypt: {
                "client_id": client["client_id"],
                "otp": "invalid_otp_hash",
                "username": username_encrypt,
                "clinic_no": clinic_no_encrypt,
            },
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.OTP_INCORRECT.title,
            CustomMessageCode.OTP_INCORRECT.code,
        ),
    ],
)
def test_forgot_password_reset_case_invalid(
    client,
    tenant_uuid,
    clinic_no,
    internal_oauth_client,
    setup_data,
    payload_func,
    status_code,
    expected_message,
    expected_code,
):
    valid_password = unittest_generate_password()
    payload = {
        "new_password": valid_password,
        "confirm_password": valid_password,
        **payload_func(
            internal_oauth_client,
            setup_data["otp_hash"],
            setup_data["username_encrypt"],
            setup_data["clinic_no_encrypt"],
        ),
    }
    set_otp_redis_mock(tenant_uuid, setup_data["username_hash"], setup_data["otp_hash"])

    with (
        patch(
            "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
            return_value=mock_redis_client,
        ),
    ):
        response = client.post(
            "/forgot-password/doctor/reset",
            json=payload,
        )

    assert response.status_code == status_code
    result = response.json
    assert result["success"] is False
    assert result["message"] == expected_message
    assert result["messageCode"] == expected_code


def test_verify_otp_forgot_password_case_otp_expired(
    client,
    tenant_uuid,
    clinic_no,
    internal_oauth_client,
    setup_data,
):
    # Simulates the OTP not found/expired scenario (don't set the OTP in Redis)
    payload = get_valid_payload(
        internal_oauth_client,
        setup_data,
    )

    with patch(
        "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
        return_value=mock_redis_client,
    ):
        response = client.post(
            "/forgot-password/doctor/reset",
            json=payload,
        )

    assert response.status_code == HTTPStatus.UNAUTHORIZED.value
    result = response.json
    assert result["success"] is False
    assert result["message"] == CustomMessageCode.OTP_NOT_FOUND_OR_EXPIRED.title
    assert result["messageCode"] == CustomMessageCode.OTP_NOT_FOUND_OR_EXPIRED.code
    assert result["data"]["clinic_no"] == clinic_no


@pytest.mark.parametrize(
    "status_code, expected_message, expected_code, patch_funcs, call_counts",
    [
        (
            HTTPStatus.BAD_REQUEST.value,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "db.db_connection.TenantDatabase.get_sync_db_session_instance",
                    side_effect=DBAPIError("Database error!", None, None),
                ),
            ],
            [1],
        ),
        (
            HTTPStatus.BAD_REQUEST.value,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "db.db_connection.CentralDatabase.get_sync_db_session_instance",
                    side_effect=DBAPIError("Database error!", None, None),
                ),
            ],
            [1],
        ),
    ],
)
def test_api_doctor_login_case_exception(
    client,
    tenant_uuid,
    clinic_no,
    internal_oauth_client,
    setup_data,
    status_code,
    expected_message,
    expected_code,
    patch_funcs,
    call_counts,
):
    payload = get_valid_payload(
        internal_oauth_client,
        setup_data,
    )
    set_otp_redis_mock(tenant_uuid, setup_data["username_hash"], setup_data["otp_hash"])

    patch_objs = []
    with ExitStack() as stack:
        for patch_func in patch_funcs:
            mock_obj = stack.enter_context(patch_func)
            patch_objs.append(mock_obj)

        response = client.post(
            "/forgot-password/doctor/reset",
            json=payload,
        )

    assert response.status_code == status_code
    result = response.json
    assert result["success"] is False
    assert result["message"] == expected_message
    assert result["messageCode"] == expected_code
    for idx, patch_obj in enumerate(patch_objs):
        assert patch_obj.call_count == call_counts[idx]
