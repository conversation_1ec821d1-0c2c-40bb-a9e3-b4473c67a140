from contextlib import ExitStack
from unittest.mock import patch

import pytest
from core.messages import CustomMessageCode
from sqlalchemy.exc import DB<PERSON><PERSON>rror
from tests.helpers.insert_data.insert_doctor import unittest_insert_doctor_response_info
from tests.helpers.redis_mock import mock_redis_client

from gc_dentist_shared.core.constants import X_TENANT_CLINIC_NO


def get_valid_payload(overrides=None):
    payload = {}
    if overrides:
        payload.update(overrides)
    return payload


def get_headers(clinic_no, valid_headers=None):
    headers = {X_TENANT_CLINIC_NO: clinic_no}
    if valid_headers:
        headers.update(valid_headers)
    return headers


@pytest.fixture(scope="class")
def setup_data(
    sync_tenant_db_session_object,
    tenant_uuid,
):
    with sync_tenant_db_session_object.begin():
        doctor = unittest_insert_doctor_response_info(
            sync_tenant_db_session_object,
            tenant_uuid=tenant_uuid,
        )
    sync_tenant_db_session_object.commit()
    return doctor


def test_api_doctor_login_pass(client, clinic_no, internal_oauth_client, setup_data):
    doctor_user = setup_data["doctor_user"]
    payload = get_valid_payload(
        {
            "username": doctor_user["username"],
            "password": doctor_user["password"],
            "client_id": internal_oauth_client["client_id"],
        }
    )
    headers = get_headers(clinic_no)

    with (
        patch(
            "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
            return_value=mock_redis_client,
        ),
        patch(
            "services.doctor_auth_service.DoctorAuthService.send_otp_to_mail",
            side_effect=lambda *args, **kwargs: None,
        ) as mock_send_otp_to_mail,
    ):
        response = client.post(
            "/login/doctor/send-otp",
            json=payload,
            headers=headers,
        )

    assert response.status_code == 200
    result = response.json
    assert result["data"] == {"user_id": setup_data["doctor_id"]}
    assert result["success"] is True
    assert result["message"] == CustomMessageCode.SEND_MAIL_OTP_SUCCESS.title
    assert result["messageCode"] == CustomMessageCode.SEND_MAIL_OTP_SUCCESS.code
    assert mock_send_otp_to_mail.call_count == 1


@pytest.mark.parametrize(
    "payload_func, status_code, result_flag, expected_message, expected_code",
    [
        (
            lambda doctor, client: {
                "username": doctor["username"],
                "password": doctor["password"],
                "client_id": client["client_id"],
            },
            200,
            True,
            CustomMessageCode.SEND_MAIL_OTP_SUCCESS.title,
            CustomMessageCode.SEND_MAIL_OTP_SUCCESS.code,
        ),
        (
            lambda doctor, client: {
                "username": "invalid_username",
                "password": doctor["password"],
                "client_id": client["client_id"],
            },
            401,
            False,
            CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.title,
            CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.code,
        ),
        (
            lambda doctor, client: {
                "username": doctor["username"],
                "password": "invalid_password",  # pragma: allowlist secret
                "client_id": client["client_id"],
            },
            401,
            False,
            CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.title,
            CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.code,
        ),
        (
            lambda doctor, client: {
                "username": doctor["username"],
                "password": doctor["password"],
                "client_id": "invalid_client_id",
            },
            401,
            False,
            CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title,
            CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.code,
        ),
        (
            lambda doctor, client: {"invalid_field": "invalid_value"},
            400,
            False,
            CustomMessageCode.INVALID_REQUEST_DATA.title,
            CustomMessageCode.INVALID_REQUEST_DATA.code,
        ),
    ],
)
def test_api_doctor_login_case_fail(
    client,
    clinic_no,
    internal_oauth_client,
    setup_data,
    payload_func,
    status_code,
    result_flag,
    expected_message,
    expected_code,
):
    doctor_user = setup_data["doctor_user"]
    payload = get_valid_payload(payload_func(doctor_user, internal_oauth_client))
    headers = get_headers(clinic_no)

    with (
        patch(
            "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
            return_value=mock_redis_client,
        ),
        patch(
            "services.doctor_auth_service.DoctorAuthService.send_otp_to_mail",
            side_effect=lambda *args, **kwargs: None,
        ),
    ):
        response = client.post(
            "/login/doctor/send-otp",
            json=payload,
            headers=headers,
        )

    assert response.status_code == status_code
    result = response.json
    assert result["success"] == result_flag
    assert result["message"] == expected_message
    assert result["messageCode"] == expected_code


@pytest.mark.parametrize(
    "status_code, expected_message, expected_code, patchs_func, call_counts",
    [
        (
            401,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "services.doctor_auth_service.DoctorAuthService.authenticate_user",
                    side_effect=Exception("Unknown error occurred"),
                ),
            ],
            [1],
        ),
        (
            401,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "db.db_connection.CentralDatabase.get_sync_db_session_instance",
                    side_effect=DBAPIError("Database error!", None, None),
                ),
            ],
            [1],
        ),
        (
            401,
            CustomMessageCode.SEND_MAIL_OTP_FAILED.title,
            CustomMessageCode.SEND_MAIL_OTP_FAILED.code,
            [
                patch(
                    "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
                    return_value=mock_redis_client,
                ),
                patch(
                    "services.doctor_auth_service.DoctorAuthService.get_mail_template",
                    side_effect=Exception("Mail template exception!"),
                ),
            ],
            [1, 1],
        ),
        (
            401,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "gc_dentist_shared.core.common.synchronous_redis.SyncRedisCli.get_instance",
                    return_value=mock_redis_client,
                ),
                patch(
                    "services.doctor_auth_service.DoctorAuthService.send_otp_to_mail",
                    side_effect=Exception("Send mail exception!"),
                ),
            ],
            [1, 1],
        ),
    ],
)
def test_api_doctor_login_case_exeption(
    client,
    clinic_no,
    internal_oauth_client,
    setup_data,
    status_code,
    expected_message,
    expected_code,
    patchs_func,
    call_counts,
):
    doctor_user = setup_data["doctor_user"]
    payload = get_valid_payload(
        {
            "username": doctor_user["username"],
            "password": doctor_user["password"],
            "client_id": internal_oauth_client["client_id"],
        }
    )
    headers = get_headers(clinic_no)

    patch_objs = []
    with ExitStack() as stack:
        for patch_func in patchs_func:
            mock_obj = stack.enter_context(patch_func)
            patch_objs.append(mock_obj)

        response = client.post(
            "/login/doctor/send-otp",
            json=payload,
            headers=headers,
        )

    assert response.status_code == status_code
    result = response.json
    assert result["success"] is False
    assert result["message"] == expected_message
    assert result["messageCode"] == expected_code
    for idx, patch_obj in enumerate(patch_objs):
        assert patch_obj.call_count == call_counts[idx]
