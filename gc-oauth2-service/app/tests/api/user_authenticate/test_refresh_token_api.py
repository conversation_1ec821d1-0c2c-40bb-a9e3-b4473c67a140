from http import HTTPStatus

import pytest
from core.messages import CustomMessageCode
from sqlalchemy import text, update
from tests.helpers.enums.role_enum import UnittestRoleKeyEnum
from tests.helpers.insert_data.create_oauth_token import (
    unittest_dynamic_create_oauth_token,
)
from tests.helpers.insert_data.insert_doctor import unittest_insert_doctor
from tests.helpers.insert_data.insert_doctor_role import unittest_insert_doctor_role
from tests.helpers.insert_data.insert_global_user import (
    unittest_create_global_user,
    unittest_create_tenant_patient_user_mapping,
)
from tests.helpers.insert_data.insert_patient_user import unittest_create_patient_user
from tests.helpers.insert_data.insert_system_user import (
    unittest_insert_unittest_system_user,
)
from werkzeug.security import gen_salt

from gc_dentist_shared.central_models import SystemUser


def get_payload(overrides=None):
    payload = {
        "client_id": gen_salt(24),
        "refresh_token": gen_salt(48),
        "grant_type": "refresh_token",
    }
    if overrides:
        payload.update(overrides)

    return payload


@pytest.fixture(scope="class")
def setup_global_user_flow_data(
    sync_central_db_session_object,
    internal_oauth_client,
    tenant_uuid,
):
    client_id = internal_oauth_client["client_id"]
    tenant_uuid = str(tenant_uuid)
    with sync_central_db_session_object as db_session:
        with db_session.begin():
            global_user = unittest_create_global_user(sync_central_db_session_object)
            tenant_patient_user_mapping = unittest_create_tenant_patient_user_mapping(
                sync_central_db_session_object,
                global_user_id=global_user.id,
                tenant_uuid=tenant_uuid,
            )
            oauth_token = unittest_dynamic_create_oauth_token(
                sync_central_db_session_object,
                user=global_user,
                client_id=client_id,
                global_user_id=global_user.id,
            )

    return {
        "global_user_id": global_user.id,
        "tenant_patient_user_mapping_id": tenant_patient_user_mapping.id,
        "refresh_token": oauth_token.refresh_token,
    }


@pytest.fixture(scope="class")
def setup_clinic_patient_data(
    sync_central_db_session_object,
    sync_tenant_db_session_object,
    internal_oauth_client,
    tenant_uuid,
):
    client_id = internal_oauth_client["client_id"]
    tenant_uuid = str(tenant_uuid)

    with sync_tenant_db_session_object as db_session:
        with db_session.begin():
            patient_user = unittest_create_patient_user(
                sync_tenant_db_session_object,
                tenant_uuid=tenant_uuid,
                username=gen_salt(24),
            )

    with sync_central_db_session_object as db_session:
        with db_session.begin():
            oauth_token = unittest_dynamic_create_oauth_token(
                sync_central_db_session_object,
                user=patient_user,
                client_id=client_id,
                clinic_patient_id=patient_user.id,
                tenant_uuid=tenant_uuid,
            )
            refresh_token = oauth_token.refresh_token

    return {"refresh_token": refresh_token}


@pytest.fixture(scope="class")
def setup_clinic_doctor_data(
    sync_central_db_session_object,
    sync_tenant_db_session_object,
    internal_oauth_client,
    tenant_uuid,
):
    client_id = internal_oauth_client["client_id"]
    tenant_uuid = str(tenant_uuid)
    with sync_tenant_db_session_object as db_session:
        with db_session.begin():
            doctor_user = unittest_insert_doctor(
                sync_tenant_db_session_object, tenant_uuid
            )
            unittest_insert_doctor_role(
                sync_tenant_db_session_object,
                doctor_user.id,
                UnittestRoleKeyEnum.CLINIC_ADMIN.value,
            )

    with sync_central_db_session_object as db_session:
        with db_session.begin():
            oauth_token = unittest_dynamic_create_oauth_token(
                sync_central_db_session_object,
                user=doctor_user,
                client_id=client_id,
                clinic_doctor_id=doctor_user.id,
                tenant_uuid=tenant_uuid,
            )

        refresh_token = oauth_token.refresh_token

    return {"refresh_token": refresh_token}


@pytest.fixture(scope="class")
def setup_system_user_data(
    sync_central_db_session_object,
    internal_oauth_client,
):
    client_id = internal_oauth_client["client_id"]

    with sync_central_db_session_object as db_session:
        with db_session.begin():
            system_user = unittest_insert_unittest_system_user(
                sync_central_db_session_object
            )
            system_user_id = system_user.id
            oauth_token = unittest_dynamic_create_oauth_token(
                sync_central_db_session_object,
                user=system_user,
                client_id=client_id,
                system_user_id=system_user_id,
            )

            refresh_token = oauth_token.refresh_token

    return {"refresh_token": refresh_token, "system_user_id": system_user_id}


def test_refresh_token_global_user_success(
    client,
    sync_central_db_session_object,
    setup_global_user_flow_data,
    tenant_uuid,
    internal_oauth_client,
):
    payload = get_payload(
        {
            "client_id": internal_oauth_client["client_id"],
            **setup_global_user_flow_data,
        }
    )
    response = client.post("/refresh-token", json=payload)

    assert response.status_code == HTTPStatus.OK
    body = response.get_json()
    assert body["success"] is True
    assert isinstance(body, dict)
    assert "data" in body
    assert isinstance(body["data"], dict)
    # Ensure tokens are present in data
    assert "access_token" in body["data"]
    assert "refresh_token" in body["data"]
    assert "token_type" in body["data"]
    assert "expires_in" in body["data"]
    assert body["data"]["tenant_uuids"] == [str(tenant_uuid)]


def test_refresh_token_clinic_patient_success(
    client,
    sync_central_db_session_object,
    sync_tenant_db_session_object,
    setup_clinic_patient_data,
    tenant_uuid,
    internal_oauth_client,
):
    payload = get_payload(
        {
            "client_id": internal_oauth_client["client_id"],
            **setup_clinic_patient_data,
        }
    )
    response = client.post("/refresh-token", json=payload)

    assert response.status_code == HTTPStatus.OK
    body = response.get_json()
    assert body["success"] is True
    assert isinstance(body, dict)
    assert "data" in body
    assert isinstance(body["data"], dict)
    # Ensure tokens are present in data
    assert "access_token" in body["data"]
    assert "refresh_token" in body["data"]
    assert "token_type" in body["data"]
    assert "expires_in" in body["data"]
    assert body["data"]["tenant_uuids"] == [str(tenant_uuid)]


def test_refresh_token_clinic_doctor_success(
    client,
    sync_central_db_session_object,
    sync_tenant_db_session_object,
    setup_clinic_doctor_data,
    tenant_uuid,
    internal_oauth_client,
):
    payload = get_payload(
        {
            "client_id": internal_oauth_client["client_id"],
            **setup_clinic_doctor_data,
        }
    )
    response = client.post("/refresh-token", json=payload)

    assert response.status_code == HTTPStatus.OK
    body = response.get_json()
    assert body["success"] is True
    assert isinstance(body, dict)
    assert "data" in body
    assert isinstance(body["data"], dict)
    # Ensure tokens are present in data
    assert "access_token" in body["data"]
    assert "refresh_token" in body["data"]
    assert "token_type" in body["data"]
    assert "expires_in" in body["data"]
    assert "tenant_uuid" in body["data"]
    assert body["data"]["tenant_uuid"] == str(tenant_uuid)


def test_refresh_token_system_user_success(
    client,
    sync_central_db_session_object,
    setup_system_user_data,
    tenant_uuid,
    internal_oauth_client,
):
    payload = get_payload(
        {
            "client_id": internal_oauth_client["client_id"],
            "refresh_token": setup_system_user_data["refresh_token"],
        }
    )
    response = client.post("/refresh-token", json=payload)

    assert response.status_code == HTTPStatus.OK
    body = response.get_json()
    assert body["success"] is True
    assert isinstance(body, dict)
    assert "data" in body
    assert isinstance(body["data"], dict)
    # Ensure tokens are present in data
    assert "access_token" in body["data"]
    assert "refresh_token" in body["data"]
    assert "token_type" in body["data"]
    assert "expires_in" in body["data"]


def test_refresh_token_system_user_not_activate(
    client,
    sync_central_db_session_object,
    setup_system_user_data,
    tenant_uuid,
    internal_oauth_client,
):
    with sync_central_db_session_object as db_session:
        with db_session.begin():
            sync_central_db_session_object.execute(
                update(SystemUser)
                .where(SystemUser.id == setup_system_user_data["system_user_id"])
                .values(is_active=False)
            )

    payload = get_payload(
        {
            "client_id": internal_oauth_client["client_id"],
            "refresh_token": setup_system_user_data["refresh_token"],
        }
    )
    response = client.post("/refresh-token", json=payload)

    assert response.status_code == HTTPStatus.UNAUTHORIZED
    body = response.get_json()
    assert body["message"] == CustomMessageCode.USER_NOT_FOUND.title


@pytest.mark.parametrize(
    "data_idx, invalid_payload, expected_status_code, expected_message",
    [
        (
            0,
            # Invalid payload, missing request body
            {},
            HTTPStatus.BAD_REQUEST.value,
            CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
        ),
        (
            1,
            # Invalid payload, missing request bodys
            None,
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.UNKNOWN_ERROR.title,
        ),
        (
            2,
            # Invalid grant type
            get_payload({"grant_type": "invalid_grand_type"}),
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.INVALID_GRANT_TYPE.title,
        ),
        (
            3,
            # Invalid client_id
            get_payload({"client_id": gen_salt(24)}),
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title,
        ),
    ],
)
def test_refresh_token_invalid_request(
    client,
    data_idx,
    invalid_payload,
    expected_status_code,
    expected_message,
    tenant_uuid,
    internal_oauth_client,
):
    response = client.post("/refresh-token", json=invalid_payload)

    assert response.status_code == expected_status_code
    body = response.get_json()
    assert body["success"] is False
    assert body["message"] == expected_message


def test_refresh_token_invalid_refresh_token(
    client,
    setup_global_user_flow_data,
    tenant_uuid,
    internal_oauth_client,
):
    data = setup_global_user_flow_data
    payload = get_payload({"client_id": internal_oauth_client["client_id"], **data})

    # Invalid refresh token
    invalid_payload = {**payload, "refresh_token": gen_salt(48)}
    response = client.post("/refresh-token", json=invalid_payload)

    assert response.status_code == HTTPStatus.UNAUTHORIZED.value
    body = response.get_json()
    assert body["success"] is False
    assert body["message"] == CustomMessageCode.INVALID_REFRRESH_TOKEN.title


def test_refresh_token_inactive_oauth_client(
    client,
    sync_central_db_session_object,
    setup_global_user_flow_data,
    internal_oauth_client,
):
    client_id = internal_oauth_client["client_id"]
    refresh_token = setup_global_user_flow_data["refresh_token"]

    # Deactivate client
    try:
        with sync_central_db_session_object as db_session:
            with db_session.begin():
                sync_central_db_session_object.execute(
                    text(
                        "UPDATE oauth_clients SET is_active = false WHERE client_id = :cid"
                    ),
                    {"cid": client_id},
                )

        payload = get_payload({"client_id": client_id, "refresh_token": refresh_token})
        response = client.post("/refresh-token", json=payload)

        assert response.status_code == HTTPStatus.UNAUTHORIZED.value
        body = response.get_json()
        assert body["success"] is False
        assert body["message"] == CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title
    finally:
        # Reactivate client to avoid side effects on other tests
        with sync_central_db_session_object as db_session:
            with db_session.begin():
                sync_central_db_session_object.execute(
                    text(
                        "UPDATE oauth_clients SET is_active = true WHERE client_id = :cid"
                    ),
                    {"cid": client_id},
                )
