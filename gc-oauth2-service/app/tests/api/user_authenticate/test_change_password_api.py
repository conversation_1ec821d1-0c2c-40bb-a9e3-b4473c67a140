from contextlib import ExitStack
from http import HTTPStatus
from unittest.mock import patch

import pytest
from core.messages import CustomMessageCode
from sqlalchemy.exc import DBAPIError
from tests.helpers.insert_data.create_oauth_token import (
    unittest_dynamic_create_oauth_token,
    unittest_query_oauth_tokens,
)
from tests.helpers.insert_data.create_password import unittest_generate_password
from tests.helpers.insert_data.insert_doctor import unittest_insert_doctor

from gc_dentist_shared.core.constants import AUTHORIZATION_HEADER, X_TENANT_UUID


def get_valid_payload(
    internal_oauth_client,
    setup_data,
    overrides=None,
):
    valid_password = unittest_generate_password()
    payload = {
        "client_id": internal_oauth_client["client_id"],
        "current_password": setup_data["current_password"],
        "new_password": valid_password,
        "confirm_password": valid_password,
    }

    if overrides:
        payload.update(overrides)
    return payload


def get_headers(tenant_uuid: str | None, access_token: str | None, valid_headers=None):
    headers = {
        X_TENANT_UUID: tenant_uuid,
        AUTHORIZATION_HEADER: f"Bearer {access_token}",
    }
    if valid_headers:
        headers.update(valid_headers)
    return headers


@pytest.fixture(scope="class")
def setup_data(
    sync_tenant_db_session_object,
    sync_central_db_session_object,
    tenant_uuid: str,
    internal_oauth_client: dict,
):
    current_password = unittest_generate_password()
    with sync_tenant_db_session_object as session_object:
        with session_object.begin():
            valid_doctor_user = unittest_insert_doctor(
                sync_tenant_db_session_object,
                tenant_uuid=tenant_uuid,
                password=current_password,
            )
            invalid_doctor_user_1 = unittest_insert_doctor(
                sync_tenant_db_session_object,
                tenant_uuid=tenant_uuid,
                custom_user_fields={"status": False},
            )
            invalid_doctor_user_2 = unittest_insert_doctor(
                sync_tenant_db_session_object,
                tenant_uuid=tenant_uuid,
                password=current_password,
            )

    with sync_central_db_session_object as session_object:
        with session_object.begin():
            token = unittest_dynamic_create_oauth_token(
                sync_central_db_session_object,
                user=valid_doctor_user,
                client_id=internal_oauth_client["client_id"],
                clinic_doctor_id=valid_doctor_user.id,
                tenant_uuid=tenant_uuid,
            )
            invalid_token = unittest_dynamic_create_oauth_token(
                sync_central_db_session_object,
                user=invalid_doctor_user_1,
                client_id=internal_oauth_client["client_id"],
                clinic_doctor_id=invalid_doctor_user_1.id,
                tenant_uuid=tenant_uuid,
            )
            expired_token = unittest_dynamic_create_oauth_token(
                sync_central_db_session_object,
                user=invalid_doctor_user_2,
                client_id=internal_oauth_client["client_id"],
                tenant_uuid=tenant_uuid,
                clinic_doctor_id=invalid_doctor_user_2.id,
                token_expire=-15,
            )

    return {
        "doctor_id": valid_doctor_user.id,
        "current_password": current_password,
        "access_token": token.access_token,
        "invalid_access_token": invalid_token.access_token,
        "expired_access_token": expired_token.access_token,
    }


def test_change_password_success(
    client,
    setup_data,
    tenant_uuid,
    internal_oauth_client,
    sync_central_db_session_object,
):
    payload = get_valid_payload(internal_oauth_client, setup_data)
    headers = get_headers(
        tenant_uuid=tenant_uuid,
        access_token=setup_data["access_token"],
    )

    response = client.post(
        "/change-password/doctor",
        json=payload,
        headers=headers,
    )

    assert response.status_code == HTTPStatus.OK.value
    assert response.json["success"] is True
    with sync_central_db_session_object.begin():
        oauth_tokens = unittest_query_oauth_tokens(
            sync_central_db_session_object,
            internal_oauth_client["client_id"],
            setup_data["doctor_id"],
            tenant_uuid,
        )
        assert len(oauth_tokens) == 0


@pytest.mark.parametrize(
    "new_password, confirm_password",
    [
        # new_password != confirm_password
        (
            unittest_generate_password(),
            unittest_generate_password(),
        ),
        # new_password: too short
        (
            unittest_generate_password(length=7),
            unittest_generate_password(),
        ),
        # new_password: missing uppercase letter
        (
            unittest_generate_password(include_uppercase=False),
            unittest_generate_password(),
        ),
        # new_password: missing lowercase letter
        (
            unittest_generate_password(include_lowercase=False),
            unittest_generate_password(),
        ),
        # new_password: missing digit
        (
            unittest_generate_password(include_digits=False),
            unittest_generate_password(),
        ),
        # new_password: missing special character
        (
            unittest_generate_password(include_special=False),
            unittest_generate_password(),
        ),
        # confirm_password: too short
        (
            unittest_generate_password(),
            unittest_generate_password(length=7),
        ),
        # confirm_password: missing uppercase letter
        (
            unittest_generate_password(),
            unittest_generate_password(include_uppercase=False),
        ),
        # confirm_password: missing lowercase letter
        (
            unittest_generate_password(),
            unittest_generate_password(include_lowercase=False),
        ),
        # confirm_password: missing digit
        (
            unittest_generate_password(),
            unittest_generate_password(include_digits=False),
        ),
        # confirm_password: missing special character
        (
            unittest_generate_password(),
            unittest_generate_password(include_special=False),
        ),
    ],
)
def test_change_password_invalid_password(
    client,
    setup_data,
    tenant_uuid,
    clinic_no,
    internal_oauth_client,
    new_password,
    confirm_password,
):
    payload = get_valid_payload(
        internal_oauth_client,
        setup_data,
        overrides={
            "new_password": new_password,
            "confirm_password": confirm_password,
        },
    )
    headers = get_headers(
        tenant_uuid=tenant_uuid,
        access_token=setup_data["access_token"],
    )

    response = client.post(
        "/change-password/doctor",
        json=payload,
        headers=headers,
    )

    assert response.status_code == HTTPStatus.BAD_REQUEST.value
    result = response.json
    assert result["success"] is False


def test_change_password_same_as_current(
    client,
    setup_data,
    tenant_uuid,
    internal_oauth_client,
):
    current_password = setup_data["current_password"]
    payload = get_valid_payload(
        internal_oauth_client,
        setup_data,
        overrides={
            "new_password": current_password,
            "confirm_password": current_password,
        },
    )
    headers = get_headers(
        tenant_uuid=tenant_uuid,
        access_token=setup_data["access_token"],
    )

    response = client.post(
        "/change-password/doctor",
        json=payload,
        headers=headers,
    )

    assert response.status_code == HTTPStatus.BAD_REQUEST.value
    result = response.json
    assert result["success"] is False


@pytest.mark.parametrize(
    "payload_func, headers_func, status_code, expected_message, expected_code",
    [
        # Invalid Payload
        (
            lambda client, current_password: {"invalid_field": "invalid_value"},
            lambda tenant_uuid, setup_data: get_headers(
                tenant_uuid, setup_data["access_token"]
            ),
            HTTPStatus.BAD_REQUEST.value,
            CustomMessageCode.INVALID_REQUEST_DATA.title,
            CustomMessageCode.INVALID_REQUEST_DATA.code,
        ),
        # Missing Headers
        (
            lambda client, current_password: {
                "client_id": client["client_id"],
                "current_password": current_password,
            },
            lambda tenant_uuid, setup_data: {},
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.UNAUTHORIZED_ERROR.title,
            CustomMessageCode.UNAUTHORIZED_ERROR.code,
        ),
        # Invalid Tenant UUID Header
        (
            lambda client, current_password: {
                "client_id": client["client_id"],
                "current_password": current_password,
            },
            lambda tenant_uuid, setup_data: get_headers(
                "invalid_tenant_uuid", setup_data["access_token"]
            ),
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.UNAUTHORIZED_ERROR.title,
            CustomMessageCode.UNAUTHORIZED_ERROR.code,
        ),
        # Invalid Access Token Header
        (
            lambda client, current_password: {
                "client_id": client["client_id"],
                "current_password": current_password,
            },
            lambda tenant_uuid, setup_data: get_headers(
                tenant_uuid, "invalid_access_token"
            ),
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.TOKEN_SIGNATURE_VERIFICATION_FAILED.title,
            CustomMessageCode.TOKEN_SIGNATURE_VERIFICATION_FAILED.code,
        ),
        # Expired Access Token
        (
            lambda client, current_password: {
                "client_id": client["client_id"],
                "current_password": current_password,
            },
            lambda tenant_uuid, setup_data: get_headers(
                tenant_uuid, setup_data["expired_access_token"]
            ),
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.TOKEN_SIGNATURE_VERIFICATION_FAILED.title,
            CustomMessageCode.TOKEN_SIGNATURE_VERIFICATION_FAILED.code,
        ),
        # Invalid Client Id
        (
            lambda client, current_password: {
                "client_id": "invalid_client_id",
                "current_password": current_password,
            },
            lambda tenant_uuid, setup_data: get_headers(
                tenant_uuid, setup_data["access_token"]
            ),
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title,
            CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.code,
        ),
        # Invalid Current Password
        (
            lambda client, current_password: {
                "client_id": client["client_id"],
                "current_password": "invalid_current_password",  # pragma: allowlist secret
            },
            lambda tenant_uuid, setup_data: get_headers(
                tenant_uuid, setup_data["access_token"]
            ),
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.INVALID_CURRENT_PASSWORD.title,
            CustomMessageCode.INVALID_CURRENT_PASSWORD.code,
        ),
        # Invalid Doctor User
        (
            lambda client, current_password: {
                "client_id": client["client_id"],
                "current_password": current_password,
            },
            lambda tenant_uuid, setup_data: get_headers(
                tenant_uuid, setup_data["invalid_access_token"]
            ),
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.DOCTOR_USER_NOT_FOUND.title,
            CustomMessageCode.DOCTOR_USER_NOT_FOUND.code,
        ),
    ],
)
def test_change_password_case_invalid(
    client,
    tenant_uuid,
    clinic_no,
    internal_oauth_client,
    setup_data,
    payload_func,
    headers_func,
    status_code,
    expected_message,
    expected_code,
):
    valid_password = unittest_generate_password()
    payload = {
        "new_password": valid_password,
        "confirm_password": valid_password,
        **payload_func(
            internal_oauth_client,
            setup_data["current_password"],
        ),
    }
    headers = headers_func(tenant_uuid, setup_data)

    response = client.post(
        "/change-password/doctor",
        json=payload,
        headers=headers,
    )

    assert response.status_code == status_code
    result = response.json
    assert result["success"] is False
    assert result["message"] == expected_message
    assert result["messageCode"] == expected_code


@pytest.mark.parametrize(
    "status_code, expected_message, expected_code, patch_funcs, call_counts",
    [
        (
            HTTPStatus.BAD_REQUEST.value,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "db.db_connection.TenantDatabase.get_sync_db_session_instance",
                    side_effect=DBAPIError("Database error!", None, None),
                ),
            ],
            [1],
        ),
        (
            HTTPStatus.BAD_REQUEST.value,
            CustomMessageCode.UNKNOWN_ERROR.title,
            CustomMessageCode.UNKNOWN_ERROR.code,
            [
                patch(
                    "db.db_connection.CentralDatabase.get_sync_db_session_instance",
                    side_effect=DBAPIError("Database error!", None, None),
                ),
            ],
            [3],
        ),
    ],
)
def test_api_doctor_login_case_exception(
    client,
    tenant_uuid,
    clinic_no,
    internal_oauth_client,
    setup_data,
    status_code,
    expected_message,
    expected_code,
    patch_funcs,
    call_counts,
):
    payload = get_valid_payload(internal_oauth_client, setup_data)
    headers = get_headers(
        tenant_uuid=tenant_uuid,
        access_token=setup_data["access_token"],
    )

    patch_objs = []
    with ExitStack() as stack:
        for patch_func in patch_funcs:
            mock_obj = stack.enter_context(patch_func)
            patch_objs.append(mock_obj)

        response = client.post(
            "/change-password/doctor",
            json=payload,
            headers=headers,
        )

    assert response.status_code == status_code
    result = response.json
    assert result["success"] is False
    assert result["message"] == expected_message
    assert result["messageCode"] == expected_code
    for idx, patch_obj in enumerate(patch_objs):
        assert patch_obj.call_count == call_counts[idx]
