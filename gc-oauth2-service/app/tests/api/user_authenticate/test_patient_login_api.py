from http import HTTPStatus

import pytest
from core.messages import CustomMessageCode
from tests.helpers.insert_data.insert_patient_user import unittest_create_patient_user
from werkzeug.security import gen_salt


def get_payload(overrides=None):
    payload = {
        "clinic_no": gen_salt(24),
        "client_id": gen_salt(24),
        "username": gen_salt(24),
        "password": gen_salt(24),  # pragma: allowlist secret
    }
    if overrides:
        payload.update(overrides)
    return payload


@pytest.fixture(scope="class")
def setup_data(
    sync_tenant_db_session_object,
    tenant_uuid,
):
    username = gen_salt(24)
    password = gen_salt(24)

    with sync_tenant_db_session_object.begin():
        patient_user = unittest_create_patient_user(
            sync_tenant_db_session_object,
            username=username,
            password=password,
            tenant_uuid=tenant_uuid,
        )

        return {
            "patient_user_id": patient_user.id,
            "username": username,
            "password": password,
        }


def test_login_success(
    client,
    sync_tenant_db_session_object,
    setup_data,
    tenant_uuid,
    clinic_no,
    internal_oauth_client,
):
    payload = get_payload(
        {
            "clinic_no": clinic_no,
            "client_id": internal_oauth_client["client_id"],
            **setup_data,
        },
    )

    response = client.post("/login/patient", json=payload)
    assert response.status_code == HTTPStatus.OK.value

    # Verify response contains token data
    body = response.get_json()
    assert isinstance(body, dict)
    assert "data" in body
    assert isinstance(body["data"], dict)
    # Ensure tokens are present in data
    assert "access_token" in body["data"]
    assert "refresh_token" in body["data"]
    assert "token_type" in body["data"]
    assert "expires_in" in body["data"]


@pytest.mark.parametrize(
    "data_idx, invalid_payload, expected_status_code, expected_message",
    [
        (
            0,
            # Invalid payload, missing request body
            {},
            HTTPStatus.BAD_REQUEST.value,
            CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
        ),
        (
            1,
            # Invalid payload, missing request body
            None,
            HTTPStatus.UNAUTHORIZED.value,
            CustomMessageCode.UNKNOWN_ERROR.title,
        ),
    ],
)
def test_login_invalid_request(
    client,
    data_idx,
    invalid_payload,
    expected_status_code,
    expected_message,
):
    response = client.post("/login/patient", json=invalid_payload)

    assert response.status_code == expected_status_code
    result = response.get_json()
    assert result["message"] == expected_message


def test_login_wrong_password(
    client,
    sync_tenant_db_session_object,
    setup_data,
    tenant_uuid,
    clinic_no,
    internal_oauth_client,
):
    payload = get_payload(
        {
            "clinic_no": clinic_no,
            "client_id": internal_oauth_client["client_id"],
            **setup_data,
        }
    )

    # Use incorrect password
    payload_with_wrong_password = {
        **payload,
        "password": "WrongPassword123!",  # pragma: allowlist secret
    }

    response = client.post("/login/patient", json=payload_with_wrong_password)

    assert response.status_code == HTTPStatus.UNAUTHORIZED.value
    result = response.get_json()
    assert result["message"] == CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.title


def test_login_unknown_username(
    client,
    sync_tenant_db_session_object,
    setup_data,
    tenant_uuid,
    clinic_no,
    internal_oauth_client,
):
    payload = get_payload(
        {
            "clinic_no": clinic_no,
            "client_id": internal_oauth_client["client_id"],
            **setup_data,
        }
    )
    # Use unknown username
    payload_with_unknown_username = {**payload, "username": "unknown_" + gen_salt(12)}

    response = client.post("/login/patient", json=payload_with_unknown_username)

    assert response.status_code == HTTPStatus.UNAUTHORIZED.value
    result = response.get_json()
    assert result["message"] == CustomMessageCode.USER_NOT_FOUND.title


def test_login_invalid_client_id(
    client,
    sync_tenant_db_session_object,
    setup_data,
    tenant_uuid,
    clinic_no,
):
    payload = get_payload(
        {
            "clinic_no": clinic_no,
            **setup_data,
        }
    )
    # Use invalid client_id
    payload_with_invalid_client = {**payload, "client_id": gen_salt(24)}

    response = client.post("/login/patient", json=payload_with_invalid_client)

    assert response.status_code == HTTPStatus.UNAUTHORIZED.value
    result = response.get_json()
    assert result["message"] == CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title


def test_login_invalid_clinic_no(
    client,
    sync_tenant_db_session_object,
    setup_data,
    tenant_uuid,
    clinic_no,
    internal_oauth_client,
):
    payload = get_payload(
        {
            "client_id": internal_oauth_client["client_id"],
            **setup_data,
        }
    )
    # Use invalid clinic_no
    payload_with_invalid_tenant = {
        **payload,
        "clinic_no": "invalid-clinic-no-" + gen_salt(8),
    }

    response = client.post("/login/patient", json=payload_with_invalid_tenant)

    assert response.status_code == HTTPStatus.UNAUTHORIZED.value
    result = response.get_json()
    assert result["message"] == CustomMessageCode.TENANT_NOT_FOUND.title
