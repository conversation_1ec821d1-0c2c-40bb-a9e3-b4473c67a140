import time
from datetime import datetime, timezone
from typing import ClassVar

from authlib.integrations.flask_oauth2 import AuthorizationServer, ResourceProtector
from authlib.integrations.sqla_oauth2 import (
    create_bearer_token_validator,
    create_revocation_endpoint,
)
from authlib.oauth2.rfc6749 import grants
from authlib.oauth2.rfc6749.errors import InvalidGrantError, InvalidRequestError
from authlib.oauth2.rfc7636 import CodeChallenge
from configuration.settings import configuration
from core.messages import INVALID_AUTHORIZATION_CODE, INVALID_TOKEN
from db.db_connection import CentralDatabase
from sqlalchemy import delete, select

from gc_dentist_shared.central_models import (
    GlobalUser,
    OAuth2AuthorizationCode,
    OAuth2Client,
    OAuth2Token,
)
from gc_dentist_shared.core.common.jwt_token import encode_jwt_token
from gc_dentist_shared.core.logger.config import log


class MyAuthorizationServer(AuthorizationServer):

    def generate_token(self, grant_type, client, user, scope, **kwargs):
        return encode_jwt_token(
            configuration.RSA_KEY_MANIFEST.get("current_kid"),
            configuration.JWT_RSA_PRIVATE_KEY,
            configuration.JWT_ALGORITHM,
            configuration.ACCESS_TOKEN_EXPIRE_MINUTES,
            grant_type,
            client,
            user,
            scope,
        )

    def query_client(self, client_id):
        with CentralDatabase.get_sync_db_session_instance() as central_db_session:
            with central_db_session.begin():
                result = central_db_session.execute(
                    select(OAuth2Client).where(OAuth2Client.client_id == client_id)
                )
                data_client = result.scalars().first()

                return data_client

    def save_token(self, token, request):
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    client = request.client
                    user = request.user

                    if request.payload.grant_type == "refresh_token":
                        delete_stmt = delete(OAuth2Token).where(
                            OAuth2Token.global_user_id == user.id
                        )
                        central_db_session.execute(delete_stmt)

                    new_token = OAuth2Token(
                        client_id=client.client_id,
                        global_user_id=user.id,
                        token_type=token.get("token_type"),
                        access_token=token.get("access_token"),
                        refresh_token=token.get("refresh_token"),
                        scope=token.get("scope"),
                        issued_at=int(datetime.now(timezone.utc).timestamp()),
                        refresh_token_expires_at=int(time.time())
                        + (configuration.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60),
                        expires_in=token.get("expires_in"),
                    )
                    central_db_session.add(new_token)
        except Exception as e:
            log.error(f"❌ Error saving token: {str(e)}")
            raise e


class AuthorizationCodeGrant(grants.AuthorizationCodeGrant):
    TOKEN_ENDPOINT_AUTH_METHODS: ClassVar[list] = [
        "client_secret_basic",
        "client_secret_post",
        "none",
    ]

    def save_authorization_code(self, code, request):
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    code_challenge = request.payload.data.get("code_challenge")
                    code_challenge_method = request.payload.data.get(
                        "code_challenge_method"
                    )
                    auth_code = OAuth2AuthorizationCode(
                        code=code,
                        client_id=request.client.client_id,
                        redirect_uri=request.payload.redirect_uri,
                        scope=request.payload.scope,
                        global_user_id=request.user.id,
                        expires_at=int(time.time())
                        + (configuration.OAUTH_CODE_EXPIRE_MINUTES * 60),
                        code_challenge=code_challenge,
                        code_challenge_method=code_challenge_method,
                    )
                    central_db_session.add(auth_code)
                    return auth_code
        except Exception as e:
            log.error(f"❌ Error saving authorization code: {str(e)}")
            raise e

    def query_authorization_code(self, code, client):
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    stmt = select(OAuth2AuthorizationCode).where(
                        OAuth2AuthorizationCode.code == code,
                        OAuth2AuthorizationCode.client_id == client.client_id,
                    )
                    code_obj = central_db_session.execute(stmt).scalar_one_or_none()
                    if code_obj and not code_obj.is_expired():
                        return code_obj
                    return None
        except Exception as e:
            log.error("❌ Error querying authorization code: {str(e)}")
            raise e

    def delete_authorization_code(self, authorization_code):
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    central_db_session.delete(authorization_code)
        except Exception as e:
            log.error("❌ Error deleting authorization code: {str(e)}")
            raise e

    def authenticate_user(self, authorization_code):
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    stmt = select(OAuth2AuthorizationCode).where(
                        OAuth2AuthorizationCode.code == authorization_code.code,
                        OAuth2AuthorizationCode.client_id
                        == authorization_code.client_id,
                    )
                    db_code = central_db_session.execute(stmt).scalar_one_or_none()
                    if db_code is None or db_code.is_expired():
                        raise InvalidGrantError(INVALID_AUTHORIZATION_CODE)

                    user = central_db_session.get(GlobalUser, db_code.global_user_id)
                    return user
        except Exception as e:
            log.error("❌ Error authenticating user: {str(e)}")
            raise e


class PasswordGrant(grants.ResourceOwnerPasswordCredentialsGrant):
    def authenticate_user(self, username, password):
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    stmt = select(GlobalUser).where(
                        GlobalUser.username == username, GlobalUser.password == password
                    )
                    user = central_db_session.execute(stmt).scalar_one_or_none()
                    if user is not None and user.check_password(password):
                        return user
        except Exception as e:
            log.error("❌ Error PasswordGrant authenticating user: {str(e)}")
            raise e


class RefreshTokenGrant(grants.RefreshTokenGrant):
    TOKEN_ENDPOINT_AUTH_METHODS: ClassVar[list] = [
        "client_secret_basic",
        "client_secret_post",
    ]

    def authenticate_refresh_token(self, refresh_token):
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    stmt = select(OAuth2Token).where(
                        OAuth2Token.refresh_token == refresh_token
                    )
                    token = central_db_session.execute(stmt).scalar_one_or_none()
                    if token and not token.is_refresh_token_expired():
                        return token
                    else:
                        raise InvalidRequestError(INVALID_TOKEN)
        except Exception as e:
            log.error(
                f"❌ Error RefreshTokenGrant authenticate_refresh_token : {str(e)}"
            )
            raise e

    def authenticate_user(self, credential):
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    stmt = select(GlobalUser).where(
                        GlobalUser.id == credential.global_user_id
                    )
                    user = central_db_session.execute(stmt).scalar_one_or_none()
                    return user
        except Exception as e:
            log.error(f"❌ Error RefreshTokenGrant authenticate_user : {str(e)}")
            raise e

    def revoke_old_credential(self, credential):
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    credential.revoked = True
                    central_db_session.add(credential)
        except Exception as e:
            log.error(f"❌ Error revoking old credential: {str(e)}")
            raise e


# db_sesion = CentralDatabase.get_sync_db_session_instance()
# query_client = create_query_client_func(db_sesion, OAuth2Client)
# save_token = create_save_token_func(db_sesion, OAuth2Token)
# authorization = AuthorizationServer(
#     query_client=query_client,
#     save_token=save_token,
# )
authorization = MyAuthorizationServer()
require_oauth = ResourceProtector()


def config_oauth(app):
    authorization.init_app(app)
    with CentralDatabase.get_sync_db_session_instance() as central_db_session:
        with central_db_session.begin():
            # support all grants
            authorization.register_grant(grants.ImplicitGrant)
            authorization.register_grant(grants.ClientCredentialsGrant)
            authorization.register_grant(
                AuthorizationCodeGrant, [CodeChallenge(required=True)]
            )
            authorization.register_grant(PasswordGrant)
            authorization.register_grant(RefreshTokenGrant)

            # support revocation
            revocation_cls = create_revocation_endpoint(central_db_session, OAuth2Token)
            authorization.register_endpoint(revocation_cls)

            # protect resource
            bearer_cls = create_bearer_token_validator(central_db_session, OAuth2Token)
            require_oauth.register_token_validator(bearer_cls())
