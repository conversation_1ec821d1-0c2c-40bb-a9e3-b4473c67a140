from http import HTT<PERSON>tatus

from core.common.api_response import Api<PERSON><PERSON>ponse
from core.messages import CustomMessageCode
from flask import Blueprint, request
from schemas.auth_schema import SystemUserLoginRequest, SystemUserOTPRequest
from services.system_user_auth_service import SystemUserAuthService

from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

bp = Blueprint("system_user", __name__)


@bp.route("/login/system-user/send-otp", methods=["POST"])
def login_system_user_with_otp():
    try:
        json_data = request.get_json()
        if not json_data:
            return ApiResponse.error(
                message=CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
                message_code=CustomMessageCode.MISSING_JSON_IN_REQUEST.code,
            )
        login_request_data = SystemUserLoginRequest(**json_data)
        system_user_auth_service = SystemUserAuthService()
        response = system_user_auth_service.authenticate_user_with_otp(
            obj=login_request_data
        )
        return ApiResponse.success(
            data=response.model_dump(),
            message=CustomMessageCode.SEND_MAIL_OTP_SUCCESS.title,
            message_code=CustomMessageCode.SEND_MAIL_OTP_SUCCESS.code,
        )

    except CustomValueError as e:
        log.error(f"❌ System user send otp CustomValueError: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )
    except ValueError as e:
        log.error(f"❌ System user send otp ValueError: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.INVALID_REQUEST_DATA.title,
            message_code=CustomMessageCode.INVALID_REQUEST_DATA.code,
        )

    except Exception as e:
        log.error(f"❌ System user send otp Exception: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )


@bp.route("/login/system-user/verify-otp", methods=["POST"])
def verify_otp_for_system_user():
    json_data = request.get_json()
    if not json_data:
        return ApiResponse.error(
            message=CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
            message_code=CustomMessageCode.MISSING_JSON_IN_REQUEST.code,
        )

    login_data = SystemUserOTPRequest(**json_data)
    system_auth_service = SystemUserAuthService()
    response = system_auth_service.verify_otp(obj=login_data)

    return ApiResponse.success(data=response.model_dump())


@bp.route("/login/system-user", methods=["POST"])
def login_system_user_with_username_password():
    try:
        json_data = request.get_json()
        if not json_data:
            return ApiResponse.error(
                message=CustomMessageCode.MISSING_JSON_IN_REQUEST.title,
                message_code=CustomMessageCode.MISSING_JSON_IN_REQUEST.code,
            )

        login_request_data = SystemUserLoginRequest(**json_data)
        system_user_auth_service = SystemUserAuthService()
        response = system_user_auth_service.login_system_user_with_username_password(
            obj=login_request_data
        )
        return ApiResponse.success(data=response.model_dump())

    except CustomValueError as e:
        log.error(f"❌ SystemUser login failed with error: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )
    except ValueError as e:
        log.error(f"❌ SystemUser login valid error: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.INVALID_REQUEST_DATA.title,
            message_code=CustomMessageCode.INVALID_REQUEST_DATA.code,
        )
    except Exception as e:
        log.error(f"❌ SystemUser login exception: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            status_code=HTTPStatus.UNAUTHORIZED.value,
        )
