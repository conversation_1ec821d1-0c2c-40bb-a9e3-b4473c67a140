from dataclasses import dataclass
from typing import Optional

from enums.login_audit_enum import LoginAuditEnum


@dataclass
class LoginAuditContext:
    ip_address: str
    user_agent: str
    client_id: Optional[str] = None
    tenant_uuid: Optional[str] = None
    clinic_doctor_id: Optional[int] = None
    system_user_id: Optional[int] = None
    clinic_patient_id: Optional[int] = None
    global_user_id: Optional[int] = None
    status: Optional[int] = LoginAuditEnum.SUCCESS.value
    failure_reason: Optional[str] = None

    def to_dict(self):
        return self.__dict__.copy()
