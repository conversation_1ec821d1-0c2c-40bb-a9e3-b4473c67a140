"""
RSA Key Pair Generator for OAuth2 Service
Generates RSA key pairs for JWT token signing and verification
"""

import os
import sys
from datetime import datetime
from pathlib import Path

from gc_dentist_shared.core.common.digital_signature import DigitalSignature
from gc_dentist_shared.core.logger.config import log

SCRIPTS_DIR = Path(__file__).resolve().parent
APP_DIR = SCRIPTS_DIR.parent
if str(APP_DIR) not in sys.path:
    sys.path.insert(0, str(APP_DIR))

from configuration.settings import configuration  # noqa: E402


def generate_rsa_key_pair(private_path: str, public_path: str):
    """
    Generate RSA key pair and save to specified paths
    Args:
        private_path: Path to save private key
        public_path: Path to save public key
    """
    # Generate private key
    rsa_key_pair = DigitalSignature.create_rsa_key_pair()
    private_key = rsa_key_pair["private_key"]
    public_key = rsa_key_pair["public_key"]

    # Create directory if not exists
    os.makedirs(os.path.dirname(private_path), exist_ok=True)
    os.makedirs(os.path.dirname(public_path), exist_ok=True)

    # Save private key
    with open(private_path, "wb") as f:
        f.write(private_key.encode())

    # Save public key
    with open(public_path, "wb") as f:
        f.write(public_key.encode())

    log.info("✅ Generated RSA key pair:")
    log.info(f"   Private: {private_path}")
    log.info(f"   Public:  {public_path}")
    log.info(f"   Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


def generate_default_rsa_keys():

    # Base directory for manifest-relative paths should be the `app` directory
    current_kid = configuration.RSA_KEY_MANIFEST.get("current_kid")
    private_path = APP_DIR / configuration.RSA_KEY_MANIFEST.get("keys").get(
        current_kid
    ).get("private_path")
    public_path = APP_DIR / configuration.RSA_KEY_MANIFEST.get("keys").get(
        current_kid
    ).get("public_path")

    generate_rsa_key_pair(private_path, public_path)


if __name__ == "__main__":
    generate_default_rsa_keys()
