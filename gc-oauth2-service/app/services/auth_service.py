import time
from datetime import datetime, timezone

from configuration.settings import configuration
from core.constants import AUTHORI<PERSON><PERSON><PERSON>, TOKEN_PREFIX
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase
from schemas.oauth_schema import CreateOauthClient
from services.key_manager_service import <PERSON><PERSON><PERSON><PERSON>
from sqlalchemy import delete, select
from sqlalchemy.exc import DB<PERSON><PERSON>rror, OperationalError
from sqlalchemy.orm import Session
from werkzeug.security import gen_salt

from gc_dentist_shared.central_models import OAuth2Client, OAuth2Token
from gc_dentist_shared.core.constants import X_TENANT_UUID
from gc_dentist_shared.core.decorators.retry import sync_retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log


class OAuth2ClientService:
    @staticmethod
    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="dynamic_save_token_internal_client_sync",
    )
    def dynamic_save_token_internal_client_sync(
        central_db_session: Session,
        token: dict,
        client_id: str,
        delete_existing_token: bool = False,
        **kwargs,
    ):
        try:
            if delete_existing_token:
                OAuth2ClientService._delete_existing_token(central_db_session, kwargs)

            new_token = OAuth2Token(
                client_id=client_id,
                token_type=token.get("token_type"),
                access_token=token.get("access_token"),
                refresh_token=token.get("refresh_token"),
                scope=token.get("scope"),
                issued_at=int(datetime.now(timezone.utc).timestamp()),
                refresh_token_expires_at=int(time.time())
                + (configuration.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60),
                expires_in=token.get("expires_in"),
                **kwargs,
            )
            central_db_session.add(new_token)
        except Exception as e:
            log.error(f"❌ Error saving token: {str(e)}")
            raise e

    @staticmethod
    def _delete_existing_token(central_db_session, kwargs):
        condition_list = {
            OAuth2Token.global_user_id: kwargs.get("global_user_id"),
            OAuth2Token.clinic_patient_id: kwargs.get("clinic_patient_id"),
            OAuth2Token.clinic_doctor_id: kwargs.get("clinic_doctor_id"),
            OAuth2Token.system_user_id: kwargs.get("system_user_id"),
            OAuth2Token.tenant_uuid: kwargs.get("tenant_uuid"),
        }
        conditions = [
            column == value
            for column, value in condition_list.items()
            if value is not None
        ]
        if not conditions:
            raise ValueError(CustomMessageCode.INVALID_CONDITION.title)

        delete_stmt = delete(OAuth2Token).where(*conditions)
        central_db_session.execute(delete_stmt)

    @staticmethod
    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="get_internal_client_by_client_id_sync",
    )
    def get_internal_client_by_client_id_sync(db_session, client_id: str):
        result = db_session.execute(
            select(OAuth2Client).where(
                OAuth2Client.client_id == client_id, OAuth2Client.is_active.is_(True)
            )
        )
        return result.scalar_one_or_none()

    @staticmethod
    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="create_oauth_client",
    )
    def create_oauth_client_sync(obj: CreateOauthClient) -> None:
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    client_id = gen_salt(24)
                    client_id_issued_at = int(time.time())
                    client = OAuth2Client(
                        name=obj.client_name,
                        client_id=client_id,
                        client_id_issued_at=client_id_issued_at,
                    )
                    client.set_client_metadata(obj.model_dump(mode="json"))
                    client.client_secret = gen_salt(48)

                    central_db_session.add(client)
        except Exception as e:
            log.error(f"❌ Error creating OAuth client: {str(e)}")
            raise e

    @staticmethod
    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="verify_doctor_auth_token_sync",
    )
    def verify_doctor_auth_token_sync(request) -> OAuth2Token:
        try:
            auth: str = (
                request.headers.get(AUTHORIZATION)
                or request.cookies.get(AUTHORIZATION)
                or ""
            )
            _, _, token = auth.partition(f"{TOKEN_PREFIX} ")
            if not token:
                log.error("❌ JWTAuthMiddleware missing Bearer token")
                raise CustomValueError(
                    CustomMessageCode.UNAUTHORIZED_ERROR.title,
                    CustomMessageCode.UNAUTHORIZED_ERROR.code,
                )

            key_manager = KeyManager()
            payload = key_manager.get_payload_access_token(token)
            if not payload:
                log.error("❌ Invalid JWT token payload")
                raise CustomValueError(
                    CustomMessageCode.UNAUTHORIZED_ERROR.title,
                    CustomMessageCode.UNAUTHORIZED_ERROR.code,
                )

            tenant_uuid = request.headers.get(X_TENANT_UUID) or payload.get(
                "tenant_uuid"
            )
            if not tenant_uuid:
                log.error("❌ Missing tenant UUID in request headers")
                raise CustomValueError(
                    CustomMessageCode.X_TENANT_UUID_IS_REQUIRED.title,
                    CustomMessageCode.X_TENANT_UUID_IS_REQUIRED.code,
                )

            user_id = int(payload.get("sub"))
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    result = central_db_session.execute(
                        select(OAuth2Token).where(
                            OAuth2Token.access_token == token,
                            OAuth2Token.clinic_doctor_id == user_id,
                            OAuth2Token.tenant_uuid == tenant_uuid,
                            OAuth2Token.expires_in > int(time.time()),
                        )
                    )
                    token = result.scalar_one_or_none()
            if not token:
                log.error("❌ Invalid or expired access token")
                raise CustomValueError(
                    CustomMessageCode.UNAUTHORIZED_ERROR.title,
                    CustomMessageCode.UNAUTHORIZED_ERROR.code,
                )
            return token
        except Exception as e:
            log.error(f"❌ Error verifying access token: {str(e)}")
            raise e
