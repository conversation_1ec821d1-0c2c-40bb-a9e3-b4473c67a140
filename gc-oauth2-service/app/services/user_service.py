from typing import Optional

from configuration.settings import configuration
from db.db_connection import CentralDatabase
from sqlalchemy import select

from gc_dentist_shared.central_models import GlobalUser
from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.logger.config import log


def get_global_user_by_name(username: str):

    try:
        with CentralDatabase.get_sync_db_session_instance() as central_db_session:
            with central_db_session.begin():
                aes_gcm = AesGCMRotation(configuration)
                user = central_db_session.execute(
                    select(GlobalUser).where(
                        GlobalUser.username_hash == aes_gcm.sha256_hash(username)
                    )
                ).scalar_one_or_none()
                return user
    except Exception as e:
        log.error(f"❌ Error get_global_user_by_name error: {str(e)}")
        return None


def get_global_user_by_id(user_id: Optional[int]):
    if not user_id:
        return None
    try:
        with CentralDatabase.get_sync_db_session_instance() as central_db_session:
            with central_db_session.begin():
                user = central_db_session.execute(
                    select(GlobalUser).where(GlobalUser.id == user_id)
                ).scalar_one_or_none()
                return user
    except Exception as e:
        log.error(f"❌ Error get_global_user_by_id error: {str(e)}")
        return None
