import json
import random
import string
from datetime import datetime, timedelta, timezone
from http import HTT<PERSON>tatus
from typing import Optional

from configuration.context.language_context import get_current_language
from configuration.settings import configuration
from core.constants import (
    OTP_BLOCK_TIME_MINUTES,
    OTP_LENGTH,
    OTP_MAX_SEND_ATTEMPTS,
    OTP_SEND_WINDOW_MINUTES,
    SYSTEM_USER_LOGIN_REDIS_PREFIX,
)
from core.decorators.login_audit import audit_login
from core.messages import CustomMessageCode
from dataclass.login_audit_context import LoginAuditContext
from db.db_connection import CentralDatabase
from schemas.auth_schema import (
    OAuthTokenResponse,
    SystemUserLoginRequest,
    SystemUserOTPRequest,
    SystemUserResponse,
)
from services.auth_service import OAuth2ClientService
from sqlalchemy import select
from sqlalchemy.exc import DBAPIError, OperationalError

from gc_dentist_shared.central_models import MailT<PERSON>plate, OAuth2Client, SystemUser
from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.common.jwt_token import encode_jwt_token
from gc_dentist_shared.core.common.synchronous_mail import SyncMailClient
from gc_dentist_shared.core.common.synchronous_redis import SyncRedisCli
from gc_dentist_shared.core.constants import UTF8
from gc_dentist_shared.core.decorators.retry import sync_retry_on_failure
from gc_dentist_shared.core.enums.mail_enum import MailTemplateCategoryEnum, MessageType
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.core.schemas.email_request_schema import MailRequest


class SystemUserAuthService:

    # region System User Login and Authentication

    def authenticate_user_with_otp(
        self, obj: SystemUserLoginRequest
    ) -> SystemUserResponse:
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                self._get_internal_oauth_client(central_db_session, obj.client_id)

                system_user = self._verify_system_user_credentials(
                    central_db_session,
                    username=obj.username,
                    plain_password=obj.password,
                )

                current_time = datetime.now(timezone.utc)

                if (
                    system_user.otp_lock_expires_at
                    and system_user.otp_lock_expires_at > current_time
                ):
                    raise CustomValueError(
                        message_code=CustomMessageCode.OTP_TEMPORARILY_LOCKED.code,
                        message=CustomMessageCode.OTP_TEMPORARILY_LOCKED.title,
                    )

            self._process_otp_with_rate_limiting(
                system_user=system_user,
                current_time=current_time,
            )

            return SystemUserResponse(user_id=system_user.id)

        except CustomValueError as e:
            log.error(
                f"❌ Login for system user by sending OTP via mail failed: {str(e)}"
            )
            raise e
        except ValueError as e:
            log.error(f"❌ System user login valid error: {str(e)}")
            raise CustomValueError(
                message=CustomMessageCode.INVALID_REQUEST_DATA.title,
                message_code=CustomMessageCode.INVALID_REQUEST_DATA.code,
            )
        except Exception as e:
            log.error(
                f"❌ Login for system user by sending OTP via mail failed exception: {str(e)}"
            )
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
                status_code=HTTPStatus.UNAUTHORIZED.value,
            )

    @audit_login
    def verify_otp(
        self, obj: SystemUserOTPRequest, login_audit_context: LoginAuditContext
    ) -> OAuthTokenResponse:
        """
        Verify the email OTP and generate access/refresh tokens

        :param obj: The verification data containing username and OTP
        :param login_audit_context: The login audit context from decorator
        :return: Response with access and refresh tokens
        """
        try:
            login_audit_context.client_id = obj.client_id
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    client = self._get_internal_oauth_client(
                        central_db_session, obj.client_id
                    )

                    system_user = self._get_system_user_by_id(
                        central_db_session, obj.user_id
                    )
                    login_audit_context.system_user_id = system_user.id

                    redis = SyncRedisCli.get_instance(configuration)
                    aes_gcm = AesGCMRotation(configuration)
                    otp_key = f"{SYSTEM_USER_LOGIN_REDIS_PREFIX}_{aes_gcm.sha256_hash(obj.user_id)}"
                    otp_data = redis.get(otp_key)

                    # Case OTP expired or not found -> resend OTP
                    if not otp_data:
                        raise CustomValueError(
                            message=CustomMessageCode.OTP_NOT_FOUND_OR_EXPIRED.title,
                            message_code=CustomMessageCode.OTP_NOT_FOUND_OR_EXPIRED.code,
                        )

                    # Case OTP incorrect
                    otp_data = json.loads(otp_data)
                    if otp_data["otp"] != obj.otp:
                        raise CustomValueError(
                            message=CustomMessageCode.OTP_INCORRECT.title,
                            message_code=CustomMessageCode.OTP_INCORRECT.code,
                        )

                    token = self._create_access_token(
                        central_db_session,
                        internal_client=client,
                        user=system_user,
                    )

                    # Delete the OTP key and reset otp_first_send_at
                    system_user.otp_first_send_at = None
                    central_db_session.add(system_user)
                    redis.delete(otp_key)

                    return token

        except CustomValueError as e:
            log.error(f"❌ Verify mail OTP failed with error: {str(e)}")
            raise e

        except ValueError as e:
            log.error(f"❌ System user login valid error: {str(e)}")
            raise CustomValueError(
                message=CustomMessageCode.INVALID_REQUEST_DATA.title,
                message_code=CustomMessageCode.INVALID_REQUEST_DATA.code,
            )
        except Exception as e:
            log.error(f"❌ Verify mail OTP exception: {str(e)}")
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
                status_code=HTTPStatus.UNAUTHORIZED.value,
            )

    # endregion

    # region Private Methods

    # OAuth Client Management
    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="get_internal_oauth_client",
    )
    def _get_internal_oauth_client(self, central_db_session, client_id: str):
        try:
            result = central_db_session.execute(
                select(OAuth2Client).where(
                    OAuth2Client.client_id == client_id,
                    OAuth2Client.is_active.is_(True),
                )
            )
            client = result.scalar_one_or_none()

            if not client:
                raise CustomValueError(
                    message=CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title,
                    message_code=CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.code,
                )

            return client

        except CustomValueError as e:
            log.error(f"❌ Oauth client not found with client_id: {client_id}")
            raise e

        except Exception as e:
            log.error(f"❌ Database error while finding oauth client: {str(e)}")
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            )

    # User Authentication and Validation
    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="verify_system_user_credentials",
    )
    def _verify_system_user_credentials(
        self, central_db_session, username: str, plain_password: str
    ):
        try:
            stmt = select(SystemUser).where(
                SystemUser.username == username, SystemUser.is_active.is_(True)
            )
            system_user = central_db_session.execute(stmt).scalar_one_or_none()
            if not system_user or not system_user.validate_password(
                plain_password=plain_password
            ):
                raise CustomValueError(
                    message=CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.title,
                    message_code=CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.code,
                )

            return system_user

        except CustomValueError as e:
            log.error(f"❌ Invalid system user credentials for username: {username}")
            raise e
        except Exception as e:
            log.error(
                f"❌ Database error while verifying system user credentials: {str(e)}"
            )
            raise e

    def _get_system_user_by_id(self, central_db_session, user_id: int) -> SystemUser:
        try:
            stmt = select(SystemUser).where(
                SystemUser.id == user_id, SystemUser.is_active.is_(True)
            )
            system_user = central_db_session.execute(stmt).scalar_one_or_none()
            if not system_user:
                raise CustomValueError(
                    message=CustomMessageCode.SYSTEM_USER_NOT_FOUND.title,
                    message_code=CustomMessageCode.SYSTEM_USER_NOT_FOUND.code,
                )

            return system_user
        except Exception as e:
            log.error(f"❌ System user not found for user_id: {user_id}")
            raise e

    # OTP Processing and Rate Limiting
    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="process_otp_with_rate_limiting",
    )
    def _process_otp_with_rate_limiting(
        self,
        system_user: SystemUser,
        current_time: datetime,
    ):
        try:
            redis = SyncRedisCli.get_instance(configuration)
            aes_gcm = AesGCMRotation(configuration)
            otp_key = f"{SYSTEM_USER_LOGIN_REDIS_PREFIX}_{aes_gcm.sha256_hash(system_user.id)}"

            otp_data = self._build_otp_data(system_user, current_time, redis, otp_key)

            redis.setex(
                name=otp_key,
                time=otp_data["key_ttl"],
                value=json.dumps(
                    {
                        "otp": otp_data["otp"],
                        "current_attempt": otp_data["current_attempt"],
                        "latest_send": current_time.isoformat(),
                    }
                ),
            )
            self._send_otp_to_mail(
                system_user_email=system_user.username,
                data={
                    "otp": otp_data["otp"],
                    "expires_in": OTP_SEND_WINDOW_MINUTES,
                    "logo_url": configuration.DEFAULT_MAIL_LOGO_URL,
                },
                template_category=MailTemplateCategoryEnum.LOGIN_FOR_SYSTEM_ADMIN.value,
            )

        except Exception as e:
            log.error(
                f"❌ Failed to process OTP for system user: {system_user.username}, error: {str(e)}"
            )
            raise e

    # Email and Template Management
    def _send_otp_to_mail(
        self,
        system_user_email: str,
        data: dict,
        template_category: int,
    ):
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    language = get_current_language() or "ja"
                    template = self._get_mail_template(
                        central_db_session,
                        template_category=template_category,
                        template_language=language,
                    )
                    mail_client = SyncMailClient.get_instance(configuration)
                    body_content = mail_client.render_body_content_template(
                        template=template.content, data_bidding=data
                    )

                    # Create mail request
                    mail_request = MailRequest(
                        recipients=[system_user_email],
                        subject=template.title,
                        body=body_content,
                        charset=UTF8,
                        data_bidding=data,
                        category=template.category,
                        subtype=MessageType.HTML,
                    )

                    result = mail_client.send_message(mail_request)
                    if result:
                        log.info(
                            f"✅ OTP email sent successfully to system user: {system_user_email}"
                        )
                    else:
                        raise CustomValueError(
                            message=CustomMessageCode.SEND_MAIL_OTP_FAILED.title,
                            message_code=CustomMessageCode.SEND_MAIL_OTP_FAILED.code,
                        )

        except Exception as e:
            log.error(
                f"❌ Failed to send email to system user: {system_user_email}, error: {str(e)}"
            )
            raise CustomValueError(
                message=CustomMessageCode.SEND_MAIL_OTP_FAILED.title,
                message_code=CustomMessageCode.SEND_MAIL_OTP_FAILED.code,
            )

    @sync_retry_on_failure(
        exceptions=(DBAPIError, OperationalError),
        log_prefix="get_mail_template",
    )
    def _get_mail_template(
        self, central_db_session, template_category: int, template_language: str = "ja"
    ) -> MailTemplate:
        """Get mail template from central database"""
        try:
            result = central_db_session.execute(
                select(MailTemplate).where(
                    MailTemplate.category == template_category,
                    MailTemplate.language == template_language,
                    MailTemplate.status.is_(True),
                )
            )
            template = result.scalar_one_or_none()
            if not template:
                raise CustomValueError(
                    message=CustomMessageCode.MAIL_TEMPLATE_NOT_FOUND.title,
                    message_code=CustomMessageCode.MAIL_TEMPLATE_NOT_FOUND.code,
                )

            return template

        except CustomValueError as e:
            log.error(f"❌ Mail template not found for category: {template_category}")
            raise e
        except (DBAPIError, OperationalError) as e:
            log.error(f"❌ Database error while finding mail template: {str(e)}")
            raise e

        except Exception as e:
            log.error(f"❌ Database error while finding mail template: {str(e)}")
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            )

    # Token Management
    def _create_access_token(
        self,
        db_session,
        internal_client: OAuth2Client,
        user: SystemUser,
        role_key_ids: Optional[list[int]] = None,
    ) -> OAuthTokenResponse:
        try:
            token = encode_jwt_token(
                configuration.RSA_KEY_MANIFEST.get("current_kid"),
                configuration.JWT_RSA_PRIVATE_KEY,
                configuration.JWT_ALGORITHM,
                configuration.ACCESS_TOKEN_EXPIRE_MINUTES,
                "",
                internal_client,
                user,
                " ".join(configuration.DEFAULT_INTERNAL_SCOPES),
                role_key_ids=role_key_ids,
            )
            OAuth2ClientService.dynamic_save_token_internal_client_sync(
                db_session,
                token=token,
                client_id=internal_client.client_id,
                system_user_id=user.id,
                delete_existing_token=True,
            )
            return OAuthTokenResponse(
                access_token=token.get("access_token"),
                refresh_token=token.get("refresh_token"),
                token_type=token.get("token_type"),
                expires_in=token.get("expires_in"),
                tenant_uuid=None,
            )

        except CustomValueError as e:
            log.error(
                f"❌ Failed to create token for system user: {user.username}, error: {str(e)}"
            )
            raise e

        except Exception as e:
            log.error(f"❌ Database error while creating token: {str(e)}")
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            )

    def _build_otp_data(
        self,
        system_user,
        current_time: datetime,
        redis,
        otp_key: str,
    ) -> dict:
        with CentralDatabase.get_sync_db_session_instance() as central_db_session:
            with central_db_session.begin():
                otp = "".join(random.choices(string.digits, k=OTP_LENGTH))
                key_ttl = (OTP_SEND_WINDOW_MINUTES * 60) * OTP_MAX_SEND_ATTEMPTS

                otp_data = redis.get(otp_key)
                otp_data = (
                    json.loads(otp_data)
                    if otp_data
                    else {"otp": otp, "current_attempt": 0}
                )

                new_attempt = otp_data["current_attempt"] + 1
                latest_send = (
                    datetime.fromisoformat(otp_data["latest_send"])
                    if otp_data.get("latest_send")
                    else None
                )

                if latest_send and current_time - latest_send < timedelta(
                    minutes=OTP_SEND_WINDOW_MINUTES
                ):
                    raise CustomValueError(
                        message=CustomMessageCode.OTP_STILL_VALID.title,
                        message_code=CustomMessageCode.OTP_STILL_VALID.code,
                    )

                if new_attempt > OTP_MAX_SEND_ATTEMPTS:
                    system_user.otp_lock_expires_at = current_time + timedelta(
                        minutes=OTP_BLOCK_TIME_MINUTES
                    )
                    central_db_session.add(system_user)
                    raise CustomValueError(
                        message_code=CustomMessageCode.OTP_TEMPORARILY_LOCKED.code,
                        message=CustomMessageCode.OTP_TEMPORARILY_LOCKED.title,
                    )

                return {
                    "otp": otp,
                    "current_attempt": new_attempt,
                    "key_ttl": key_ttl,
                }

    @audit_login
    def login_system_user_with_username_password(
        self, obj: SystemUserLoginRequest, login_audit_context: LoginAuditContext
    ) -> OAuthTokenResponse:
        try:
            login_audit_context.client_id = obj.client_id
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    client = self._get_internal_oauth_client(
                        central_db_session, obj.client_id
                    )

                    system_user = self._verify_system_user_credentials(
                        central_db_session,
                        username=obj.username,
                        plain_password=obj.password,
                    )
                    login_audit_context.system_user_id = system_user.id

                    return self._create_access_token(
                        central_db_session,
                        internal_client=client,
                        user=system_user,
                    )

        except CustomValueError as e:
            log.error(
                f"❌ Failed to authenticate system_user: {obj.username}, error: {str(e)}"
            )
            raise e

        except Exception as e:
            log.error(
                f"❌ Failed to authenticate system_user: {obj.username}, error: {str(e)}"
            )
            raise e

    # endregion
