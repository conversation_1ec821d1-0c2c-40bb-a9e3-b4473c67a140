from datetime import datetime, timezone
from typing import Optional

from db.db_connection import CentralDatabase

from gc_dentist_shared.central_models import LoginAudit
from gc_dentist_shared.core.logger.config import log


class LoginAuditService:
    @classmethod
    def create_login_audit(
        cls,
        ip_address: str,
        user_agent: str,
        status: int,
        client_id: Optional[str] = None,
        tenant_uuid: Optional[str] = None,
        clinic_doctor_id: Optional[int] = None,
        system_user_id: Optional[int] = None,
        clinic_patient_id: Optional[int] = None,
        global_user_id: Optional[int] = None,
        failure_reason: Optional[str] = None,
    ):
        try:
            with CentralDatabase.get_sync_db_session_instance() as central_db_session:
                with central_db_session.begin():
                    login_audit = LoginAudit(
                        client_id=client_id,
                        tenant_uuid=tenant_uuid,
                        clinic_doctor_id=clinic_doctor_id,
                        system_user_id=system_user_id,
                        clinic_patient_id=clinic_patient_id,
                        global_user_id=global_user_id,
                        ip_address=ip_address,
                        user_agent=user_agent,
                        status=status,
                        failure_reason=failure_reason,
                        login_at=datetime.now(timezone.utc),
                    )
                    central_db_session.add(login_audit)

        except Exception as e:
            log.error(f"❌ create login audit error: {str(e)}")
