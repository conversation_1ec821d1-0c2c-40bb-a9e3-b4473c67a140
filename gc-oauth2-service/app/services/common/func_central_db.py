from core.messages import CustomMessageCode
from sqlalchemy import select, text
from sqlalchemy.exc import DB<PERSON>IError, OperationalError
from sqlalchemy.orm import Session

from gc_dentist_shared.central_models import TenantClinic
from gc_dentist_shared.core.common.utils import is_valid_uuid
from gc_dentist_shared.core.decorators.retry import sync_retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log


@sync_retry_on_failure(
    exceptions=(DBAPIError, OperationalError),
    log_prefix="get_db_name_by_tenant_uuid",
)
def get_db_name_by_tenant_uuid(central_db_session: Session, tenant_uuid: str) -> str:
    try:
        if not tenant_uuid or not is_valid_uuid(tenant_uuid):
            raise CustomValueError(
                message=CustomMessageCode.X_TENANT_UUID_IS_INVALID.title,
                message_code=CustomMessageCode.X_TENANT_UUID_IS_INVALID.code,
            )
        result = central_db_session.execute(
            text("SELECT db_name FROM tenant_clinics WHERE tenant_uuid = :tenant_uuid"),
            {"tenant_uuid": tenant_uuid},
        )

        db_name = result.scalar_one_or_none()
        if not db_name:
            raise CustomValueError(
                message=CustomMessageCode.X_TENANT_NOT_FOUND.title,
                message_code=CustomMessageCode.X_TENANT_NOT_FOUND.code,
            )

        return db_name

    except CustomValueError as e:
        log.error(f"❌ Tenant not found with tenant uuid: {tenant_uuid}")
        raise e
    except (DBAPIError, OperationalError) as e:
        log.error(f"❌ Database error while finding tenant: {str(e)}")
        raise e
    except Exception as e:
        log.error(f"❌ Database error while finding tenant: {str(e)}")
        raise CustomValueError(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
        )


@sync_retry_on_failure(
    exceptions=(DBAPIError, OperationalError),
    log_prefix="get_tenant_clinic_by_clinic_no",
)
def get_tenant_clinic_by_clinic_no(
    central_db_session: Session, clinic_no: str
) -> TenantClinic:
    try:
        result = central_db_session.execute(
            select(TenantClinic).where(TenantClinic.clinic_no == clinic_no)
        )
        tenant = result.scalar_one_or_none()

        if not tenant:
            raise CustomValueError(
                message=CustomMessageCode.X_TENANT_NOT_FOUND.title,
                message_code=CustomMessageCode.X_TENANT_NOT_FOUND.code,
            )

        return tenant

    except CustomValueError as e:
        log.error(f"❌ Tenant not found with clinic no: {clinic_no}")
        raise e

    except Exception as e:
        log.error(f"❌ Database error while finding tenant: {str(e)}")
        raise CustomValueError(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
        )
