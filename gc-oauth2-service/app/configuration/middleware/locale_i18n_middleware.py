import logging
import re
from pathlib import Path

from configuration.context.language_context import set_current_language
from flask import request

from gc_dentist_shared.core.common.i18n import i18n
from gc_dentist_shared.core.constants import MAPPING_LANG_REGION
from gc_dentist_shared.core.logger.config import log

logger = logging.getLogger(__name__)


class LanguageMiddleware:
    def __init__(self, app):
        self.app = app
        self.register()

    def register(self):
        self.app.before_request(self.before_request)

    @staticmethod
    def get_language(lang_code: str) -> str:
        if not lang_code:
            return "en_US"

        languages = re.findall(
            r"([a-z]{2}-[A-Z]{2}|[a-z]{2})(;q=\d.\d{1,3})?", lang_code
        )
        languages = sorted(
            languages, key=lambda x: x[1], reverse=True
        )  # sort by priority, no priority comes last

        translation_directory = Path("locale")
        if not translation_directory.exists():
            return "en_US"

        translation_files = [i.name for i in translation_directory.iterdir()]
        explicit_priority = None

        for lang in languages:
            lang_code = MAPPING_LANG_REGION.get(lang[0][:2]) or "en-US"
            lang_folder = lang_code.replace("-", "_")
            if lang_folder in translation_files:
                if not lang[
                    1
                ]:  # languages without quality value have highest priority 1
                    return lang_folder
                elif (
                    not explicit_priority
                ):  # set language with explicit priority <= priority 1
                    explicit_priority = lang_folder

        # Return language with explicit priority or default value
        return explicit_priority if explicit_priority else "en_US"

    def before_request(self):
        try:
            lang_code: str | None = request.accept_languages.best_match(
                MAPPING_LANG_REGION.keys()
            )
            set_current_language(lang_code)

            language = self.get_language(lang_code)
            i18n.set_language(language=language)

        except Exception as exc:
            log.error(f"Unhandled exception in language middleware: {exc}")
            import traceback

            log.error(traceback.format_exc())
