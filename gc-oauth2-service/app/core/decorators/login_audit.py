from functools import wraps

from dataclass.login_audit_context import LoginAuditContext
from enums.login_audit_enum import LoginAuditEnum
from flask import request
from services.login_audit_service import LoginAuditService

from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError


def audit_login(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        login_audit_context = LoginAuditContext(
            ip_address=request.remote_addr,
            user_agent=request.headers.get("User-Agent", ""),
            status=LoginAuditEnum.SUCCESS.value,
        )
        login_audit_service = LoginAuditService()

        try:
            response = f(*args, login_audit_context=login_audit_context, **kwargs)
            return response

        except CustomValueError as e:
            login_audit_context.failure_reason = e.message
            login_audit_context.status = LoginAuditEnum.FAILED.value
            raise e
        except Exception as e:
            login_audit_context.failure_reason = str(e)
            login_audit_context.status = LoginAuditEnum.FAILED.value
            raise e
        finally:
            login_audit_service.create_login_audit(**login_audit_context.to_dict())

    return decorated_function
