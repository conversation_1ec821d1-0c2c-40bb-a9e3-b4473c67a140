TOKEN_PREFIX = "Bearer"
AUTHORIZATION = "Authorization"
GRANT_TYPE_REFRESH_TOKEN = "refresh_token"

# OTP Configuration Parameters
OTP_LENGTH: int = 6
OTP_SEND_WINDOW_MINUTES: int = 15  # Must be equal to redis TTL
OTP_BLOCK_TIME_MINUTES: int = 30
OTP_MAX_SEND_ATTEMPTS: int = 5

DOCTOR_LOGIN_REDIS_PREFIX = "oauth_doctor_login"
DOCTOR_FORGOT_PASSWORD_REDIS_PREFIX = (
    "oauth_doctor_forgot_password"  # pragma: allowlist secret
)
SYSTEM_USER_LOGIN_REDIS_PREFIX = "oauth_system_user_login"
