[project]
name = "gc-oauth2-service"
version = "0.1.0"
description = ""
authors = [
    {name="Your Name", email="<EMAIL>"}
]
requires-python = ">=3.12,<4.0"
dependencies = [
    "authlib (>=1.6.1,<2.0.0)",
    "sqlalchemy (>=2.0.43,<3.0.0)",
    "asyncpg (>=0.30.0,<0.31.0)",
    "psycopg2-binary (>=2.9.10,<3.0.0)",
    "flask (>=3.1.1,<4.0.0)",
    "flask-pydantic-spec (>=0.8.6,<0.9.0)",
    "jinja2 (>=3.1.6,<4.0.0)",
    "python-dotenv (>=1.1.1,<2.0.0)",
    "hypercorn (>=0.17.3,<0.18.0)",
    "flask (>=3.1.1,<4.0.0)",
    "pydantic-settings (>=2.10.1,<3.0.0)",
    "pydantic (>=2.11.7,<3.0.0)",
    "flask-cors (>=6.0.1,<7.0.0)",
    "botocore (>=1.40.11,<2.0.0)",
    "boto3 (>=1.40.11,<2.0.0)",
    "email-validator (>=2.2.0,<3.0.0)",
    "redis (>=6.4.0,<7.0.0)",
    "phonenumbers (>=9.0.12,<10.0.0)",
    "twilio (>=9.7.1,<10.0.0)",
    "pytest (>=8.4.1,<9.0.0)",
    "flasgger (>=*******,<********)",
    "sqlparse (>=0.5.3,<0.6.0)",
    "pydantic-extra-types (>=2.10.5,<3.0.0)",
    "babel (>=2.17.0,<3.0.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"


[tool.poetry]
package-mode = false