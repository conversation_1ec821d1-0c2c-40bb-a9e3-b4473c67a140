from contextlib import asynccontextmanager

from api.routes import router
from configuration.middleware.identify_tenant_middleware import IdentifyTenantMiddleware
from configuration.middleware.jwt_auth_middleware import JWTAuthMiddleware
from configuration.settings import Settings, configuration
from core.dependencies.get_tenant_uuid_dependency import get_tenant_uuid_for_swagger
from db.db_connection import CentralDatabase
from fastapi import Depends, FastAPI
from sqlalchemy import text
from starlette.middleware.authentication import AuthenticationMiddleware
from starlette_context.middleware import RawContextMiddleware

from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.logger.config import log


def create_app(skip_auth: bool = False) -> FastAPI:
    @asynccontextmanager
    async def lifespan(app: FastAPI):
        # startup logic
        await load_aes_key()
        session = CentralDatabase.get_sessionmaker()
        try:
            async with session() as conn:
                await conn.execute(text("SELECT 1"))
            log.info("✅ Database connected successfully")
        except Exception as e:
            log.error(f"❌ Database connection failed: {e}")
            raise e
        yield

    app = FastAPI(
        lifespan=lifespan,
        dependencies=[Depends(get_tenant_uuid_for_swagger)],
    )
    app.include_router(router)

    register_middlewares(app)

    return app


def register_middlewares(app: FastAPI):
    env = Settings().ENVIRONMENT
    if env in ["production", "staging"]:
        from configuration.middleware.opentelemetry_log_middleware import (
            OpenTelemetryLogMiddleware,
        )

        app.add_middleware(OpenTelemetryLogMiddleware)

    app.add_middleware(IdentifyTenantMiddleware)
    app.add_middleware(
        AuthenticationMiddleware,
        backend=JWTAuthMiddleware(),
        on_error=JWTAuthMiddleware.auth_exception_handler,
    )
    app.add_middleware(RawContextMiddleware)


async def load_aes_key():
    env = Settings().ENVIRONMENT
    if env in ["production", "staging"]:
        (
            aws_secret_rotation_key_mapping,
            current_version_id,
        ) = await AesGCMRotation(
            configuration
        ).get_key_from_aws_secrets_manager(Settings().AES_SECRET_ID_ROTATION)

        if aws_secret_rotation_key_mapping:
            Settings().AWS_SECRET_ROTATION_KEY_MAPPING = aws_secret_rotation_key_mapping

    if not Settings().AWS_SECRET_CURRENT_VERSION:
        log.error("❌ AES ENV secret get current version is None")
        return

    if not Settings().AWS_SECRET_ROTATION_KEY_MAPPING:
        log.error("❌ AES ENV secret key is None")
        return


app = create_app(skip_auth=True)
