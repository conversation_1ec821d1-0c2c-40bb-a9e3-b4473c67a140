version: '3.8'

services:
  gc-admin-app-service:
    container_name: gc-admin-service
    build:
      context: .
      dockerfile: ./gc-admin-app-service/Dockerfile
      tags:
        - gc-admin-app-service:latest
    command: bash -c "alembic -c alembic_admin.ini upgrade head && uvicorn main:app --host 0.0.0.0 --port 8000"
    volumes:
      - ./gc-admin-app-service/app/:/app/
    ports:
      - "8001:8000"
    depends_on:
    - postgres
    - redis
  gc-dentist-app-service:
    container_name: gc-dentist-service
    build:
      context: .
      dockerfile: ./gc-dentist-app-service/Dockerfile
      tags:
        - gc-dentist-app-service:latest
    command: bash -c "uvicorn main:app --host 0.0.0.0 --port 8000"
    volumes:
      - ./gc-dentist-app-service/app/:/app/
    ports:
      - "8002:8000"
    depends_on:
    - postgres
    - redis
  gc-oauth-app-service:
    container_name: gc-oauth2-service
    build:
      context: .
      dockerfile: ./gc-oauth2-service/Dockerfile
      tags: 
        - gc-oauth2-service:latest
    command:  bash -c "flask run"
    volumes:
      - ./gc-oauth2-service/app/:/app/
    ports:
      - "8003:5000"
    depends_on:
    - postgres
    - redis
  gc-mobile-api-service:
    container_name: gc-moblie-api-service
    build:
      context: .
      dockerfile: ./gc-mobile-api-service/Dockerfile
      tags: 
        - gc-mobile-api-service:latest
    command:  bash -c "uvicorn main:app --host 0.0.0.0 --port 8000"
    volumes:
      - ./gc-mobile-api-service/app/:/app/
    ports:
      - "8004:8000"
    depends_on:
    - postgres
    - redis
  gc-data-communication-service:
    container_name: gc-data-communication-service
    build:
      context: .
      dockerfile: ./gc-data-communication-service/Dockerfile
      tags: 
        - gc-data-communication-service:latest
    command:   bash -c "uvicorn main:app --host 0.0.0.0 --port 8000"
    volumes:
      - ./gc-data-communication-service/app/:/app/
    ports:
      - "8005:8000"
    depends_on:
    - postgres
    - redis

  ### Database Services ###
  postgres:
    image: postgres:15
    restart: unless-stopped
    ports:
      - '${POSTGRES_PORT:-5432}:5432'
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-noda}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-noda123}
      POSTGRES_DB: ${POSTGRES_GLOBAL_DB_NAME:-central_local}
    volumes:
      - db-data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-noda} -d ${POSTGRES_GLOBAL_DB_NAME:-central_local}"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    ports:
      - '${REDIS_PORT:-6379}:6379'
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-""}
    command: >
      sh -c "
        if [ -n '${REDIS_PASSWORD:-}' ]; then
          redis-server --requirepass ${REDIS_PASSWORD}
        else
          redis-server
        fi
      "
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  db-data: