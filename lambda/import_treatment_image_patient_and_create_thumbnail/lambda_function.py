import json
import logging
import os
import sys
from datetime import datetime
from urllib.parse import urljoin

import urllib3

logger = logging.getLogger()
logger.setLevel("INFO")
if not logger.handlers:
    handler = logging.StreamHandler(stream=sys.stdout)
    formatter = logging.Formatter(
        "%(asctime)s %(levelname)s [%(name)s] %(message)s", "%Y-%m-%d %H:%M:%S"
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)


X_API_KEY_IMPORT_TREATMENT_IMAGE = os.getenv(
    "X_API_KEY_IMPORT_TREATMENT_IMAGE", ""
).strip()
X_REQUEST_VALUE_IMPORT_TREATMENT_IMAGE = os.getenv(
    "X_REQUEST_VALUE_IMPORT_TREATMENT_IMAGE", "lambda-import-treatment-image-patient"
).strip()

X_API_KEY_CREATE_THUMBNAIL = os.getenv("X_API_KEY_CREATE_THUMBNAIL", "").strip()
X_REQUEST_VALUE_CREATE_THUMBNAIL = os.getenv(
    "X_REQUEST_VALUE_CREATE_THUMBNAIL", "lambda-create-thumbnail"
).strip()


SERVICE_BASE_URL = os.getenv("SERVICE_BASE_URL", "").strip()
END_POINT_REQUEST_IMPORT_TREATMENT_IMAGE = os.getenv(
    "END_POINT_REQUEST_IMPORT_TREATMENT_IMAGE", "/v1_0/patients/treatment-images"
)
END_POINT_REQUEST_CREATE_THUMBNAIL = os.getenv(
    "END_POINT_REQUEST_CREATE_THUMBNAIL", "/v1_0/thumbnails"
)

REQUEST_TIME_OUT = int(os.getenv("REQUEST_TIME_OUT", 30))

CREATE_THUMBNAIL_WIDTH = os.getenv("CREATE_THUMBNAIL_WIDTH", 256)
CREATE_THUMBNAIL_HEIGHT = os.getenv("CREATE_THUMBNAIL_HEIGHT", 360)
CREATE_THUMBNAIL_QUALITY = os.getenv("CREATE_THUMBNAIL_QUALITY", 75)


def lambda_handler(event, context):
    try:
        s3_file_path = event["Records"][0]["s3"]["object"]["key"]
        logger.info(f"Received event for S3 object: {s3_file_path}")
        http_pool = urllib3.PoolManager()

        call_api_import_treatment_image(http_pool, s3_file_path)

        call_api_create_thumbnail(http_pool, s3_file_path)

    except urllib3.exceptions.TimeoutError as e:
        error_msg = f"❌ Lambda Request Import Treatment Image And Create Thumbnail The request timed out: {str(e)}"
        logger.error(error_msg)
    except urllib3.exceptions.RequestError as e:
        error_msg = f"❌ Lambda Request Import Treatment Image And Create Thumbnail An error occurred Error: {str(e)}"
        logger.error(error_msg)
    except Exception as e:
        error_msg = f"❌ Lambda Request Import Treatment Image And Create Thumbnail Error: {str(e)}"
        logger.error(error_msg)


def call_api_import_treatment_image(http_pool, s3_file_path):

    now = datetime.now()
    logger.info(f"Lambda Request Import Treatment Image: NOW(UTC) = {str(now)}")

    url_import_treatment_image = urljoin(
        SERVICE_BASE_URL, END_POINT_REQUEST_IMPORT_TREATMENT_IMAGE
    ).rstrip("/")
    headers_import_treatment_image = {
        "Content-Type": "application/json",
        "X-Api-Key": X_API_KEY_IMPORT_TREATMENT_IMAGE,
        "X-Request-Value": X_REQUEST_VALUE_IMPORT_TREATMENT_IMAGE,
    }
    payload = {"image_file_path": s3_file_path}

    response = http_pool.request(
        "POST",
        url_import_treatment_image,
        body=json.dumps(payload),
        headers=headers_import_treatment_image,
        timeout=REQUEST_TIME_OUT,
    )
    logger.info(
        f"✅ Lambda Request Import Treatment Image Success: {response.data.decode('utf-8')}"
    )


def call_api_create_thumbnail(http_pool, s3_file_path):

    now = datetime.now()
    logger.info(f"Lambda Request Create Thumbnail: NOW(UTC) = {str(now)}")

    url_create_thumbnail = urljoin(
        SERVICE_BASE_URL, END_POINT_REQUEST_CREATE_THUMBNAIL
    ).rstrip("/")
    headers_create_thumbnail = {
        "Content-Type": "application/json",
        "X-Api-Key": X_API_KEY_CREATE_THUMBNAIL,
        "X-Request-Value": X_REQUEST_VALUE_CREATE_THUMBNAIL,
    }
    payload = {
        "original_image_path": s3_file_path,
        "thumbnail_width": int(CREATE_THUMBNAIL_WIDTH),
        "thumbnail_height": int(CREATE_THUMBNAIL_HEIGHT),
        "quality": int(CREATE_THUMBNAIL_QUALITY),
    }

    response = http_pool.request(
        "POST",
        url_create_thumbnail,
        body=json.dumps(payload),
        headers=headers_create_thumbnail,
        timeout=REQUEST_TIME_OUT,
    )
    logger.info(
        f"✅ Lambda Request Create thumbnail Success: {response.data.decode('utf-8')}"
    )
