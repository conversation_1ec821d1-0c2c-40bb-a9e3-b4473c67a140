import logging
import os
from datetime import datetime, timezone
from urllib.parse import urljoin

import urllib3

logger = logging.getLogger()
logger.setLevel("INFO")

X_API_KEY = os.getenv("X_API_KEY", "").strip()
X_REQUEST_VALUE = os.getenv("X_REQUEST_VALUE", "").strip()

SERVICE_BASE_URL = os.getenv("SERVICE_BASE_URL", "").strip()
END_POINT_REQUEST = os.getenv("END_POINT_REQUEST", "/v1_0/storage-usage/tenants/sync")
REQUEST_TIME_OUT = int(os.getenv("REQUEST_TIME_OUT", 30))


def lambda_handler(event, context):
    url = urljoin(SERVICE_BASE_URL, END_POINT_REQUEST).rstrip("/")

    headers = {
        "Content-Type": "application/json",
        "X-Api-Key": X_API_KEY,
        "X-Request-Value": X_REQUEST_VALUE,
    }

    now = datetime.now(timezone.utc)
    logger.info(f"Lambda Request Sync Usage Storage: NOW(UTC) = {str(now)}")

    try:
        http = urllib3.PoolManager()
        response = http.request("POST", url, headers=headers, timeout=REQUEST_TIME_OUT)
        logger.info(
            f"✅ Lambda Request Sync Usage Storage Success: {response.data.decode('utf-8')}"
        )
    except urllib3.exceptions.TimeoutError as e:
        error_msg = (
            f"❌ Lambda Request Sync Usage Storage The request timed out: {str(e)}"
        )
        logger.error(error_msg)
    except urllib3.exceptions.RequestError as e:
        error_msg = (
            f"❌ Lambda Request Sync Usage Storage An error occurred Error: {str(e)}"
        )
        logger.error(error_msg)
    except Exception as e:
        error_msg = f"❌ Lambda Request Sync Usage Storage Error: {str(e)}"
        logger.error(error_msg)
