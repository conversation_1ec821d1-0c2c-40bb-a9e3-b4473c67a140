class MockS3Client:
    def __init__(self, *args, **kwargs):
        self.restore_requests = []

    @classmethod
    async def get_instance(cls, configuration):
        instance = cls()
        instance.S3_GLACIER_TRANSITION_DAYS = configuration.S3_GLACIER_TRANSITION_DAYS
        instance.S3_TEMP_FILE_TTL = configuration.S3_TEMP_FILE_TTL
        return instance

    async def restore_object(self, key: str, days: int, tier: str):
        # Record the restore request
        self.restore_requests.append(
            {
                "key": key,
                "days": days,
                "tier": tier,
            }
        )
