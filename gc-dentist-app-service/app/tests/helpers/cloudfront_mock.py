import contextlib
import time
from unittest.mock import patch

import pytest
from configuration.settings import configuration
from cryptography.hazmat.primitives.asymmetric import rsa

from gc_dentist_shared.core.common.cloudfront import CloudFront


@pytest.fixture
def mock_cloudfront_config_factory():
    def _mock(
        url="https://mock.cloudfront.net",
        expire_minutes=10,
        public_key_id=None,
        private_key=None,
    ):
        public_key_id = str(public_key_id or time.time())
        private_key = private_key or generate_rsa_private_key_pem()

        instance_patch = patch.object(CloudFront, "_instance", None)

        context = patch.multiple(
            configuration,
            CLOUDFRONT_URL=url,
            CLOUDFRONT_PRIVATE_KEY=private_key,
            CLOUDFRONT_PUBLIC_KEY_ID=public_key_id,
            CLOUDFRONT_SIGNED_URL_EXPIRE_MINUTES=expire_minutes,
        )

        @contextlib.contextmanager
        def _context():
            with context, instance_patch:
                yield

        return _context()

    return _mock


def generate_rsa_private_key_pem(key_size: int = 2048) -> str:
    return rsa.generate_private_key(public_exponent=65537, key_size=key_size)
