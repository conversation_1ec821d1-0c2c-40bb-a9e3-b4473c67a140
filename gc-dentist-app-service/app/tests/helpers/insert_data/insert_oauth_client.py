import time
import uuid

from sqlalchemy.ext.asyncio import AsyncSession
from werkzeug.security import gen_salt

from gc_dentist_shared.central_models import OAuth2Client


async def unittest_insert_oauth_client(
    async_central_db_session_object: AsyncSession,
):
    client_name: str = f"Noda-Client-{(str(uuid.uuid4()))}"
    client_uri: str = "https://example.com"
    grant_types: list = ["refresh_token", "authorization_code"]
    redirect_uris: list = ["https://example.com/callback"]
    response_types: list = ["code"]
    scope: str = "profile+mail"
    token_endpoint_auth_method: str = "client_secret_post"

    client_metadata = {
        "client_name": client_name,
        "client_uri": client_uri,
        "grant_types": grant_types,
        "redirect_uris": redirect_uris,
        "response_types": response_types,
        "scope": scope,
        "token_endpoint_auth_method": token_endpoint_auth_method,
    }

    client_secret = "" if token_endpoint_auth_method == "none" else gen_salt(48)
    client_id = gen_salt(24)
    client_id_issued_at = int(time.time())

    internal_oauth_client = OAuth2Client(
        name=client_name,
        client_id=client_id,
        client_secret=client_secret,
        client_id_issued_at=client_id_issued_at,
        is_active=True,
    )
    internal_oauth_client.set_client_metadata(client_metadata)

    async_central_db_session_object.add(internal_oauth_client)
    await async_central_db_session_object.flush()
    await async_central_db_session_object.refresh(internal_oauth_client)

    return internal_oauth_client
