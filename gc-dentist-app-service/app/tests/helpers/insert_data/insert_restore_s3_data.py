import uuid
from datetime import datetime, timezone
from typing import Optional

from sqlalchemy import delete
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.enums.s3_enums import S3RestoreStatus
from gc_dentist_shared.tenant_models import RestoreS3Data


async def unittest_insert_restore_s3_data(
    db_session: AsyncSession,
    custom_fields: Optional[dict] = None,
) -> int:
    data = {
        "document_uuid": str(uuid.uuid4()),
        "version_id": 1,
        "s3_url": f"main/{str(uuid.uuid4())}/document-1.json",
        "status": S3RestoreStatus.RESTORING.value,
        "requested_at": datetime.now(timezone.utc),
    }

    # Override data with custom fields if provided
    if custom_fields:
        data.update(custom_fields)

    restore_data = RestoreS3Data(**data)
    db_session.add(restore_data)
    await db_session.flush()
    await db_session.refresh(restore_data)

    return restore_data.id


async def unittest_remove_restore_s3_data(
    db_session: AsyncSession, restore_data_ids: Optional[list[int]] = None
):
    if not restore_data_ids:
        return

    stmt = delete(RestoreS3Data).where(RestoreS3Data.id.in_(restore_data_ids))
    await db_session.execute(stmt)
    await db_session.flush()
