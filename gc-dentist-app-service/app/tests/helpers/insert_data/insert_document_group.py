import uuid

from sqlalchemy import delete

from gc_dentist_shared.tenant_models import DocumentGroup


async def unittest_insert_document_group(session, **kwargs):
    """Helper function to insert a document group for testing"""
    name = kwargs.get("name", f"Document Group {uuid.uuid4().hex[:8]}")
    key_name = kwargs.get("key_name", name.lower().replace(" ", "_"))
    is_parent = kwargs.get("is_parent", False)
    group_parent_id = kwargs.get("group_parent_id", None)

    document_group = DocumentGroup(
        name=name,
        key_name=key_name,
        is_parent=is_parent,
        group_parent_id=group_parent_id,
    )

    session.add(document_group)
    await session.flush()
    await session.refresh(document_group)

    return document_group


async def unittest_delete_document_groups(session, document_group_ids):
    """Helper function to delete document groups by IDs"""
    if not document_group_ids:
        return

    stmt = delete(DocumentGroup).where(DocumentGroup.id.in_(document_group_ids))
    await session.execute(stmt)
    await session.flush()
