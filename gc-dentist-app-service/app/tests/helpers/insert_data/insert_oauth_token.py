import time
from types import SimpleNamespace
from uuid import uuid4

from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import OAuth2Client, OAuth2Token
from gc_dentist_shared.core.common.jwt_token import encode_jwt_token


def unittest_create_jwt_token(
    internal_client: OAuth2Client,
    user_id: int,
    tenant_uuid: str,
    role_key_ids: list[int],
    token_expire: int,
    rsa_key_pair: dict,
    jwt_algorithm: str = "RS256",
):
    user = SimpleNamespace(id=user_id, tenant_uuid=tenant_uuid)
    data_token = encode_jwt_token(
        key_id=rsa_key_pair["key_id"],
        jwt_secret_key=rsa_key_pair["private_key"],
        jwt_algorithm=jwt_algorithm,
        access_token_expire_minutes=token_expire,
        grant_type="",
        client=internal_client,
        user=user,
        scope=" ".join(
            [
                "profile",
                "email",
            ]
        ),
        role_key_ids=role_key_ids,
    )
    return {
        "access_token": data_token.get("access_token"),
        "refresh_token": data_token.get("refresh_token"),
        "token_type": data_token.get("token_type"),
        "expires_in": data_token.get("expires_in"),
    }


async def unittest_insert_oauth_token(
    async_central_db_session_object: AsyncSession,
    internal_client: OAuth2Client,
    rsa_key_pair: dict,
    doctor_user_id: int,
    tenant_uuid: str,
    role_key_ids: list[int],
    token_expire: int = 15,
):
    jwt_token = unittest_create_jwt_token(
        user_id=doctor_user_id,
        tenant_uuid=tenant_uuid,
        role_key_ids=role_key_ids,
        internal_client=internal_client,
        token_expire=token_expire,
        rsa_key_pair=rsa_key_pair,
    )

    oauth_token = OAuth2Token(
        token_uuid=uuid4(),
        client_id=internal_client.client_id,
        token_type=jwt_token["token_type"],
        access_token=jwt_token["access_token"],
        refresh_token=jwt_token["refresh_token"],
        expires_in=jwt_token["expires_in"],
        scope="profile+mail",
        issued_at=int(time.time()),
        refresh_token_expires_at=int(time.time()) + (3600 * 24 * 30),
        access_token_revoked_at=0,
        refresh_token_revoked_at=0,
        clinic_doctor_id=doctor_user_id,
        tenant_uuid=tenant_uuid,
    )

    async_central_db_session_object.add(oauth_token)
    await async_central_db_session_object.flush()
    await async_central_db_session_object.refresh(oauth_token)

    token_data = {
        "token_type": oauth_token.token_type,
        "access_token": oauth_token.access_token,
        "refresh_token": oauth_token.refresh_token,
        "expires_in": oauth_token.expires_in,
    }

    return token_data
