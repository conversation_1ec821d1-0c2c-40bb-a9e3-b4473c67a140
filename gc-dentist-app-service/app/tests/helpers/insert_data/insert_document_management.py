import uuid
from datetime import date

from gc_dentist_shared.core.enums.document import (
    DocumentDataType,
    DocumentDisplayMode,
    DocumentExtension,
    DocumentStatus,
)
from gc_dentist_shared.tenant_models import DocumentGroup, DocumentManagement


async def unittest_insert_document(
    db_session, patient_user_id, custom_group_fields=None, custom_document_fields=None
):
    group_data = {
        "name": str(uuid.uuid4()),
    }

    if custom_group_fields:
        group_data.update(custom_group_fields)

    group_model = DocumentGroup(**group_data)
    db_session.add(group_model)
    await db_session.flush()
    await db_session.refresh(group_model)

    document_data = {
        "document_group_id": group_model.id,
        "patient_user_id": patient_user_id,
        "name": str(uuid.uuid4()),
        "status": DocumentStatus.ACTIVATED.value,
        "data_type": DocumentDataType.ORIGINAL.value,
        "document_data": {"file_key_1": "main/documents/image.jpg"},
        "preview_document_data": {"file_key_1": "sub/documents/image.jpg"},
        "examination_date": date.today(),
        "medical_history_id": None,
        "display_mode": DocumentDisplayMode.LIST_IMAGE.value,
        "document_uuid": str(uuid.uuid4()),
        "document_extension": DocumentExtension.IMAGE.value,
        "version_id": 1,
    }
    if custom_document_fields:
        document_data.update(custom_document_fields)

    document_model = DocumentManagement(**document_data)
    db_session.add(document_model)
    await db_session.flush()
    await db_session.refresh(document_model)

    document_data["document_id"] = document_model.id

    return document_data
