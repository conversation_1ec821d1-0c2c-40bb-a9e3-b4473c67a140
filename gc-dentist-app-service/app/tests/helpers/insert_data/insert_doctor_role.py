from gc_dentist_shared.tenant_models import Doctor<PERSON><PERSON>


async def unittest_insert_doctor_role(
    db_session, doctor_user_id: int, role_key_id: int, delete_flag: bool = False
):
    new_doctor_role = Doctor<PERSON><PERSON>(
        doctor_user_id=doctor_user_id, role_key_id=role_key_id, delete_flag=delete_flag
    )
    db_session.add(new_doctor_role)
    await db_session.flush()
    await db_session.refresh(new_doctor_role)
