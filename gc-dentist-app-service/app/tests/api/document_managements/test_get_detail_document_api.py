import time
from unittest.mock import patch
from urllib.parse import parse_qs, urlparse

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from fastapi import status
from tests.helpers.cloudfront_mock import mock_cloudfront_config_factory  # noqa: F401
from tests.helpers.insert_data.insert_document_management import (
    unittest_insert_document,
)
from tests.helpers.insert_data.insert_patient import unittest_insert_patient

from gc_dentist_shared.core.enums.document import DocumentDataType


@pytest.fixture(scope="module")
def _headers(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }


@pytest_asyncio.fixture(scope="class")
async def setup_data(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)
        document = await unittest_insert_document(
            async_tenant_db_session_object, patient_user_id=patient_user_id
        )

        return {"patient_user_id": patient_user_id, "document": document}


@pytest.mark.asyncio
async def test_get_document_success(async_client, _headers, setup_data):
    patient_user_id = setup_data.get("patient_user_id")
    document = setup_data.get("document")
    document_id = document.get("document_id")

    response = await async_client.get(
        f"/v1_0/document-managements/{document_id}/patients/{patient_user_id}",
        headers=_headers,
    )
    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True
    data = response_json["data"]

    fields_response = [
        "patient_user_id",
        "name",
        "document_group_id",
        "data_type",
        "document_data",
        "examination_date",
        "medical_history_id",
        "display_mode",
        "s3_status",
        "version_id",
        "document_extension",
    ]
    for field in fields_response:
        assert field in data
        if field in ["document_data", "preview_document_data"]:
            assert isinstance(data[field], dict)


@pytest.mark.asyncio
async def test_get_document_edited_success(
    async_client, async_tenant_db_session_object, _headers, setup_data
):
    patient_user_id = setup_data.get("patient_user_id")

    async with async_tenant_db_session_object.begin():
        document = await unittest_insert_document(
            async_tenant_db_session_object,
            patient_user_id=patient_user_id,
            custom_document_fields={
                "document_data": {"key_json": "main/develop/file.json"},
                "preview_document_data": {"p1": "sub/develop/file.img"},
                "data_type": DocumentDataType.EDITED.value,
            },
        )
        document_id = document.get("document_id")

    response = await async_client.get(
        f"/v1_0/document-managements/{document_id}/patients/{patient_user_id}",
        headers=_headers,
    )
    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True


@pytest.mark.asyncio
async def test_get_document_valid_signed_url(
    async_client, _headers, setup_data, mock_cloudfront_config_factory  # noqa: F811
):
    patient_user_id = setup_data.get("patient_user_id")
    document = setup_data.get("document")
    document_id = document.get("document_id")

    context = mock_cloudfront_config_factory()
    with context:
        response = await async_client.get(
            f"/v1_0/document-managements/{document_id}/patients/{patient_user_id}",
            headers=_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()["data"]

        document_data = data["document_data"].values()
        preview_document_data = data["preview_document_data"].values()
        files = list(document_data) + list(preview_document_data)
        for f in files:
            parsed_url = urlparse(f)
            query_params = parse_qs(parsed_url.query)
            assert "Signature" in query_params


@pytest.mark.asyncio
async def test_get_document_not_found(async_client, _headers, setup_data):
    patient_user_id = setup_data.get("patient_user_id")
    document = setup_data.get("document")
    document_id = document.get("document_id")
    fake_id = int(time.time())

    response = await async_client.get(
        f"/v1_0/document-managements/{fake_id}/patients/{patient_user_id}",
        headers=_headers,
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert (
        response.json()["messageCode"]
        == CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.code
    )

    response = await async_client.get(
        f"/v1_0/document-managements/{document_id}/patients/{fake_id}",
        headers=_headers,
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert (
        response.json()["messageCode"]
        == CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.code
    )


@pytest.mark.asyncio
async def test_get_document_exception_error(async_client, _headers, setup_data):
    """Test general exception handling"""
    # Patch the service to throw a generic Exception
    with patch(
        "services.document_managements.document_management_service.DocumentManagementService.get_document",
        side_effect=Exception("Something went wrong"),
    ):
        patient_user_id = setup_data.get("patient_user_id")
        document = setup_data.get("document")
        document_id = document.get("document_id")

        response = await async_client.get(
            f"/v1_0/document-managements/{document_id}/patients/{patient_user_id}",
            headers=_headers,
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            response.json()["messageCode"]
            == CustomMessageCode.DOCUMENT_GET_INFO_FAILED.code
        )


@pytest.mark.asyncio
async def test_cleaned_up(async_tenant_db_session_object, truncate_table):
    await truncate_table(
        async_tenant_db_session_object,
        [
            "patient_profiles",
            "patient_users",
            "document_group",
            "document_managements",
        ],
    )
