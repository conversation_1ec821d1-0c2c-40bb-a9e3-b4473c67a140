from datetime import datetime, timed<PERSON><PERSON>
from unittest.mock import patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from sqlalchemy.exc import OperationalError
from tests.helpers.header_mock import get_headers

from .setup_data import OralExaminationSetupData


@pytest_asyncio.fixture(scope="class")
async def setup_intraoral_examination_data(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        data = await OralExaminationSetupData.insert_intraoral_examination(
            async_tenant_db_session_object
        )
    return data


def get_valid_payload(overrides=None):
    payload = OralExaminationSetupData.default_data.copy()
    if overrides is not None:
        payload.update(overrides)
    return payload


@pytest.mark.asyncio
async def test_update_intraoral_examination_success(
    async_client, tenant_uuid, setup_intraoral_examination_data
):
    data = setup_intraoral_examination_data
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload(
        {
            "examination_date": datetime.now().astimezone().isoformat(),
        }
    )
    oral_examination_id = data["oral_examination_id"]

    response = await async_client.put(
        f"/v1_0/intraoral-examinations/{oral_examination_id}",
        headers=headers,
        json=payload,
    )
    assert response.status_code == 200
    result = response.json()
    assert result["success"] is True


@pytest.mark.asyncio
async def test_update_intraoral_examination_not_found(async_client, tenant_uuid):
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload()

    response = await async_client.put(
        "/v1_0/intraoral-examinations/999999", headers=headers, json=payload
    )
    assert response.status_code == 400
    result = response.json()
    assert result["success"] is False
    assert result["message"] == CustomMessageCode.ORAL_EXAMINATION_NOT_FOUND.title
    assert result["messageCode"] == CustomMessageCode.ORAL_EXAMINATION_NOT_FOUND.code


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "payload_overrides",
    [
        {
            "examination_date": (datetime.now() - timedelta(days=1))
            .astimezone()
            .isoformat(),
        },
        {
            "examination_date": datetime.now().astimezone().isoformat(),
            "intraoral_examination": {
                "teeth": {"invalid_tooth": {}},
            },
        },
        {
            "examination_date": datetime.now().astimezone().isoformat(),
            "intraoral_examination": {
                "teeth": {
                    "upper_right_1": {
                        "tooth_type": "adult",
                        "note": "string",
                        "treatment": ["WSD"],
                        "whole_tooth": {
                            "inspection": "string",
                        },
                        "points": {
                            "1": {"inspection": "C3"},
                        },
                    },
                }
            },
        },
    ],
)
async def test_update_intraoral_examination_validation_errors(
    async_client, tenant_uuid, setup_intraoral_examination_data, payload_overrides
):
    data = setup_intraoral_examination_data
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload(payload_overrides)
    oral_examination_id = data["oral_examination_id"]

    response = await async_client.put(
        f"/v1_0/intraoral-examinations/{oral_examination_id}",
        headers=headers,
        json=payload,
    )
    assert response.status_code == 422
    result = response.json()
    assert result["success"] is False


@pytest.mark.asyncio
async def test_update_intraoral_examination_database_error(
    async_client, tenant_uuid, setup_intraoral_examination_data
):
    data = setup_intraoral_examination_data
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload()

    with patch(
        "services.oral_examination.intraoral_examination_service."
        "IntraoralExaminationService.update_intraoral_examination",
        side_effect=OperationalError("Database error", {}, "SQL statement"),
    ) as mock_update_intraoral_examination:

        oral_examination_id = data["oral_examination_id"]
        response = await async_client.put(
            f"/v1_0/intraoral-examinations/{oral_examination_id}",
            headers=headers,
            json=payload,
        )
        assert response.status_code == 400
        result = response.json()
        assert result["success"] is False
        assert result["message"] == CustomMessageCode.UNKNOWN_ERROR.title
        assert result["messageCode"] == CustomMessageCode.UNKNOWN_ERROR.code
        assert mock_update_intraoral_examination.call_count == 1
