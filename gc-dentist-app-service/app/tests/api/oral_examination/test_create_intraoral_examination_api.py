from datetime import datetime, timed<PERSON><PERSON>
from unittest.mock import patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from sqlalchemy.exc import OperationalError
from tests.helpers.header_mock import get_headers

from .setup_data import OralExaminationSetupData


@pytest_asyncio.fixture(scope="class")
async def setup_intraoral_examination_data(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        data = [
            await OralExaminationSetupData.setup_data_for_intraoral_examination(
                async_tenant_db_session_object
            )
            for _ in range(4)
        ]
    return data


def get_valid_payload(overrides=None):
    payload = OralExaminationSetupData.default_data.copy()
    if overrides is not None:
        payload.update(overrides)
    return payload


@pytest.mark.asyncio
async def test_create_intraoral_examination_success(
    async_client, tenant_uuid, setup_intraoral_examination_data
):
    data = setup_intraoral_examination_data[0]
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload(
        {
            "patient_user_id": data["patient_user_id"],
            "medical_history_id": data["medical_history_id"],
            "examination_date": datetime.now().astimezone().isoformat(),
        }
    )

    response = await async_client.post(
        "/v1_0/intraoral-examinations", headers=headers, json=payload
    )
    assert response.status_code == 200
    result = response.json()
    assert result["success"] is True


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "payload_overrides, expected_status, expected_success",
    [
        (
            lambda data: {
                "patient_user_id": "invalid_patient_user_id",
                "medical_history_id": data["medical_history_id"],
                "examination_date": datetime.now().astimezone().isoformat(),
            },
            422,
            False,
        ),
        (
            lambda data: {
                "patient_user_id": data["patient_user_id"],
                "medical_history_id": "invalid_medical_history_id",
                "examination_date": datetime.now().astimezone().isoformat(),
            },
            422,
            False,
        ),
        (
            lambda data: {
                "patient_user_id": data["patient_user_id"],
                "medical_history_id": data["medical_history_id"],
                "examination_date": "invalid_date_format",
            },
            422,
            False,
        ),
        (
            lambda data: {
                "patient_user_id": data["patient_user_id"],
                "medical_history_id": data["medical_history_id"],
                "examination_date": (
                    datetime.now().astimezone() - timedelta(days=1)
                ).isoformat(),
            },
            422,
            False,
        ),
        (
            lambda data: {
                "patient_user_id": data["patient_user_id"],
                "medical_history_id": data["medical_history_id"],
                "examination_date": datetime.now().astimezone().isoformat(),
                "intraoral_examination": {
                    "teeth": {"invalid_tooth": {}},
                },
            },
            422,
            False,
        ),
    ],
)
async def test_create_intraoral_examination_invalid_cases(
    async_client,
    tenant_uuid,
    setup_intraoral_examination_data,
    payload_overrides,
    expected_status,
    expected_success,
):
    data = setup_intraoral_examination_data[1]
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload(payload_overrides(data))

    response = await async_client.post(
        "/v1_0/intraoral-examinations", headers=headers, json=payload
    )
    assert response.status_code == expected_status
    result = response.json()
    assert result["success"] is expected_success


@pytest.mark.asyncio
async def test_create_intraoral_examination_notfound_medical_history_id(
    async_client, tenant_uuid, setup_intraoral_examination_data
):
    data = setup_intraoral_examination_data[1]
    data_invalid = setup_intraoral_examination_data[2]
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload(
        {
            "patient_user_id": data["patient_user_id"],
            "medical_history_id": data_invalid["medical_history_id"],
            "examination_date": datetime.now().astimezone().isoformat(),
        }
    )

    response = await async_client.post(
        "/v1_0/intraoral-examinations", headers=headers, json=payload
    )
    assert response.status_code == 400
    result = response.json()
    assert result["success"] is False
    assert result["messageCode"] == CustomMessageCode.MEDICAL_HISTORY_NOT_FOUND.code
    assert result["message"] == CustomMessageCode.MEDICAL_HISTORY_NOT_FOUND.title


@pytest.mark.asyncio
async def test_create_intraoral_examination_retry(
    async_client, tenant_uuid, setup_intraoral_examination_data
):
    data = setup_intraoral_examination_data[3]
    headers = get_headers(tenant_uuid)
    payload = get_valid_payload(
        {
            "patient_user_id": data["patient_user_id"],
            "medical_history_id": data["medical_history_id"],
            "examination_date": datetime.now().astimezone().isoformat(),
        }
    )

    with patch(
        "services.oral_examination.intraoral_examination_service"
        ".IntraoralExaminationService.check_medical_history_exists",
        side_effect=OperationalError("Operational error", None, None),
    ) as mock_get_master_data:

        response = await async_client.post(
            "/v1_0/intraoral-examinations", headers=headers, json=payload
        )
        assert response.status_code == 400
        result = response.json()
        assert result["success"] is False
        assert result["messageCode"] == CustomMessageCode.UNKNOWN_ERROR.code
        assert result["message"] == CustomMessageCode.UNKNOWN_ERROR.title
        assert mock_get_master_data.call_count == 3
