from unittest.mock import patch

import pytest
from core.messages import CustomMessageCode
from sqlalchemy.exc import OperationalError


def get_headers(tenant_uuid, valid_headers=None):
    headers = {"X-Tenant-UUID": tenant_uuid}
    if valid_headers:
        headers.update(valid_headers)
    return headers


def get_master_data_params(**overrides):
    params = {
        "master_names": [
            "m_era",
            "m_oral_examinations",
            "m_prefecture",
        ]
    }
    params.update(overrides)
    return params


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "param_data, headers_func, expected_status",
    [
        (get_master_data_params(), lambda t: get_headers(t), 200),
        ({"master_names": ["m_invalid"]}, lambda t: get_headers(t), 422),
        ({"master_names": []}, lambda t: get_headers(t), 422),
        (
            {"master_names": ["m_era", "m_oral_examinations"]},
            lambda t: get_headers(t),
            200,
        ),
        ({"master_names": ["m_prefecture"]}, lambda t: get_headers(t), 200),
    ],
)
async def test_get_master_data_success_and_validation(
    async_client, tenant_uuid, param_data, headers_func, expected_status
):
    headers = headers_func(tenant_uuid)

    response = await async_client.get(
        "/v1_0/master-data", headers=headers, params=param_data
    )
    assert response.status_code == expected_status


@pytest.mark.asyncio
async def test_get_master_data_exception_on_get_master_data(async_client, tenant_uuid):
    headers = get_headers(tenant_uuid)
    param_data = get_master_data_params()

    with patch(
        "services.master_data_service.MasterDataService.get_master_data",
        side_effect=Exception("Data fetch error"),
    ):

        response = await async_client.get(
            "/v1_0/master-data", headers=headers, params=param_data
        )
        assert response.status_code == 400
        result = response.json()
        assert result["message"] == CustomMessageCode.MASTER_DATA_NOT_FOUND.title
        assert result["messageCode"] == CustomMessageCode.MASTER_DATA_NOT_FOUND.code


@pytest.mark.asyncio
async def test_get_master_data_exception_on_get_master_data_by_model_name(
    async_client, tenant_uuid
):
    headers = get_headers(tenant_uuid)
    param_data = get_master_data_params()

    with patch(
        "services.master_data_service.MasterDataService.get_master_data_by_model_name",
        side_effect=Exception("Data fetch error"),
    ):

        response = await async_client.get(
            "/v1_0/master-data", headers=headers, params=param_data
        )
        assert response.status_code == 400
        result = response.json()
        assert result["message"] == CustomMessageCode.MASTER_DATA_NOT_FOUND.title
        assert result["messageCode"] == CustomMessageCode.MASTER_DATA_NOT_FOUND.code


@pytest.mark.asyncio
async def test_get_master_data_exception_not_found_on_get_master_data_by_model_name(
    async_client, tenant_uuid
):
    headers = get_headers(tenant_uuid)
    param_data = get_master_data_params()

    with patch(
        "services.master_data_service.MasterDataService.get_master_data_by_model_name",
        side_effect=OperationalError("Operational error", None, None),
    ) as mock_get_master_data_by_model_name:

        response = await async_client.get(
            "/v1_0/master-data", headers=headers, params=param_data
        )
        assert response.status_code == 400
        result = response.json()
        assert result["message"] == CustomMessageCode.MASTER_DATA_NOT_FOUND.title
        assert result["messageCode"] == CustomMessageCode.MASTER_DATA_NOT_FOUND.code
        assert mock_get_master_data_by_model_name.call_count == 1
