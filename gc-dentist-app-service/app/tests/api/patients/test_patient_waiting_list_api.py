from datetime import date, datetime
from unittest.mock import patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from fastapi import status
from sqlalchemy import text
from sqlalchemy.exc import OperationalError
from tests.helpers.enums.patient import EmergencyFlag, PatientWaitingStatus
from tests.helpers.insert_data.insert_doctor import unittest_insert_doctor
from tests.helpers.insert_data.insert_patient import unittest_insert_patient
from tests.helpers.insert_data.insert_patient_waitting import (
    unittest_insert_patient_waiting,
)

from gc_dentist_shared.core.logger.config import log


@pytest.fixture(scope="module")
def _headers(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }


@pytest.fixture(scope="module")
def _share_data_saved():
    return {"status_list": []}


def get_valid_payload(overrides=None):
    payload = {
        "patient_user_id": 1,
        "emergency_flag": EmergencyFlag.NORMAL.value,
        "coming_time": date.today(),
        "status": PatientWaitingStatus.SCHEDULED.value,
        "visit_start_date": date.today(),
        "visit_end_date": date.today(),
        "visit_start_time": datetime.now().time(),
        "visit_end_time": None,
        "assigned_doctors": [],
        "assigned_room": None,
        "room_number": 0,
    }
    if overrides is not None:
        payload.update(overrides)
    return payload


@pytest_asyncio.fixture(scope="module")
async def _setup_create_patient_and_doctor(async_tenant_db_session_object):
    patient_profiles = [
        {
            "first_name": "An",
            "last_name": "Nguyen",
            "first_name_kana": "アン",
            "last_name_kana": "グエン",
        },
        {
            "first_name": "Binh",
            "last_name": "Tran",
            "first_name_kana": "ビン",
            "last_name_kana": "チャン",
        },
        {
            "first_name": "abcBinh",
            "last_name": "abcBinh",
            "first_name_kana": "アビンア",
            "last_name_kana": "グエン",
        },
    ]

    doctor_profiles = [
        {
            "first_name": "Minh",
            "last_name": "Le",
            "first_name_kana": "ミン",
            "last_name_kana": "レ",
        },
        {
            "first_name": "Thu",
            "last_name": "Thitoan",
            "first_name_kana": "トゥ",
            "last_name_kana": "チャン",
        },
        {
            "first_name": "MInhThu",
            "last_name": "TranLe",
            "first_name_kana": "トゥン",
            "last_name_kana": "チャレ",
        },
    ]

    patient_list_to_return = []
    doctor_list_to_return = []

    async with async_tenant_db_session_object.begin():
        for profile in patient_profiles:
            patient_data = await unittest_insert_patient(
                async_tenant_db_session_object,
                custom_profile_fields=profile,
            )
            patient_list_to_return.append(patient_data)

        for profile in doctor_profiles:
            doctor_data = await unittest_insert_doctor(
                async_tenant_db_session_object,
                custom_profile_fields=profile,
            )
            doctor_list_to_return.append(doctor_data)

    return {"patients": patient_list_to_return, "doctors": doctor_list_to_return}


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "index,status",
    [
        (0, PatientWaitingStatus.SCHEDULED.value),
        (1, PatientWaitingStatus.SCHEDULED.value),
        (2, PatientWaitingStatus.RESOLVED.value),
    ],
)
@pytest.mark.asyncio
async def test_setup_patient_waiting_db(
    async_tenant_db_session_object,
    _setup_create_patient_and_doctor,
    _share_data_saved,
    index,
    status,
):
    patient_id = _setup_create_patient_and_doctor["patients"][index]
    doctor_id = _setup_create_patient_and_doctor["doctors"][index]

    payload_data = {
        "patient_user_id": patient_id,
        "assigned_doctors": [doctor_id],
        "status": status,
    }

    payload = get_valid_payload(payload_data)

    async with async_tenant_db_session_object.begin():
        await unittest_insert_patient_waiting(
            async_tenant_db_session_object,
            patient_user_id=patient_id,
            custom_fields=payload,
        )

    _share_data_saved["status_list"].append(status)

    return True


@pytest.mark.asyncio
async def test_get_list_success_structure(async_client, _headers):
    response = await async_client.get("/v1_0/patient-waitings", headers=_headers)

    assert response.status_code == status.HTTP_200_OK

    response_json = response.json()
    assert response_json["success"] is True
    assert response_json["data"] is not None

    first_item = response_json["data"][0]

    assert "room_number" in first_item
    assert "patient_list" in first_item
    assert isinstance(first_item["patient_list"], list)


@pytest.mark.asyncio
async def test_get_list_has_not_filter_success(
    async_client, _headers, _share_data_saved
):
    response = await async_client.get("/v1_0/patient-waitings", headers=_headers)

    assert response.status_code == status.HTTP_200_OK

    response_json = response.json()
    assert response_json["success"] is True

    data = response_json["data"]
    assert len(data[0]) == _share_data_saved["status_list"].count(
        PatientWaitingStatus.SCHEDULED.value
    )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "filters",
    [
        ({"search": ""}),
        ({"search": "", "target_date": None}),
        ({"search": None, "target_date": date.today().isoformat()}),
        ({"search": "An", "target_date": None}),
        ({"search": "nguy", "target_date": None}),
        ({"search": "An Binh", "target_date": None}),
        ({"search": "チ", "target_date": None}),
        ({"search": "ミン", "target_date": None}),
        ({"search": "thIToan", "target_date": date.today().isoformat()}),
        ({"search": "レ ミン", "target_date": date.today().isoformat()}),
    ],
)
@pytest.mark.asyncio
async def test_get_list_has_filter_success(async_client, _headers, filters):
    response = await async_client.get(
        "/v1_0/patient-waitings", params=filters, headers=_headers
    )

    assert response.status_code == status.HTTP_200_OK

    response_json = response.json()
    assert response_json["success"] is True

    data = response_json["data"]
    assert isinstance(data, list)
    assert len(data) > 0


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "filters,status_code",
    [
        ({"search": "123"}, 200),
        ({"search": "AnTran", "target_date": ""}, 200),
        ({"search": None, "target_date": "2025-01-01"}, 200),
        ({"search": "チミabc", "target_date": "2025-01-01"}, 200),
        ({"search": "チミ", "target_date": "2025"}, 422),
        ({"search": "", "target_date": "2025-12"}, 422),
        ({"search": "", "target_date": "abc"}, 422),
        ({"search": "an", "target_date": "abc"}, 422),
    ],
)
@pytest.mark.asyncio
async def test_get_list_has_filter_empty_data_or_validation(
    async_client, _headers, filters, status_code
):
    response = await async_client.get(
        "/v1_0/patient-waitings", params=filters, headers=_headers
    )

    assert response.status_code == status_code

    if status_code == status.HTTP_200_OK:
        response_json = response.json()
        assert response_json["success"] is True

        data = response_json["data"]
        assert isinstance(data, list)
        assert len(data) == 0


@pytest.mark.asyncio
async def test_get_list_failed(async_client, _headers):
    with patch(
        "api.v1.patients.patient_waiting_api.PatientWaitingService.get_list_waiting_grouped"
    ) as mock_method:
        mock_method.side_effect = OperationalError("Database error", None, None)
        response = await async_client.get("/v1_0/patient-waitings", headers=_headers)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        result = response.json()
        assert result["success"] is False
        assert (
            result["messageCode"]
            == CustomMessageCode.PATIENT_WAITING_GET_LIST_FAILED.code
        )


@pytest.mark.asyncio
async def test_cleanup(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        await async_tenant_db_session_object.execute(
            text(
                "TRUNCATE TABLE patient_waitings, patient_profiles, patient_users, "
                "doctor_profiles, doctor_users RESTART IDENTITY CASCADE"
            )
        )
        log.info("✅ Cleanup test data done.")

    return True
