import random
import time
from datetime import datetime
from unittest.mock import patch

import pytest
from configuration.settings import configuration
from core.constants import DISPLAY_DATE_FORMAT, PATIENT_FIELDS_ENCRYPTED
from fastapi import status
from sqlalchemy import select
from tests.helpers.insert_data.insert_patient import unittest_remove_patient_by_ids

from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.tenant_models.patient_profiles import PatientProfile
from gc_dentist_shared.tenant_models.patient_users import PatientUser


@pytest.fixture(scope="module")
def _headers(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }


@pytest.fixture(scope="function")
def payload():
    """Fixture to build patient payload with default values"""
    unique_no = str(random.randint(10**10, 10**11 - 1))  # noqa
    phone_number = str(int(time.time() * 1e6))[-11:]

    default_payload = {
        "patient_no": unique_no,
        "is_adult": False,  # default value
        "profile": {
            "last_name": "test",
            "first_name": "test",
            "last_name_kana": "テスト",
            "first_name_kana": "テスト",
            "home_phone": phone_number,
            "phone": phone_number,
            "email": "<EMAIL>",
            "gender": 1,
            "date_of_birth": "1991/01/01",
            "prefecture_id": 1,
            "postal_code": "0600000",
            "address_1": "city test",
            "address_2": "street name and number test",
            "address_3": "house number test",
            "parent_name": "test",
            "country_code": "+81",
        },
    }
    return default_payload


@pytest.mark.asyncio
async def test_create_patient_profile_success(
    async_client, _headers, payload, async_tenant_db_session_object
):
    """Test successful patient creation with database verification"""
    response = await async_client.post("/v1_0/patients", json=payload, headers=_headers)
    assert response.status_code == 200

    data = response.json()
    assert data["success"] is True
    patient_id = data["data"]["patient_user_id"]
    aes_gcm = AesGCMRotation(configuration)

    # Verify database
    async with async_tenant_db_session_object.begin():
        patient_user = await async_tenant_db_session_object.scalar(
            select(PatientUser).where(PatientUser.id == patient_id)
        )
        assert patient_user
        assert patient_user.patient_no == payload["patient_no"]

        profile = await async_tenant_db_session_object.scalar(
            select(PatientProfile).where(PatientProfile.patient_user_id == patient_id)
        )

        for key, value in payload["profile"].items():
            actual = getattr(profile, key)
            if key == "date_of_birth":
                value = datetime.strptime(value, DISPLAY_DATE_FORMAT).strftime(
                    "%Y-%m-%d"
                )

            if key in PATIENT_FIELDS_ENCRYPTED and actual:
                actual = aes_gcm.decrypt_data(actual)

            assert actual == value

    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[patient_id]
        )


@pytest.mark.asyncio
@pytest.mark.parametrize("patient_no", ["", "**********", "**********12"])
async def test_create_patient_invalid_patient_no(
    async_client, _headers, payload, patient_no
):
    payload["patient_no"] = patient_no
    response = await async_client.post("/v1_0/patients", json=payload, headers=_headers)
    assert response.status_code == 422


@pytest.mark.asyncio
@pytest.mark.parametrize("invalid_gender", [-1, 99])
async def test_create_patient_invalid_gender(
    async_client, _headers, payload, invalid_gender
):
    payload["profile"]["gender"] = invalid_gender
    response = await async_client.post("/v1_0/patients", json=payload, headers=_headers)
    assert response.status_code == 422


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "invalid_phone", ["123456789", "**********12", "invalid-phone"]
)
async def test_create_patient_invalid_phone(
    async_client, _headers, payload, invalid_phone
):
    payload["profile"]["phone"] = invalid_phone
    response = await async_client.post("/v1_0/patients", json=payload, headers=_headers)
    assert response.status_code == 422


@pytest.mark.asyncio
@pytest.mark.parametrize("invalid_email", ["invalid-email", "test@", "@example.com"])
async def test_create_patient_invalid_email(
    async_client, _headers, payload, invalid_email
):
    payload["profile"]["email"] = invalid_email
    response = await async_client.post("/v1_0/patients", json=payload, headers=_headers)
    assert response.status_code == 422


@pytest.mark.asyncio
@pytest.mark.parametrize("invalid_postal_code", ["123456789", "invalid-postal-code"])
async def test_create_patient_invalid_postal_code(
    async_client, _headers, payload, invalid_postal_code
):
    payload["profile"]["postal_code"] = invalid_postal_code
    response = await async_client.post("/v1_0/patients", json=payload, headers=_headers)
    assert response.status_code == 422


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "invalid_date_of_birth",
    ["9999-13-01", "invalid-date-of-birth", "9999-12-01", "9999/13/01", "9999/12/01"],
)
async def test_create_patient_invalid_date_of_birth(
    async_client, _headers, payload, invalid_date_of_birth
):
    payload["profile"]["date_of_birth"] = invalid_date_of_birth
    response = await async_client.post("/v1_0/patients", json=payload, headers=_headers)
    assert response.status_code == 422


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "valid_date_of_birth", ["1990-05-15", "1990/05/15", "2000/12/31"]
)
async def test_create_patient_valid_date_of_birth_formats(
    async_client, _headers, payload, valid_date_of_birth
):
    """Test that both YYYY-MM-DD and YYYY/MM/DD formats are accepted"""
    payload["profile"]["date_of_birth"] = valid_date_of_birth
    response = await async_client.post("/v1_0/patients", json=payload, headers=_headers)
    assert response.status_code == 200


@pytest.mark.asyncio
async def test_create_patient_profile_missing_required_fields(
    async_client, _headers, payload
):
    """Test missing required fields in patient profile"""
    invalid_profile = {
        "first_name": "",
        "last_name": "",
        "first_name_kana": "",
        "last_name_kana": "",
    }

    payload["profile"].update(invalid_profile)

    response = await async_client.post("/v1_0/patients", json=payload, headers=_headers)
    assert response.status_code == 422


@pytest.mark.asyncio
async def test_create_patient_profile_duplicate_patient_no_exception(
    async_client, _headers, payload, async_tenant_db_session_object
):
    """Test CustomValueError when creating patient with duplicate patient_no"""
    # First, create a patient

    response1 = await async_client.post(
        "/v1_0/patients", json=payload, headers=_headers
    )
    assert response1.status_code == 200

    # Try to create another patient with the same patient_no
    response2 = await async_client.post(
        "/v1_0/patients", json=payload, headers=_headers
    )
    assert response2.status_code == 409

    response_data = response2.json()
    assert response_data["success"] is False
    assert "Patient clinical number exists" in response_data["message"]

    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object,
            patient_ids=[response1.json()["data"]["patient_user_id"]],
        )


@pytest.mark.asyncio
async def test_create_patient_profile_internal_server_error(
    async_client,
    _headers,
    payload,
):
    """Test general exception handling"""
    # Patch the service to throw a generic Exception
    with patch(
        "services.patient_service.PatientService.create_patient",
        side_effect=Exception("Something went wrong"),
    ):
        response = await async_client.post(
            "/v1_0/patients", headers=_headers, json=payload
        )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json()["messageCode"] == 60002
