import time
import uuid
from unittest.mock import patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from fastapi import status
from tests.helpers.insert_data.insert_document_group import (
    unittest_insert_document_group,
)


def get_payload(overrides=None):
    default_payload = {
        "name": f"Document_Group_{uuid.uuid4().hex[:8]}",
        "group_parent_id": None,
    }
    if overrides:
        default_payload.update(**overrides)
    return default_payload


@pytest_asyncio.fixture(scope="module")
async def setup_data(async_tenant_db_session_object):
    """Set up a parent document group for testing"""
    async with async_tenant_db_session_object.begin():
        # Create a test parent group
        parent_group_name = f"Parent Group {uuid.uuid4().hex[:8]}"
        parent_group = await unittest_insert_document_group(
            async_tenant_db_session_object,
            name=parent_group_name,
            is_parent=True,
        )

        return {
            "parent_group_id": parent_group.id,
            "parent_group_name": parent_group_name,
        }


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_id, payload_modifier, expected_status, expected_success, expected_message, expected_message_code",
    [
        # Test successful creation with parent group
        (
            "success_with_parent",
            lambda data: {
                "name": f"Test Group {str(uuid.uuid4())[:8]}",
                "group_parent_id": data["parent_group_id"],
            },
            status.HTTP_200_OK,
            True,
            CustomMessageCode.DOCUMENT_GROUP_CREATED_SUCCESS.title,
            CustomMessageCode.DOCUMENT_GROUP_CREATED_SUCCESS.code,
        ),
        # Test successful creation with default parent (None)
        (
            "success_with_default_parent",
            lambda data: {
                "name": str(uuid.uuid4()),
                "group_parent_id": None,
            },
            status.HTTP_200_OK,
            True,
            CustomMessageCode.DOCUMENT_GROUP_CREATED_SUCCESS.title,
            CustomMessageCode.DOCUMENT_GROUP_CREATED_SUCCESS.code,
        ),
        # Test with non-existent parent ID -> Fallback to "Other" group
        (
            "success_with_invalid_parent",
            lambda data: {
                "name": str(uuid.uuid4()),
                "group_parent_id": int(time.time()),
            },
            status.HTTP_200_OK,
            True,
            CustomMessageCode.DOCUMENT_GROUP_CREATED_SUCCESS.title,
            CustomMessageCode.DOCUMENT_GROUP_CREATED_SUCCESS.code,
        ),
        # Test duplicate name error
        (
            "error_duplicate_name",
            lambda data: {"name": data["parent_group_name"]},
            status.HTTP_400_BAD_REQUEST,
            False,
            CustomMessageCode.DOCUMENT_GROUP_EXISTS.title,
            CustomMessageCode.DOCUMENT_GROUP_EXISTS.code,
        ),
        # Test missing name field
        (
            "missing_name",
            lambda data: {
                "name": None,
                "group_parent_id": None,
            },
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            False,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.title,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
        ),
        # Test empty name field
        (
            "empty_name",
            lambda data: {"name": ""},
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            False,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.title,
            CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
        ),
    ],
)
async def test_create_document_group_parametrized(
    async_client,
    _headers_tenant_uuid,
    setup_data,
    test_id,
    payload_modifier,
    expected_status,
    expected_success,
    expected_message,
    expected_message_code,
):
    """Parametrized test for create document group API"""
    payload = get_payload(payload_modifier(setup_data))

    response = await async_client.post(
        "/v1_0/document-groups", json=payload, headers=_headers_tenant_uuid
    )

    assert response.status_code == expected_status
    result = response.json()
    assert result["success"] is expected_success
    assert result["message"] == expected_message
    assert result["messageCode"] == expected_message_code

    if expected_success:
        assert "document_group_id" in result["data"]


@pytest.mark.asyncio
async def test_create_document_group_generic_exception(
    async_client, _headers_tenant_uuid
):
    """Test general exception handling in create document group endpoint"""
    # Patch to throw a generic Exception
    with patch(
        "services.document_group_service.DocumentGroupService.create_document_group",
        side_effect=Exception("Something went wrong"),
    ):
        payload = get_payload(
            {
                "name": str(uuid.uuid4()),
            }
        )
        response = await async_client.post(
            "/v1_0/document-groups", headers=_headers_tenant_uuid, json=payload
        )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    result = response.json()
    assert result["success"] is False
    assert result["message"] == CustomMessageCode.DOCUMENT_GROUP_CREATED_FAILED.title
    assert result["messageCode"] == CustomMessageCode.DOCUMENT_GROUP_CREATED_FAILED.code
