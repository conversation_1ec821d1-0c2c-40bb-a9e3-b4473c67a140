import time
import uuid
from datetime import datetime
from unittest.mock import patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from fastapi import status
from tests.helpers.insert_data.insert_document_group import (
    unittest_insert_document_group,
)


@pytest_asyncio.fixture(scope="module")
async def setup_data(async_tenant_db_session_object):
    """Set up test data for document groups"""
    async with async_tenant_db_session_object.begin():
        # Create a regular document group
        regular_group = await unittest_insert_document_group(
            async_tenant_db_session_object,
            name=f"Regular Group {uuid.uuid4().hex[:8]}",
            is_parent=False,
        )

        # Create a document group to be marked as deleted
        deleted_group = await unittest_insert_document_group(
            async_tenant_db_session_object, name=f"Deleted Group {uuid.uuid4().hex[:8]}"
        )

        # Mark the group as deleted
        deleted_group.deleted_at = datetime.now()
        await async_tenant_db_session_object.flush()

        return {
            "regular_group_id": regular_group.id,
            "deleted_group_id": deleted_group.id,
            "non_existent_id": int(time.time()),
        }


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_id, document_group_id_source, expected_status, expected_success, expected_message_code, expected_message",
    [
        (
            "success_regular_group",
            lambda data: data["regular_group_id"],
            status.HTTP_200_OK,
            True,
            CustomMessageCode.DOCUMENT_GROUP_GET_SUCCESS.code,
            CustomMessageCode.DOCUMENT_GROUP_GET_SUCCESS.title,
        ),
        (
            "error_not_found",
            lambda data: data["non_existent_id"],
            status.HTTP_400_BAD_REQUEST,
            False,
            CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.code,
            CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.title,
        ),
        (
            "error_deleted",
            lambda data: data["deleted_group_id"],
            status.HTTP_400_BAD_REQUEST,
            False,
            CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.code,
            CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.title,
        ),
    ],
)
async def test_get_document_group_parametrized(
    async_client,
    _headers_tenant_uuid,
    setup_data,
    test_id,
    document_group_id_source,
    expected_status,
    expected_success,
    expected_message_code,
    expected_message,
):
    """Parametrized test for get document group API"""
    document_group_id = document_group_id_source(setup_data)

    response = await async_client.get(
        f"/v1_0/document-groups/{document_group_id}", headers=_headers_tenant_uuid
    )

    assert response.status_code == expected_status
    result = response.json()
    assert result["success"] is expected_success
    assert result["messageCode"] == expected_message_code
    assert result["message"] == expected_message

    if expected_success:
        assert "id" in result["data"]
        assert "name" in result["data"]
        assert "is_parent" in result["data"]
        assert "group_parent_id" in result["data"]


@pytest.mark.asyncio
async def test_get_document_group_invalid_id_format(async_client, _headers_tenant_uuid):
    """Test retrieval with invalid ID format"""
    response = await async_client.get(
        "/v1_0/document-groups/invalid_id", headers=_headers_tenant_uuid
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.asyncio
async def test_get_document_group_generic_exception(
    async_client, _headers_tenant_uuid, setup_data
):
    """Test handling of generic Exception in get document group endpoint"""
    document_group_id = setup_data["regular_group_id"]

    # Patch to throw a generic Exception
    with patch(
        "services.document_group_service.DocumentGroupService.get_document_group",
        side_effect=Exception("Something went wrong"),
    ):
        response = await async_client.get(
            f"/v1_0/document-groups/{document_group_id}", headers=_headers_tenant_uuid
        )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    result = response.json()
    assert result["success"] is False
    assert result["messageCode"] == CustomMessageCode.DOCUMENT_GROUP_GET_FAILED.code
    assert result["message"] == CustomMessageCode.DOCUMENT_GROUP_GET_FAILED.title
