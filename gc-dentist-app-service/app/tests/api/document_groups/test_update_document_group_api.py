import time
import uuid
from datetime import datetime
from unittest.mock import patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from fastapi import status
from tests.helpers.insert_data.insert_document_group import (
    unittest_insert_document_group,
)


@pytest_asyncio.fixture(scope="module")
async def setup_data(async_tenant_db_session_object):
    """Set up test data for document groups"""
    async with async_tenant_db_session_object.begin():
        # Create a regular document group
        regular_group = await unittest_insert_document_group(
            async_tenant_db_session_object,
            name=f"Regular Update Group {uuid.uuid4().hex[:8]}",
            is_parent=False,
        )

        # Create a parent document group
        parent_group = await unittest_insert_document_group(
            async_tenant_db_session_object,
            name=f"Parent Update Group {uuid.uuid4().hex[:8]}",
            is_parent=True,
        )

        # Create a document group to check for duplicate names
        duplicate_check_group = await unittest_insert_document_group(
            async_tenant_db_session_object,
            name=f"Duplicate Check Group {uuid.uuid4().hex[:8]}",
        )

        # Create a document group marked as deleted
        deleted_group = await unittest_insert_document_group(
            async_tenant_db_session_object,
            name=f"Deleted Update Group {uuid.uuid4().hex[:8]}",
        )

        # Mark the group as deleted
        deleted_group.deleted_at = datetime.now()
        await async_tenant_db_session_object.flush()

        return {
            "regular_group_id": regular_group.id,
            "parent_group_id": parent_group.id,
            "duplicate_check_group_name": duplicate_check_group.name,
            "deleted_group_id": deleted_group.id,
            "non_existent_id": int(time.time()),
        }


def get_payload(overrides=None):
    """Helper function to create update payload"""
    default_payload = {
        "name": f"Updated Group {uuid.uuid4().hex[:8]}",
        "group_parent_id": None,
    }
    if overrides:
        default_payload.update(**overrides)
    return default_payload


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_id, document_group_id_source, payload_modifier, "
    "expected_status, expected_success, expected_message_code, expected_message",
    [
        # Successful update with new name
        (
            "success_update_name",
            lambda data: data["regular_group_id"],
            lambda data: {"name": f"Updated Name {uuid.uuid4().hex[:8]}"},
            status.HTTP_200_OK,
            True,
            CustomMessageCode.DOCUMENT_GROUP_UPDATED_SUCCESS.code,
            CustomMessageCode.DOCUMENT_GROUP_UPDATED_SUCCESS.title,
        ),
        # Successful update with new parent group
        (
            "success_update_parent",
            lambda data: data["regular_group_id"],
            lambda data: {"group_parent_id": data["parent_group_id"]},
            status.HTTP_200_OK,
            True,
            CustomMessageCode.DOCUMENT_GROUP_UPDATED_SUCCESS.code,
            CustomMessageCode.DOCUMENT_GROUP_UPDATED_SUCCESS.title,
        ),
        # Successful update with both name and parent group
        (
            "success_update_both",
            lambda data: data["regular_group_id"],
            lambda data: {
                "name": f"Updated Both {uuid.uuid4().hex[:8]}",
                "group_parent_id": data["parent_group_id"],
            },
            status.HTTP_200_OK,
            True,
            CustomMessageCode.DOCUMENT_GROUP_UPDATED_SUCCESS.code,
            CustomMessageCode.DOCUMENT_GROUP_UPDATED_SUCCESS.title,
        ),
        # Error when document group not found
        (
            "error_not_found",
            lambda data: data["non_existent_id"],
            lambda data: {"name": f"Not Found {uuid.uuid4().hex[:8]}"},
            status.HTTP_400_BAD_REQUEST,
            False,
            CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.code,
            CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.title,
        ),
        # Error when document group is deleted
        (
            "error_deleted",
            lambda data: data["deleted_group_id"],
            lambda data: {"name": f"Deleted Group {uuid.uuid4().hex[:8]}"},
            status.HTTP_400_BAD_REQUEST,
            False,
            CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.code,
            CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.title,
        ),
        # Error when name already exists
        (
            "error_duplicate_name",
            lambda data: data["regular_group_id"],
            lambda data: {"name": data["duplicate_check_group_name"]},
            status.HTTP_400_BAD_REQUEST,
            False,
            CustomMessageCode.DOCUMENT_GROUP_EXISTS.code,
            CustomMessageCode.DOCUMENT_GROUP_EXISTS.title,
        ),
    ],
)
async def test_update_document_group_parametrized(
    async_client,
    _headers_tenant_uuid,
    setup_data,
    test_id,
    document_group_id_source,
    payload_modifier,
    expected_status,
    expected_success,
    expected_message_code,
    expected_message,
):
    """Parametrized test for update document group API with various scenarios"""
    document_group_id = document_group_id_source(setup_data)
    payload = get_payload(payload_modifier(setup_data))

    response = await async_client.put(
        f"/v1_0/document-groups/{document_group_id}",
        json=payload,
        headers=_headers_tenant_uuid,
    )

    assert response.status_code == expected_status
    result = response.json()
    assert result["success"] is expected_success
    assert result["messageCode"] == expected_message_code
    assert result["message"] == expected_message

    if expected_success:
        assert "document_group_id" in result["data"]
        assert result["data"]["document_group_id"] == document_group_id


@pytest.mark.asyncio
async def test_update_document_group_invalid_id_format(
    async_client, _headers_tenant_uuid
):
    """Test update with invalid ID format"""
    payload = get_payload()
    response = await async_client.put(
        "/v1_0/document-groups/invalid_id", json=payload, headers=_headers_tenant_uuid
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.asyncio
async def test_update_document_group_generic_exception(
    async_client, _headers_tenant_uuid, setup_data
):
    """Test handling of generic Exception in update document group endpoint"""
    document_group_id = setup_data["regular_group_id"]
    payload = get_payload()

    # Patch to throw a generic Exception
    with patch(
        "services.document_group_service.DocumentGroupService.update_document_group",
        side_effect=Exception("Something went wrong"),
    ):
        response = await async_client.put(
            f"/v1_0/document-groups/{document_group_id}",
            json=payload,
            headers=_headers_tenant_uuid,
        )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    result = response.json()
    assert result["success"] is False
    assert result["messageCode"] == CustomMessageCode.DOCUMENT_GROUP_UPDATED_FAILED.code
    assert result["message"] == CustomMessageCode.DOCUMENT_GROUP_UPDATED_FAILED.title
