import random
import string
import uuid
from datetime import datetime
from unittest.mock import patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from fastapi import status
from tests.helpers.insert_data.insert_document_group import (
    unittest_delete_document_groups,
    unittest_insert_document_group,
)


@pytest_asyncio.fixture(scope="class")
async def setup_data(async_tenant_db_session_object):
    """Set up test data for document groups with clear parent-child relationships"""
    document_group_ids = []

    async with async_tenant_db_session_object.begin():
        # Create parent groups with distinct names
        parent_group_1 = await unittest_insert_document_group(
            async_tenant_db_session_object,
            name=f"Parent Group 1 {uuid.uuid4().hex[:8]}",
            is_parent=True,
        )
        document_group_ids.append(parent_group_1.id)

        parent_group_2 = await unittest_insert_document_group(
            async_tenant_db_session_object,
            name=f"Parent Group 2 {uuid.uuid4().hex[:8]}",
            is_parent=True,
        )
        document_group_ids.append(parent_group_2.id)

        # Create child groups for parent 1
        child_1_1 = await unittest_insert_document_group(
            async_tenant_db_session_object,
            name=f"Child 1.1 of {parent_group_1.name}",
            is_parent=False,
            group_parent_id=parent_group_1.id,
        )
        document_group_ids.append(child_1_1.id)

        child_1_2 = await unittest_insert_document_group(
            async_tenant_db_session_object,
            name=f"Child 1.2 of {parent_group_1.name}",
            is_parent=False,
            group_parent_id=parent_group_1.id,
        )
        document_group_ids.append(child_1_2.id)

        # Create child group for parent 2
        child_2_1 = await unittest_insert_document_group(
            async_tenant_db_session_object,
            name=f"Child 2.1 of {parent_group_2.name}",
            is_parent=False,
            group_parent_id=parent_group_2.id,
        )
        document_group_ids.append(child_2_1.id)

        # Create a parent group for search testing
        search_term = "".join(random.choices(string.ascii_letters, k=10))
        searchable_parent = await unittest_insert_document_group(
            async_tenant_db_session_object,
            name=f"{search_term} {uuid.uuid4().hex[:8]}",
            is_parent=True,
        )
        document_group_ids.append(searchable_parent.id)

        # Create a child for the searchable parent
        searchable_child = await unittest_insert_document_group(
            async_tenant_db_session_object,
            name=f"Child of {search_term} {uuid.uuid4().hex[:8]}",
            is_parent=False,
            group_parent_id=searchable_parent.id,
        )
        document_group_ids.append(searchable_child.id)

        # Create a parent that will be marked as deleted
        deleted_parent = await unittest_insert_document_group(
            async_tenant_db_session_object,
            name=f"Deleted Parent Group {uuid.uuid4().hex[:8]}",
            is_parent=True,
        )
        document_group_ids.append(deleted_parent.id)

        # Mark the group as deleted
        deleted_parent.deleted_at = datetime.now()
        await async_tenant_db_session_object.flush()

        # Return all the created data
        data = {
            "document_group_ids": document_group_ids,
            "parent_group_1": parent_group_1,
            "parent_group_2": parent_group_2,
            "child_1_1": child_1_1,
            "child_1_2": child_1_2,
            "child_2_1": child_2_1,
            "searchable_parent": searchable_parent,
            "searchable_child": searchable_child,
            "deleted_parent": deleted_parent,
            "search_term": search_term,
        }

        return data


@pytest.mark.asyncio
async def test_get_list_document_group_success(
    async_client, _headers_tenant_uuid, setup_data, async_tenant_db_session_object
):
    """Test successful retrieval of document group list"""
    response = await async_client.get(
        "/v1_0/document-groups", headers=_headers_tenant_uuid
    )

    assert response.status_code == status.HTTP_200_OK
    result = response.json()
    assert result["success"] is True
    assert result["message"] == CustomMessageCode.DOCUMENT_GROUP_GET_LIST_SUCCESS.title
    assert (
        result["messageCode"] == CustomMessageCode.DOCUMENT_GROUP_GET_LIST_SUCCESS.code
    )

    data = result["data"]
    assert "items" in data
    assert "page" in data
    assert "size" in data
    assert "total" in data

    parent_id_1 = setup_data["parent_group_1"].id
    parent_id_2 = setup_data["parent_group_2"].id
    searchable_parent_id = setup_data["searchable_parent"].id

    items = data["items"]
    parent_1_in_response = next(
        (item for item in items if item["id"] == parent_id_1), None
    )
    parent_2_in_response = next(
        (item for item in items if item["id"] == parent_id_2), None
    )
    searchable_in_response = next(
        (item for item in items if item["id"] == searchable_parent_id), None
    )

    # Verify all parent groups are found
    assert parent_1_in_response is not None
    assert parent_2_in_response is not None
    assert searchable_in_response is not None

    # Check parent 1's children
    children_ids_parent_1 = [
        setup_data["child_1_1"].id,
        setup_data["child_1_2"].id,
    ]
    children_in_response_1 = [
        child["id"] for child in parent_1_in_response["group_children"]
    ]
    for child_id in children_ids_parent_1:
        assert child_id in children_in_response_1

    # Check parent 2's child
    child_id_parent_2 = setup_data["child_2_1"].id
    children_in_response_2 = [
        child["id"] for child in parent_2_in_response["group_children"]
    ]
    assert child_id_parent_2 in children_in_response_2

    # Check searchable parent's child
    searchable_child_id = setup_data["searchable_child"].id
    searchable_children = [
        child["id"] for child in searchable_in_response["group_children"]
    ]
    assert searchable_child_id in searchable_children

    async with async_tenant_db_session_object.begin():
        await unittest_delete_document_groups(
            async_tenant_db_session_object, setup_data["document_group_ids"]
        )


@pytest.mark.asyncio
async def test_get_list_document_group_with_search(
    async_client, _headers_tenant_uuid, setup_data, async_tenant_db_session_object
):
    """Test document group list with search parameter"""
    search_term = setup_data["search_term"]

    response = await async_client.get(
        f"/v1_0/document-groups?search={search_term}", headers=_headers_tenant_uuid
    )

    assert response.status_code == status.HTTP_200_OK
    result = response.json()
    assert result["success"] is True

    data = result["data"]
    searchable_parent = setup_data["searchable_parent"]
    searchable_child = setup_data["searchable_child"]

    # There should be only one parent containing the search term
    assert len(data["items"]) == 1
    assert data["items"][0]["id"] == searchable_parent.id
    assert data["items"][0]["name"] == searchable_parent.name

    # Verify the child is correctly included
    group_children = data["items"][0]["group_children"]
    assert len(group_children) == 1
    assert group_children[0]["id"] == searchable_child.id
    assert group_children[0]["name"] == searchable_child.name

    async with async_tenant_db_session_object.begin():
        await unittest_delete_document_groups(
            async_tenant_db_session_object, setup_data["document_group_ids"]
        )


@pytest.mark.asyncio
async def test_get_list_document_group_no_results(async_client, _headers_tenant_uuid):
    """Test document group list with search that returns no results"""
    random_search = f"NonExistentGroup{uuid.uuid4().hex}"

    response = await async_client.get(
        f"/v1_0/document-groups?search={random_search}", headers=_headers_tenant_uuid
    )

    assert response.status_code == status.HTTP_200_OK
    result = response.json()
    assert result["success"] is True

    data = result["data"]
    assert len(data["items"]) == 0
    assert data["total"] == 0


@pytest.mark.asyncio
async def test_get_list_document_group_deleted_excluded(
    async_client, _headers_tenant_uuid, setup_data, async_tenant_db_session_object
):
    """Test that deleted document groups are excluded from the list"""
    deleted_parent_id = setup_data["deleted_parent"].id

    response = await async_client.get(
        "/v1_0/document-groups", headers=_headers_tenant_uuid
    )

    assert response.status_code == status.HTTP_200_OK
    result = response.json()
    data = result["data"]

    # Check that deleted parent is not in the response
    parent_ids = [item["id"] for item in data["items"]]
    assert deleted_parent_id not in parent_ids

    async with async_tenant_db_session_object.begin():
        await unittest_delete_document_groups(
            async_tenant_db_session_object, setup_data["document_group_ids"]
        )


@pytest.mark.asyncio
async def test_get_list_document_group_pagination(
    async_client, _headers_tenant_uuid, setup_data, async_tenant_db_session_object
):
    """Test pagination of document group list"""
    # First page with size 2
    response1 = await async_client.get(
        "/v1_0/document-groups?page=1&size=2", headers=_headers_tenant_uuid
    )

    assert response1.status_code == status.HTTP_200_OK
    result1 = response1.json()
    data1 = result1["data"]

    assert data1["page"] == 1
    assert data1["size"] == 2
    assert len(data1["items"]) <= 2

    # If we have at least 3 items, test the second page
    if data1["total"] > 2:
        # Second page with size 2
        response2 = await async_client.get(
            "/v1_0/document-groups?page=2&size=2", headers=_headers_tenant_uuid
        )

        assert response2.status_code == status.HTTP_200_OK
        result2 = response2.json()
        data2 = result2["data"]

        assert data2["page"] == 2
        assert data2["size"] == 2

    async with async_tenant_db_session_object.begin():
        await unittest_delete_document_groups(
            async_tenant_db_session_object, setup_data["document_group_ids"]
        )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "params",
    [
        {"page": 0, "size": 10},
        {"page": -1, "size": 10},
        {"page": 1, "size": 0},
        {"page": 1, "size": -5},
    ],
)
async def test_get_list_invalid_pagination_input(
    async_client,
    _headers_tenant_uuid,
    setup_data,
    params,
    async_tenant_db_session_object,
):
    response = await async_client.get(
        "/v1_0/document-groups", params=params, headers=_headers_tenant_uuid
    )
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    error_data = response.json()
    assert "data" in error_data
    assert isinstance(error_data["data"], list)
    assert len(error_data["data"]) > 0

    async with async_tenant_db_session_object.begin():
        await unittest_delete_document_groups(
            async_tenant_db_session_object, setup_data["document_group_ids"]
        )


@pytest.mark.asyncio
async def test_get_list_document_group_generic_exception(
    async_client,
    _headers_tenant_uuid,
):
    """Test handling of generic Exception in get list document group endpoint"""
    # Patch to throw a generic Exception
    with patch(
        "services.document_group_service.DocumentGroupService.get_list_document_group",
        side_effect=Exception("Something went wrong"),
    ):
        response = await async_client.get(
            "/v1_0/document-groups", headers=_headers_tenant_uuid
        )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    result = response.json()
    assert result["success"] is False
    assert (
        result["messageCode"] == CustomMessageCode.DOCUMENT_GROUP_GET_LIST_FAILED.code
    )
    assert result["message"] == CustomMessageCode.DOCUMENT_GROUP_GET_LIST_FAILED.title
