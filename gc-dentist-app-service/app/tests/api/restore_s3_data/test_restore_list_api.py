import uuid
from datetime import datetime, timedelta, timezone

import pytest
from fastapi import status
from tests.helpers.enums.restore_s3_data import S3RestoreStatus
from tests.helpers.insert_data.insert_restore_s3_data import (
    unittest_insert_restore_s3_data,
    unittest_remove_restore_s3_data,
)


@pytest.fixture(scope="module")
def _headers(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }


@pytest.mark.asyncio
async def test_get_list_success_structure(
    async_client, async_tenant_db_session_object, _headers
):
    """Test the successful response structure of list restore document API"""
    async with async_tenant_db_session_object.begin():
        restore_data_id = await unittest_insert_restore_s3_data(
            async_tenant_db_session_object
        )

    response = await async_client.get("/v1_0/s3/restore-document", headers=_headers)
    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True
    assert response_json["data"] is not None

    data = response_json["data"]
    assert isinstance(data["items"], list)
    assert "total" in data
    assert "page" in data
    assert "size" in data

    assert data["total"] >= 1
    assert len(data["items"]) >= 1

    # Verify item structure
    expected_fields = [
        "id",
        "document_uuid",
        "version_id",
        "s3_url",
        "status",
        "requested_at",
        "restored_at",
        "expires_at",
    ]
    first_item = data["items"][0]
    for field in expected_fields:
        assert field in first_item

    async with async_tenant_db_session_object.begin():
        await unittest_remove_restore_s3_data(
            async_tenant_db_session_object, restore_data_ids=[restore_data_id]
        )


@pytest.mark.asyncio
async def test_get_list_pagination_logic(
    async_client, async_tenant_db_session_object, _headers
):
    """Test pagination functionality for list restore document API"""
    document_uuid = str(uuid.uuid4())
    async with async_tenant_db_session_object.begin():
        restore_data_id_1 = await unittest_insert_restore_s3_data(
            async_tenant_db_session_object,
            custom_fields={
                "document_uuid": document_uuid,
                "requested_at": datetime.now(timezone.utc) - timedelta(minutes=10),
            },
        )
        restore_data_id_2 = await unittest_insert_restore_s3_data(
            async_tenant_db_session_object,
            custom_fields={
                "document_uuid": document_uuid,
                "requested_at": datetime.now(timezone.utc) - timedelta(minutes=5),
            },
        )
        restore_data_id_3 = await unittest_insert_restore_s3_data(
            async_tenant_db_session_object,
            custom_fields={
                "document_uuid": document_uuid,
                "requested_at": datetime.now(timezone.utc),
            },
        )

    # Check page 1
    response_page1 = await async_client.get(
        f"/v1_0/s3/restore-document?document_uuid={document_uuid}&page=1&size=2",
        headers=_headers,
    )

    assert response_page1.status_code == status.HTTP_200_OK
    data_page1 = response_page1.json()["data"]

    assert data_page1["total"] == 3
    assert len(data_page1["items"]) == 2
    assert data_page1["page"] == 1
    assert data_page1["size"] == 2

    # Items should be sorted by requested_at desc, so newest first
    assert data_page1["items"][0]["id"] == restore_data_id_3
    assert data_page1["items"][1]["id"] == restore_data_id_2

    # Check page 2
    response_page2 = await async_client.get(
        f"/v1_0/s3/restore-document?document_uuid={document_uuid}&page=2&size=2",
        headers=_headers,
    )

    assert response_page2.status_code == status.HTTP_200_OK
    data_page2 = response_page2.json()["data"]

    assert data_page2["total"] == 3
    assert len(data_page2["items"]) == 1
    assert data_page2["page"] == 2
    assert data_page2["items"][0]["id"] == restore_data_id_1

    async with async_tenant_db_session_object.begin():
        await unittest_remove_restore_s3_data(
            async_tenant_db_session_object,
            restore_data_ids=[restore_data_id_1, restore_data_id_2, restore_data_id_3],
        )


@pytest.mark.asyncio
async def test_get_list_with_document_uuid_filter(
    async_client, async_tenant_db_session_object, _headers
):
    """Test filtering by document_uuid parameter"""
    document_uuid_1 = str(uuid.uuid4())
    document_uuid_2 = str(uuid.uuid4())

    async with async_tenant_db_session_object.begin():
        restore_data_id_1 = await unittest_insert_restore_s3_data(
            async_tenant_db_session_object,
            custom_fields={"document_uuid": document_uuid_1},
        )
        restore_data_id_2 = await unittest_insert_restore_s3_data(
            async_tenant_db_session_object,
            custom_fields={"document_uuid": document_uuid_2},
        )

    # Test filter by document_uuid_1
    response = await async_client.get(
        f"/v1_0/s3/restore-document?document_uuid={document_uuid_1}", headers=_headers
    )

    assert response.status_code == status.HTTP_200_OK
    data = response.json()["data"]

    assert data["total"] >= 1
    assert len(data["items"]) >= 1
    assert all(item["document_uuid"] == document_uuid_1 for item in data["items"])

    async with async_tenant_db_session_object.begin():
        await unittest_remove_restore_s3_data(
            async_tenant_db_session_object,
            restore_data_ids=[restore_data_id_1, restore_data_id_2],
        )


@pytest.mark.asyncio
async def test_get_list_with_status_filter(
    async_client, async_tenant_db_session_object, _headers
):
    """Test filtering by status parameter"""
    async with async_tenant_db_session_object.begin():
        restore_data_id_1 = await unittest_insert_restore_s3_data(
            async_tenant_db_session_object,
            custom_fields={"status": S3RestoreStatus.RESTORING.value},
        )
        restore_data_id_2 = await unittest_insert_restore_s3_data(
            async_tenant_db_session_object,
            custom_fields={
                "status": S3RestoreStatus.RESTORED.value,
                "restored_at": datetime.now(timezone.utc),
                "expires_at": datetime.now(timezone.utc) + timedelta(days=7),
            },
        )

    # Test filter by RESTORING status
    response = await async_client.get(
        f"/v1_0/s3/restore-document?status={S3RestoreStatus.RESTORING.value}",
        headers=_headers,
    )

    assert response.status_code == status.HTTP_200_OK
    data = response.json()["data"]

    assert data["total"] >= 1
    assert len(data["items"]) >= 1
    assert all(
        item["status"] == S3RestoreStatus.RESTORING.value for item in data["items"]
    )

    async with async_tenant_db_session_object.begin():
        await unittest_remove_restore_s3_data(
            async_tenant_db_session_object,
            restore_data_ids=[restore_data_id_1, restore_data_id_2],
        )


@pytest.mark.asyncio
async def test_get_list_with_search_filter(
    async_client, async_tenant_db_session_object, _headers
):
    """Test filtering with the search parameter"""
    unique_pattern = f"test-{str(uuid.uuid4())[:8]}"
    s3_url = f"main/tenant-uuid/{unique_pattern}/document.pdf"

    async with async_tenant_db_session_object.begin():
        restore_data_id = await unittest_insert_restore_s3_data(
            async_tenant_db_session_object, custom_fields={"s3_url": s3_url}
        )

    # Test search by unique pattern
    response = await async_client.get(
        f"/v1_0/s3/restore-document?search={unique_pattern}", headers=_headers
    )

    assert response.status_code == status.HTTP_200_OK
    data = response.json()["data"]

    assert data["total"] >= 1
    assert len(data["items"]) >= 1
    assert any(unique_pattern in item["s3_url"] for item in data["items"])

    async with async_tenant_db_session_object.begin():
        await unittest_remove_restore_s3_data(
            async_tenant_db_session_object,
            restore_data_ids=[restore_data_id],
        )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "params",
    [
        {"page": 0, "size": 10},
        {"page": -1, "size": 10},
        {"page": 1, "size": 0},
        {"page": 1, "size": -5},
    ],
)
async def test_get_list_invalid_pagination_input(async_client, _headers, params):
    """Test error handling with invalid pagination parameters"""
    response = await async_client.get(
        "/v1_0/s3/restore-document", params=params, headers=_headers
    )
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    error_data = response.json()
    assert "data" in error_data
    assert isinstance(error_data["data"], list)
    assert len(error_data["data"]) > 0
