import time
from datetime import datetime, timedelta, timezone

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from fastapi import status
from tests.helpers.enums.restore_s3_data import S3RestoreStatus
from tests.helpers.insert_data.insert_restore_s3_data import (
    unittest_insert_restore_s3_data,
    unittest_remove_restore_s3_data,
)


@pytest.fixture(scope="module")
def _headers(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }


@pytest_asyncio.fixture(scope="function")
async def setup_restore_document_data(async_tenant_db_session_object):
    """Setup test data for restore document tests"""
    restore_data_ids = []

    async with async_tenant_db_session_object.begin():
        # Insert a document with RESTORING status
        restore_id_1 = await unittest_insert_restore_s3_data(
            async_tenant_db_session_object,
            custom_fields={
                "status": S3RestoreStatus.RESTORING.value,
            },
        )
        restore_data_ids.append(restore_id_1)

        # Insert a document with RESTORED status
        restore_id_2 = await unittest_insert_restore_s3_data(
            async_tenant_db_session_object,
            custom_fields={
                "status": S3RestoreStatus.RESTORED.value,
                "restored_at": datetime.now(timezone.utc),
                "expires_at": datetime.now(timezone.utc) + timedelta(days=7),
                "s3_url_temp": "https://example.com/temp-url-signed?expires=123456789&signature=abc123",
            },
        )
        restore_data_ids.append(restore_id_2)

    yield restore_data_ids

    # Cleanup
    async with async_tenant_db_session_object.begin():
        await unittest_remove_restore_s3_data(
            async_tenant_db_session_object, restore_data_ids=restore_data_ids
        )


@pytest.mark.asyncio
async def test_get_detail_restore_document_success(
    async_client, _headers, setup_restore_document_data
):
    """Test successful retrieval of restore document details"""
    restore_data_ids = setup_restore_document_data

    # Define base expected response fields
    expected_fields = [
        "id",
        "document_uuid",
        "version_id",
        "s3_url",
        "s3_url_temp",
        "status",
        "requested_at",
        "restored_at",
        "expires_at",
    ]

    # Test each status type
    for restore_id in restore_data_ids:
        response = await async_client.get(
            f"/v1_0/s3/restore-document/{restore_id}",
            headers=_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        assert response_json["success"] is True

        # Verify response structure
        data = response_json["data"]

        # Check base fields
        for field in expected_fields:
            assert field in data

        # Verify id matches requested id
        assert data["id"] == restore_id

        # For RESTORED status, verify s3_url_temp and expires_at fields
        if data["status"] == S3RestoreStatus.RESTORED.value:
            assert "s3_url_temp" in data and data["s3_url_temp"] is not None
            assert "expires_at" in data and data["expires_at"] is not None


@pytest.mark.asyncio
async def test_get_detail_restore_document_not_found(async_client, _headers):
    """Test error handling when restore document is not found"""
    non_existent_id = int(time.time())

    response = await async_client.get(
        f"/v1_0/s3/restore-document/{non_existent_id}",
        headers=_headers,
    )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_json = response.json()
    assert response_json["success"] is False
    assert (
        response_json["messageCode"]
        == CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.code
    )
    assert (
        response_json["message"]
        == CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.title
    )
