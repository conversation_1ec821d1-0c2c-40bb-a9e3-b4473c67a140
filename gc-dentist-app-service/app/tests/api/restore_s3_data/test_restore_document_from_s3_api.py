import time
import uuid
from datetime import datetime, timezone
from unittest.mock import AsyncMock, patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from fastapi import status
from tests.helpers.insert_data.insert_document_management import (
    unittest_insert_document,
)
from tests.helpers.insert_data.insert_patient import unittest_insert_patient
from tests.helpers.s3_mock import MockS3Client

from gc_dentist_shared.core.enums.document import DocumentS3Status
from gc_dentist_shared.core.enums.s3_enums import S3RestoreStatus
from gc_dentist_shared.core.exception_handler.custom_exception import (
    S3BucketExceptionError,
)
from gc_dentist_shared.tenant_models import RestoreS3Data


@pytest_asyncio.fixture(scope="function")
async def setup_data(async_tenant_db_session_object, tenant_uuid):
    """Setup test data for document management"""
    document_data = []

    async with async_tenant_db_session_object.begin():
        # Create a patient user
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)

        # Document with multiple object keys
        normal_doc_uuid = str(uuid.uuid4())
        normal_document = await unittest_insert_document(
            async_tenant_db_session_object,
            patient_user_id,
            custom_document_fields={
                "document_data": {
                    "file_key_1": f"main/{tenant_uuid}/documents/test1_image1.jpg",
                    "file_key_2": f"main/{tenant_uuid}/documents/test1_image2.jpg",
                },
                "document_uuid": normal_doc_uuid,
                "s3_status": DocumentS3Status.ARCHIVE.value,
                "version_id": 1,
            },
        )
        document_data.append(
            {
                "document_uuid": normal_doc_uuid,
                "version_id": 1,
                "object_keys": list(normal_document["document_data"].values()),
            }
        )

        # Document that already has a restore record
        restoring_doc_uuid = str(uuid.uuid4())
        restoring_doc_version = 1
        restoring_document = await unittest_insert_document(
            async_tenant_db_session_object,
            patient_user_id,
            custom_document_fields={
                "document_data": {
                    "file_key_1": f"main/{tenant_uuid}/documents/test3_image.jpg"
                },
                "document_uuid": restoring_doc_uuid,
                "s3_status": DocumentS3Status.ARCHIVE.value,
                "version_id": restoring_doc_version,
            },
        )

        # Add a restore record for document 3 to test the "already restoring" case
        restore_data = RestoreS3Data(
            document_uuid=restoring_doc_uuid,
            version_id=restoring_doc_version,
            s3_url="main/documents/test3_image.jpg",
            status=S3RestoreStatus.RESTORING.value,
            requested_at=datetime.now(timezone.utc),
        )
        async_tenant_db_session_object.add(restore_data)
        await async_tenant_db_session_object.flush()

        document_data.append(
            {
                "document_uuid": restoring_doc_uuid,
                "version_id": restoring_doc_version,
                "object_keys": list(restoring_document["document_data"].values()),
            }
        )

    yield document_data


@pytest.mark.asyncio
async def test_restore_document_success(async_client, _headers_tenant_uuid, setup_data):
    """Test successful document restoration from S3"""
    # Arrange
    document_data = setup_data[0]  # Use the first document with multiple keys
    document_uuid = document_data["document_uuid"]
    version_id = document_data["version_id"]

    mock_s3_client = MockS3Client()
    with patch(
        "gc_dentist_shared.core.common.s3_bucket.S3Client.get_instance",
        return_value=mock_s3_client,
    ):
        # Act
        response = await async_client.post(
            "/v1_0/s3/restore-document",
            headers=_headers_tenant_uuid,
            json={"document_uuid": document_uuid, "version_id": version_id},
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["success"] is True
        assert (
            response_data["message"] == CustomMessageCode.RESTORE_S3_DATA_SUCCESS.title
        )

        # Verify restore_object was called for each object key
        assert len(mock_s3_client.restore_requests) == len(document_data["object_keys"])


@pytest.mark.asyncio
async def test_restore_document_already_restoring(
    async_client, _headers_tenant_uuid, setup_data
):
    """Test error when document is already being restored"""
    # Arrange
    document_data = setup_data[1]  # Use the second document (already restoring)
    document_uuid = document_data["document_uuid"]
    version_id = document_data["version_id"]

    mock_s3_client = MockS3Client()
    with patch(
        "gc_dentist_shared.core.common.s3_bucket.S3Client.get_instance",
        return_value=mock_s3_client,
    ):
        # Act
        response = await async_client.post(
            "/v1_0/s3/restore-document",
            headers=_headers_tenant_uuid,
            json={"document_uuid": document_uuid, "version_id": version_id},
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_data = response.json()
        assert response_data["success"] is False
        assert response_data["message"] == CustomMessageCode.DOCUMENT_IS_RESTORING.title
        assert (
            response_data["messageCode"] == CustomMessageCode.DOCUMENT_IS_RESTORING.code
        )


@pytest.mark.asyncio
async def test_restore_document_not_found(async_client, _headers_tenant_uuid):
    """Test error when document is not found"""
    # Arrange
    document_uuid = str(uuid.uuid4())
    version_id = int(time.time())

    mock_s3_client = MockS3Client()
    with patch(
        "gc_dentist_shared.core.common.s3_bucket.S3Client.get_instance",
        return_value=mock_s3_client,
    ):
        # Act
        response = await async_client.post(
            "/v1_0/s3/restore-document",
            headers=_headers_tenant_uuid,
            json={"document_uuid": document_uuid, "version_id": version_id},
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_data = response.json()
        assert response_data["success"] is False
        assert (
            response_data["message"]
            == CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.title
        )
        assert (
            response_data["messageCode"]
            == CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.code
        )


@pytest.mark.asyncio
async def test_restore_document_s3_error(
    async_client, _headers_tenant_uuid, setup_data
):
    """Test error handling when S3 client throws an exception"""
    # Arrange
    document_data = setup_data[0]
    document_uuid = document_data["document_uuid"]
    version_id = document_data["version_id"]

    # Create a mock S3 client that raises an exception on restore_object method
    mock_s3_client = MockS3Client()
    mock_s3_client.restore_object = AsyncMock(side_effect=S3BucketExceptionError())
    with patch(
        "gc_dentist_shared.core.common.s3_bucket.S3Client.get_instance",
        return_value=mock_s3_client,
    ):
        # Act
        response = await async_client.post(
            "/v1_0/s3/restore-document",
            headers=_headers_tenant_uuid,
            json={"document_uuid": document_uuid, "version_id": version_id},
        )

    # Assert
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_data = response.json()
    assert response_data["success"] is False
    assert response_data["message"] == CustomMessageCode.S3_BUCKET_ERROR.title
    assert response_data["messageCode"] == CustomMessageCode.S3_BUCKET_ERROR.code


@pytest.mark.asyncio
async def test_restore_document_unexpected_error(
    async_client, _headers_tenant_uuid, setup_data
):
    """Test handling of unexpected errors"""
    # Arrange
    document_data = setup_data[0]
    document_uuid = document_data["document_uuid"]
    version_id = document_data["version_id"]

    # Force an unexpected error in the service
    with patch(
        "services.restore_s3_data_service.S3DataRestorationService.validate_restoring_document"
    ) as mock_validate:
        mock_validate.side_effect = Exception("Unexpected test error")

        mock_s3_client = MockS3Client()
        with patch(
            "gc_dentist_shared.core.common.s3_bucket.S3Client.get_instance",
            return_value=mock_s3_client,
        ):
            # Act
            response = await async_client.post(
                "/v1_0/s3/restore-document",
                headers=_headers_tenant_uuid,
                json={"document_uuid": document_uuid, "version_id": version_id},
            )

    # Assert
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_data = response.json()
    assert response_data["success"] is False
    assert response_data["message"] == CustomMessageCode.RESTORE_S3_DATA_FAILED.title
    assert response_data["messageCode"] == CustomMessageCode.RESTORE_S3_DATA_FAILED.code
