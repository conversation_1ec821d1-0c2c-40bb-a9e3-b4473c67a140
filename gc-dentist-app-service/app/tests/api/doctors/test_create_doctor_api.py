import time
from unittest.mock import patch

import pytest
import pytest_asyncio
from configuration.settings import configuration
from fastapi import status
from sqlalchemy import text
from tests.helpers.enums.role_enum import UnittestRoleKeyEnum
from tests.helpers.insert_data.insert_doctor import (
    unittest_get_doctor_created_by_id,
    unittest_insert_doctor,
)
from tests.helpers.insert_data.insert_doctor_role import unittest_insert_doctor_role

from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.enums.role_enum import RoleKeyEnum
from gc_dentist_shared.core.logger.config import log


@pytest.fixture
def _headers(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }


@pytest_asyncio.fixture(scope="module")
async def admin_doctor_user_id(async_tenant_db_session_object, default_roles):
    async with async_tenant_db_session_object.begin():
        doctor_user_id = await unittest_insert_doctor(async_tenant_db_session_object)
        await unittest_insert_doctor_role(
            async_tenant_db_session_object,
            doctor_user_id,
            UnittestRoleKeyEnum.CLINIC_ADMIN.value,
        )
    return doctor_user_id


@pytest.fixture(autouse=True)
def setup_admin_user(mock_authentication, admin_doctor_user_id):
    """Automatically set admin user for all tests in this module"""
    mock_authentication(user_id=admin_doctor_user_id)


def get_payload(**overrides):
    default_payload = {
        "email": "<EMAIL>",
        "last_name": "test",
        "first_name": "test",
        "last_name_kana": "テスト",
        "first_name_kana": "テスト",
        "gender": 1,
        "date_of_birth": "1991-01-01",
        "prefecture_id": 1,
        "postal_code": "0600000",
        "address_1": "city test",
        "address_2": "street name and number test",
        "address_3": "house number test",
        "country_code": "+81",
        "phone": str(int(time.time() * 1e6))[-11:],
        "role_key_id": RoleKeyEnum.CLINIC_STAFF.value,
        "order_index": 0,
    }
    default_payload.update(overrides)

    return default_payload


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "payload,expected_status",
    [
        (get_payload(), 200),
    ],
)
async def test_create_doctor_success(
    async_client,
    async_tenant_db_session_object,
    _headers,
    payload,
    expected_status,
):
    response = await async_client.post("/v1_0/doctors", json=payload, headers=_headers)

    assert response.status_code == expected_status
    result = response.json()
    assert result["success"] is True

    data = result["data"]
    assert "doctor_user_id" in data

    info = await unittest_get_doctor_created_by_id(
        async_tenant_db_session_object, data["doctor_user_id"]
    )
    aes_gcm = AesGCMRotation(configuration)

    email_decrypt = aes_gcm.decrypt_data(info.email)
    birthday_decrypt = aes_gcm.decrypt_data(info.date_of_birth)
    phone_decrypt = aes_gcm.decrypt_data(info.phone)

    assert email_decrypt == payload["email"]
    assert birthday_decrypt == payload["date_of_birth"]
    assert phone_decrypt == payload["phone"]


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "payload",
    [
        (get_payload(first_name="")),
        (get_payload(last_name="")),
        (get_payload(country_code="")),
        (get_payload(phone="")),
        (get_payload(email="")),
        (get_payload(date_of_birth="")),
        (get_payload(gender="")),
        (get_payload(role_key_id="")),
        (get_payload(order_index="")),
    ],
)
async def test_create_doctor_error_required(async_client, _headers, payload):
    response = await async_client.post("/v1_0/doctors", json=payload, headers=_headers)

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "payload,info",
    [
        (get_payload(first_name_kana="invalid"), "error_format"),
        (get_payload(last_name_kana="invalid"), "error_format"),
        (get_payload(country_code="12345"), "error_length"),
        (get_payload(phone="invalid"), "error_attribute"),
        (get_payload(phone="123"), "error_length"),
        (get_payload(phone="091213121212121212"), "error_length"),
        (get_payload(email="invalid"), "error_format"),
        (get_payload(email="invalid@."), "error_format"),
        (get_payload(date_of_birth="12345"), "error_attribute"),
        (get_payload(date_of_birth="1995-12"), "error_format"),
        (get_payload(date_of_birth="12-01"), "error_format"),
        (get_payload(gender="10"), "error_invalid"),
        (get_payload(gender="invalid"), "error_invalid"),
        (get_payload(order_index="invalid"), "error_attribute"),
        (get_payload(order_index="-1"), "error_attribute"),
        (get_payload(prefecture_id="invalid"), "error_attribute"),
        (get_payload(postal_code="abca34324324234"), "error_length"),
    ],
)
async def test_create_doctor_error_format(async_client, _headers, payload, info):
    response = await async_client.post("/v1_0/doctors", json=payload, headers=_headers)

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.asyncio
async def test_create_doctor_error_exsits_phone(async_client, _headers):
    input_phone = str(int(time.time() * 1e6))[-11:]
    data_first = get_payload(phone=input_phone, email="<EMAIL>")
    response_first = await async_client.post(
        "/v1_0/doctors", json=data_first, headers=_headers
    )
    assert response_first.status_code == status.HTTP_200_OK

    data_second = get_payload(phone=input_phone, email="<EMAIL>")
    response_second = await async_client.post(
        "/v1_0/doctors", json=data_second, headers=_headers
    )

    assert response_second.status_code == status.HTTP_400_BAD_REQUEST
    assert response_second.json()["messageCode"] == 100003


@pytest.mark.asyncio
async def test_create_doctor_error_exsits_email(async_client, _headers):
    data_first = get_payload(phone="1233317891", email="<EMAIL>")
    response_first = await async_client.post(
        "/v1_0/doctors", json=data_first, headers=_headers
    )
    assert response_first.status_code == status.HTTP_200_OK

    data_second = get_payload(phone="0933327890", email="<EMAIL>")
    response_second = await async_client.post(
        "/v1_0/doctors", json=data_second, headers=_headers
    )

    assert response_second.status_code == status.HTTP_400_BAD_REQUEST
    assert response_second.json()["messageCode"] == 100002


@pytest.mark.asyncio
async def test_create_doctor_with_failed_password_exception(async_client, _headers):
    """Test general exception handling"""
    # Patch the service to throw a generic Exception
    with patch(
        "services.doctor_service.utils.generate_password",
        side_effect=Exception("Something went wrong"),
    ):
        data = get_payload(email="<EMAIL>")
        response = await async_client.post("/v1_0/doctors", headers=_headers, json=data)

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json()["messageCode"] == 100001


@pytest.mark.asyncio
async def test_validate_create_doctor_phone_check_raises(async_client, _headers):
    with patch(
        "services.doctor_service.DoctorService._check_exists_phone",
        side_effect=Exception("Something wrong"),
    ):
        payload = get_payload(email="<EMAIL>")
        response = await async_client.post(
            "/v1_0/doctors", headers=_headers, json=payload
        )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json()["messageCode"] == 100001


@pytest.mark.asyncio
async def test_validate_create_doctor_email_check_raises(async_client, _headers):
    with patch(
        "services.doctor_service.DoctorService._check_exists_email",
        side_effect=Exception("Something wrong"),
    ):
        payload = get_payload(email="<EMAIL>")
        response = await async_client.post(
            "/v1_0/doctors", headers=_headers, json=payload
        )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json()["messageCode"] == 100001


@pytest.mark.asyncio
async def test_create_doctor_internal_server_error(async_client, _headers):
    """Test general exception handling"""
    # Patch the service to throw a generic Exception
    with patch(
        "services.doctor_service.DoctorService.create",
        side_effect=Exception("Something went wrong"),
    ):
        data = get_payload(email="<EMAIL>")
        response = await async_client.post("/v1_0/doctors", headers=_headers, json=data)

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json()["messageCode"] == 100001


@pytest.mark.asyncio
async def test_cleanup(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        await async_tenant_db_session_object.execute(
            text(
                "TRUNCATE TABLE doctor_profiles, doctor_users RESTART IDENTITY CASCADE"
            )
        )

        log.info("✅ Cleanup test data done.")

    return True
