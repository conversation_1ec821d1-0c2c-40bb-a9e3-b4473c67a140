import time
import uuid
from unittest.mock import patch

import pytest
from configuration.settings import configuration
from fastapi import status
from tests.helpers.insert_data.insert_doctor import (
    unittest_get_doctor_created_by_id,
    unittest_insert_doctor,
    unittest_remove_doctor_by_ids,
)

from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation


@pytest.fixture(scope="module")
def _headers(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }


@pytest.fixture
def non_existent_doctor_id():
    """Return an ID that does not exist in the DB"""
    return 999999999


@pytest.fixture
def update_payload():
    """Return valid payload for updating doctor profile"""
    phone_number = str(int(time.time() * 1e6))[-11:]
    return {
        "first_name": "Updated",
        "last_name": "Doctor",
        "first_name_kana": "アップデート",
        "last_name_kana": "ドクター",
        "phone": phone_number,
        "email": str(uuid.uuid4()) + "@example.com",
        "gender": 1,
        "date_of_birth": "1990/01/01",
        "address_1": "city",
        "address_2": "street name and number",
        "address_3": "house number",
        "prefecture_id": 1,
        "postal_code": "1200000",
        "order_index": 0,
    }


@pytest.mark.asyncio
async def test_update_doctor_profile_success(
    async_client,
    _headers,
    async_tenant_db_session_object,
    update_payload,
):
    async with async_tenant_db_session_object.begin():
        doctor_user_id = await unittest_insert_doctor(async_tenant_db_session_object)

    response = await async_client.put(
        f"/v1_0/doctors/{doctor_user_id}",
        json=update_payload,
        headers=_headers,
    )
    assert response.status_code == status.HTTP_200_OK
    assert response.json()["success"] is True

    info = await unittest_get_doctor_created_by_id(
        async_tenant_db_session_object, doctor_user_id
    )
    aes_gcm = AesGCMRotation(configuration=configuration)

    email_decrypt = aes_gcm.decrypt_data(info.email)
    birthday_decrypt = aes_gcm.decrypt_data(info.date_of_birth)
    phone_decrypt = aes_gcm.decrypt_data(info.phone)

    assert email_decrypt == update_payload["email"]
    assert birthday_decrypt == update_payload["date_of_birth"]
    assert phone_decrypt == update_payload["phone"]

    async with async_tenant_db_session_object.begin():
        await unittest_remove_doctor_by_ids(
            async_tenant_db_session_object, doctor_ids=[doctor_user_id]
        )


@pytest.mark.asyncio
async def test_update_doctor_profile_not_found(
    async_client, _headers, update_payload, non_existent_doctor_id
):
    response = await async_client.put(
        f"/v1_0/doctors/{non_existent_doctor_id}",
        json=update_payload,
        headers=_headers,
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST


@pytest.mark.asyncio
async def test_update_doctor_profile_empty_payload(
    async_client, _headers, async_tenant_db_session_object
):
    async with async_tenant_db_session_object.begin():
        doctor_user_id = await unittest_insert_doctor(async_tenant_db_session_object)

    response_empty = await async_client.put(
        f"/v1_0/doctors/{doctor_user_id}",
        json={},
        headers=_headers,
    )
    assert response_empty.status_code == status.HTTP_200_OK

    async with async_tenant_db_session_object.begin():
        await unittest_remove_doctor_by_ids(
            async_tenant_db_session_object, doctor_ids=[doctor_user_id]
        )


@pytest.mark.asyncio
async def test_update_doctor_profile_internal_error(
    async_client, _headers, update_payload
):
    with patch(
        "services.doctor_service.DoctorService.update_doctor_profile",
        side_effect=Exception("Something went wrong"),
    ):
        response = await async_client.put(
            "/v1_0/doctors/1",
            json=update_payload,
            headers=_headers,
        )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json()["messageCode"] == 100006


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "param_data, expected_status",
    [
        ({"email": "testexample.com"}, 422),
        ({"email": "test@examplecom"}, 422),
        ({"email": "invalid-email"}, 422),
        ({"phone": "123456789"}, 422),
        ({"phone": "123456789012"}, 422),
        ({"phone": "invalid-phone"}, 422),
    ],
)
async def test_validate_parameter(
    async_client,
    _headers,
    param_data,
    expected_status,
    async_tenant_db_session_object,
):
    async with async_tenant_db_session_object.begin():
        doctor_user_id = await unittest_insert_doctor(async_tenant_db_session_object)

    response = await async_client.put(
        f"/v1_0/doctors/{doctor_user_id}", headers=_headers, json=param_data
    )
    assert response.status_code == expected_status

    async with async_tenant_db_session_object.begin():
        await unittest_remove_doctor_by_ids(
            async_tenant_db_session_object, doctor_ids=[doctor_user_id]
        )


@pytest.mark.asyncio
async def test_update_doctor_email_exists(
    async_client, _headers, async_tenant_db_session_object
):
    async with async_tenant_db_session_object.begin():
        doctor_id_1 = await unittest_insert_doctor(
            async_tenant_db_session_object,
            custom_user_fields={"username": "<EMAIL>"},
        )
        doctor_id_2 = await unittest_insert_doctor(
            async_tenant_db_session_object,
            custom_user_fields={"username": "<EMAIL>"},
        )

    response = await async_client.put(
        f"/v1_0/doctors/{doctor_id_2}",
        json={"email": "<EMAIL>"},
        headers=_headers,
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST

    async with async_tenant_db_session_object.begin():
        await unittest_remove_doctor_by_ids(
            async_tenant_db_session_object,
            doctor_ids=[doctor_id_1, doctor_id_2],
        )
