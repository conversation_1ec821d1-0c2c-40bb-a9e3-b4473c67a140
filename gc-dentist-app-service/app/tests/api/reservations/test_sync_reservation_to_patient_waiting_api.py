from datetime import datetime, timed<PERSON>ta
from unittest.mock import patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from enums.reservation_status_enum import ReservationStatusEnum
from enums.reservation_system_enum import ReservationSystemEnum
from fastapi import status
from sqlalchemy import text
from sqlalchemy.exc import OperationalError
from tests.helpers.insert_data.insert_patient import unittest_insert_patient
from tests.helpers.insert_data.insert_reservation import (
    unittest_insert_reservation,
    unittest_update_status_reservation,
)

from gc_dentist_shared.core.logger.config import log


@pytest.fixture(scope="module")
def _headers(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }


@pytest_asyncio.fixture(scope="module")
async def _setup_create_patient(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        patients = [
            await unittest_insert_patient(async_tenant_db_session_object)
            for _ in range(3)
        ]

    return patients


@pytest_asyncio.fixture(scope="module")
async def _setup_create_reservation(
    async_tenant_db_session_object, _setup_create_patient
):
    patients = _setup_create_patient
    list_reservation = []

    async with async_tenant_db_session_object.begin():
        for patient_id in patients:
            reservation = await unittest_insert_reservation(
                async_tenant_db_session_object,
                custom_fields={"patient_user_id": patient_id},
            )
            list_reservation.append(reservation)

    return list_reservation


@pytest.mark.asyncio
async def test_sync_data_status_booked(
    async_client, _headers, _setup_create_reservation
):
    count_list = len(_setup_create_reservation)
    response = await async_client.post(
        "/v1_0/reservations/sync-to-patient-waiting", json={}, headers=_headers
    )

    assert response.status_code == status.HTTP_200_OK
    result = response.json()

    assert result["success"] is True

    count_data = result.get("data", {}).get("count_data")

    assert count_data == count_list


@pytest.mark.asyncio
async def test_sync_data_exists_status_canceled(
    async_client, _headers, async_tenant_db_session_object, _setup_create_reservation
):
    count_list = len(_setup_create_reservation)
    first_item = _setup_create_reservation[0]

    async with async_tenant_db_session_object.begin():
        await unittest_update_status_reservation(
            async_tenant_db_session_object,
            first_item.id,
            ReservationStatusEnum.STATUS_CANCELED.value,
        )

    response = await async_client.post(
        "/v1_0/reservations/sync-to-patient-waiting", json={}, headers=_headers
    )

    assert response.status_code == status.HTTP_200_OK
    result = response.json()

    assert result["success"] is True

    count_data = result.get("data", {}).get("count_data")

    assert count_data == count_list


@pytest.mark.asyncio
async def test_not_sync_new_data_with_status_canceled(
    async_client, _headers, async_tenant_db_session_object, _setup_create_reservation
):
    list_reservation = _setup_create_reservation
    async with async_tenant_db_session_object.begin():
        patient_id = await unittest_insert_patient(async_tenant_db_session_object)

        reservation = await unittest_insert_reservation(
            async_tenant_db_session_object,
            custom_fields={
                "patient_user_id": patient_id,
                "status": ReservationStatusEnum.STATUS_CANCELED.value,
            },
        )

        list_reservation.append(reservation)

    count_list = len(list_reservation)

    response = await async_client.post(
        "/v1_0/reservations/sync-to-patient-waiting", json={}, headers=_headers
    )

    assert response.status_code == status.HTTP_200_OK
    result = response.json()

    assert result["success"] is True

    count_data = result.get("data", {}).get("count_data")

    assert count_data != count_list


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "params,status_code",
    [
        (
            {"target_date": (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")},
            200,
        ),
        ({"target_date": "2023-12"}, 422),
        ({"source": "test"}, 422),
        (
            {
                "target_date": (datetime.now() + timedelta(days=1)).strftime(
                    "%Y-%m-%d"
                ),
                "status": ReservationSystemEnum.IAPO.value,
            },
            200,
        ),
    ],
)
@pytest.mark.asyncio
async def test_sync_data_with_params(async_client, _headers, params, status_code):
    response = await async_client.post(
        "/v1_0/reservations/sync-to-patient-waiting", json=params, headers=_headers
    )

    assert response.status_code == status_code
    result = response.json()

    if status_code == status.HTTP_200_OK:
        count_data = result.get("data", {}).get("count_data")

        assert count_data == 0


@pytest.mark.asyncio
async def test_sync_data_failed(async_client, _headers):
    with patch(
        "api.v1.reservations.reservation_api.PatientWaitingService.sync_data_from_reservation"
    ) as mock_method:
        mock_method.side_effect = OperationalError("Database error", None, None)
        response = await async_client.post(
            "/v1_0/reservations/sync-to-patient-waiting", json={}, headers=_headers
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        result = response.json()
        assert result["success"] is False
        assert (
            result["messageCode"]
            == CustomMessageCode.RESERVATION_SYNC_TO_PATIENT_WAITING.code
        )


@pytest.mark.asyncio
async def test_cleanup(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        await async_tenant_db_session_object.execute(
            text(
                "TRUNCATE TABLE medical_histories, patient_waitings, reservations, "
                "patient_profiles, patient_users RESTART IDENTITY CASCADE"
            )
        )
        log.info("✅ Cleanup test data done.")

    return True
