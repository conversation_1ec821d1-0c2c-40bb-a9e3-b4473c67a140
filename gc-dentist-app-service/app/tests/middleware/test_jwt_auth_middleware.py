import uuid
from contextlib import AsyncExitStack
from datetime import datetime
from unittest.mock import patch

import pytest
import pytest_asyncio
from fastapi import status
from httpx import ASGITransport, AsyncClient
from tests.helpers.enums.role_enum import UnittestRoleKeyEnum
from tests.helpers.insert_data.insert_doctor import unittest_insert_doctor
from tests.helpers.insert_data.insert_doctor_role import unittest_insert_doctor_role
from tests.helpers.insert_data.insert_oauth_client import unittest_insert_oauth_client
from tests.helpers.insert_data.insert_oauth_token import (
    unittest_create_jwt_token,
    unittest_insert_oauth_token,
)
from tests.helpers.jwks_mock import MockJWKSClient
from werkzeug.security import gen_salt

from gc_dentist_shared.core.common.digital_signature import DigitalSignature
from gc_dentist_shared.core.constants import AUTHORIZATION_HEADER, X_TENANT_UUID
from gc_dentist_shared.core.messages import CustomMessageCode


class TestJWTAuthMiddleware:
    pytestmark = pytest.mark.skip_middleware_auth

    @pytest.fixture
    def app_local(self):
        from main import create_app

        app = create_app()

        @app.get("/public")
        def public_endpoint():
            return {"message": "success"}

        @app.get("/protected")
        def protected_endpoint():
            return {"message": "success"}

        return app

    @pytest_asyncio.fixture
    async def async_client_local(self, app_local):
        transport = ASGITransport(app=app_local, raise_app_exceptions=False)
        async with AsyncClient(transport=transport, base_url="http://test") as ac:
            yield ac

    @pytest.fixture(autouse=True)
    def mock_identify_tenant_middleware(self):
        async def mock_dispatch(self, request, call_next):
            return await call_next(request)

        with patch(
            "configuration.middleware.identify_tenant_middleware.IdentifyTenantMiddleware.dispatch",
            mock_dispatch,
        ):
            yield

    @pytest_asyncio.fixture(scope="class")
    def rsa_key_data(self):
        rsa_key_pair = DigitalSignature.create_rsa_key_pair()
        return {
            "key_id": f"key_{datetime.now().strftime('%Y%m%d')}",
            "private_key": rsa_key_pair["private_key"].encode(),
            "public_key": rsa_key_pair["public_key"].encode(),
        }

    @pytest_asyncio.fixture(scope="class")
    async def setup_data(
        self,
        async_central_db_session_object,
        async_tenant_db_session_object,
        tenant_uuid,
        rsa_key_data,
    ):
        async with async_tenant_db_session_object.begin():
            doctor_user_id_1 = await unittest_insert_doctor(
                async_tenant_db_session_object
            )
            await unittest_insert_doctor_role(
                async_tenant_db_session_object,
                doctor_user_id_1,
                UnittestRoleKeyEnum.CLINIC_ADMIN,
            )

            doctor_user_id_2 = await unittest_insert_doctor(
                async_tenant_db_session_object
            )
            await unittest_insert_doctor_role(
                async_tenant_db_session_object,
                doctor_user_id_1,
                UnittestRoleKeyEnum.CLINIC_ADMIN,
                delete_flag=True,
            )

            doctor_user_id_3 = await unittest_insert_doctor(
                async_tenant_db_session_object, custom_user_fields={"status": False}
            )
            await unittest_insert_doctor_role(
                async_tenant_db_session_object,
                doctor_user_id_3,
                UnittestRoleKeyEnum.CLINIC_ADMIN,
            )

        async with async_central_db_session_object.begin():
            internal_oauth_client = await unittest_insert_oauth_client(
                async_central_db_session_object
            )
            valid_token = await unittest_insert_oauth_token(
                async_central_db_session_object,
                internal_client=internal_oauth_client,
                rsa_key_pair=rsa_key_data,
                doctor_user_id=doctor_user_id_1,
                tenant_uuid=tenant_uuid,
                role_key_ids=[UnittestRoleKeyEnum.CLINIC_ADMIN.value],
            )

            expired_token = await unittest_insert_oauth_token(
                async_central_db_session_object,
                internal_client=internal_oauth_client,
                rsa_key_pair=rsa_key_data,
                doctor_user_id=doctor_user_id_1,
                tenant_uuid=tenant_uuid,
                role_key_ids=[UnittestRoleKeyEnum.CLINIC_ADMIN.value],
                token_expire=-5,
            )

            invalid_role_token = await unittest_insert_oauth_token(
                async_central_db_session_object,
                internal_client=internal_oauth_client,
                rsa_key_pair=rsa_key_data,
                doctor_user_id=doctor_user_id_2,
                tenant_uuid=tenant_uuid,
                role_key_ids=[UnittestRoleKeyEnum.CLINIC_ADMIN.value],
            )

            invalid_user_token = await unittest_insert_oauth_token(
                async_central_db_session_object,
                internal_client=internal_oauth_client,
                rsa_key_pair=rsa_key_data,
                doctor_user_id=doctor_user_id_3,
                tenant_uuid=tenant_uuid,
                role_key_ids=[UnittestRoleKeyEnum.CLINIC_ADMIN.value],
            )

        return {
            "internal_oauth_client": internal_oauth_client,
            "doctor_user_id_1": doctor_user_id_1,
            "valid_token": valid_token,
            "expired_token": expired_token,
            "invalid_role_token": invalid_role_token,
            "invalid_user_token": invalid_user_token,
        }

    def get_headers(self, tenant_uuid: str, token_type: str, access_token: str) -> dict:
        headers = {
            X_TENANT_UUID: tenant_uuid,
            AUTHORIZATION_HEADER: f"{token_type} {access_token}",
        }
        return headers

    """ TEST CASES """

    @pytest.mark.asyncio
    async def test_public_endpoint(
        self,
        async_client_local,
    ):
        with patch(
            "configuration.settings.configuration.TOKEN_EXCLUDE_URLS", ["/public"]
        ):
            response = await async_client_local.get("/public", headers={})
            assert response.status_code == status.HTTP_200_OK
            assert response.json() == {"message": "success"}

    @pytest.mark.asyncio
    async def test_authentication_success(
        self,
        async_client_local,
        async_central_db_session_object,
        async_tenant_db_session_object,
        setup_data,
        tenant_uuid,
        default_roles,
        rsa_key_data,
    ):
        valid_token = setup_data["valid_token"]
        headers = self.get_headers(
            tenant_uuid,
            token_type=valid_token["token_type"],
            access_token=valid_token["access_token"],
        )

        jwks_client_mock = MockJWKSClient(rsa_key_data["public_key"])
        with patch(
            "gc_dentist_shared.core.common.jwks_client_manager.JWKSClientManager.get_instance",
            return_value=jwks_client_mock,
        ):
            response = await async_client_local.get("/protected", headers=headers)

            assert response.status_code == status.HTTP_200_OK
            assert response.json() == {"message": "success"}

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "test_id, headers_func, expected_code, expected_message",
        [
            # Missing Authentication Header
            (
                "missing_auth_header",
                lambda self, setup_data, tenant_uuid: {
                    X_TENANT_UUID: tenant_uuid,
                },
                CustomMessageCode.UNAUTHORIZED_ERROR.code,
                CustomMessageCode.UNAUTHORIZED_ERROR.title,
            ),
            # Invalid Authorization Header
            (
                "invalid_token_format",
                lambda self, setup_data, tenant_uuid: {
                    "Authorization": "InvalidFormat",
                    X_TENANT_UUID: tenant_uuid,
                },
                CustomMessageCode.UNAUTHORIZED_ERROR.code,
                CustomMessageCode.UNAUTHORIZED_ERROR.title,
            ),
            # Invalid Token Type
            (
                "invalid_token_type",
                lambda self, setup_data, tenant_uuid: {
                    AUTHORIZATION_HEADER: f"{gen_salt(5)} {setup_data['valid_token']['access_token']}",
                    X_TENANT_UUID: tenant_uuid,
                },
                CustomMessageCode.UNAUTHORIZED_ERROR.code,
                CustomMessageCode.UNAUTHORIZED_ERROR.title,
            ),
            # Invalid Access Token Data
            (
                "invalid_access_token",
                lambda self, setup_data, tenant_uuid: {
                    AUTHORIZATION_HEADER: f"{setup_data['valid_token']['token_type']} {gen_salt(48)}",
                    X_TENANT_UUID: tenant_uuid,
                },
                CustomMessageCode.UNAUTHORIZED_ERROR.code,
                CustomMessageCode.UNAUTHORIZED_ERROR.title,
            ),
            # Access Token Expired
            (
                "expired_access_token",
                lambda self, setup_data, tenant_uuid: {
                    AUTHORIZATION_HEADER: (
                        f"{setup_data['expired_token']['token_type']} "
                        f"{setup_data['expired_token']['access_token']}"
                    ),
                    X_TENANT_UUID: tenant_uuid,
                },
                CustomMessageCode.TOKEN_EXPIRED.code,
                CustomMessageCode.TOKEN_EXPIRED.title,
            ),
            # Invalid Role Key
            (
                "invalid_role_key",
                lambda self, setup_data, tenant_uuid: {
                    AUTHORIZATION_HEADER: (
                        f"{setup_data['invalid_role_token']['token_type']} "
                        f"{setup_data['invalid_role_token']['access_token']}"
                    ),
                    X_TENANT_UUID: tenant_uuid,
                },
                CustomMessageCode.UNAUTHORIZED_ERROR.code,
                CustomMessageCode.UNAUTHORIZED_ERROR.title,
            ),
            # Invalid Doctor User
            (
                "invalid_doctor_user",
                lambda self, setup_data, tenant_uuid: {
                    AUTHORIZATION_HEADER: (
                        f"{setup_data['invalid_user_token']['token_type']} "
                        f"{setup_data['invalid_user_token']['access_token']}"
                    ),
                    X_TENANT_UUID: tenant_uuid,
                },
                CustomMessageCode.UNAUTHORIZED_ERROR.code,
                CustomMessageCode.UNAUTHORIZED_ERROR.title,
            ),
        ],
    )
    async def test_authentication_failures(
        self,
        test_id,
        headers_func,
        expected_code,
        expected_message,
        async_client_local,
        setup_data,
        async_central_db_session_object,
        async_tenant_db_session_object,
        tenant_uuid,
        rsa_key_data,
    ):
        headers = headers_func(self, setup_data, tenant_uuid)

        jwks_client_mock = MockJWKSClient(rsa_key_data["public_key"])
        with patch(
            "gc_dentist_shared.core.common.jwks_client_manager.JWKSClientManager.get_instance",
            return_value=jwks_client_mock,
        ):
            response = await async_client_local.get("/protected", headers=headers)

            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            result = response.json()
            assert result["msg"] == expected_message
            assert result["msg_code"] == expected_code

    @pytest.mark.asyncio
    async def test_authentication_invalid_db_name(
        self,
        async_client_local,
        async_central_db_session_object,
        setup_data,
        tenant_uuid,
        rsa_key_data,
    ):
        # Invalid tenant db name (invalid_tenant_uuid)
        invalid_tenant_uuid = str(uuid.uuid4())

        async with async_central_db_session_object.begin():
            invalid_tenant_token = await unittest_insert_oauth_token(
                async_central_db_session_object=async_central_db_session_object,
                internal_client=setup_data["internal_oauth_client"],
                rsa_key_pair=rsa_key_data,
                doctor_user_id=setup_data["doctor_user_id_1"],
                tenant_uuid=invalid_tenant_uuid,
                role_key_ids=[UnittestRoleKeyEnum.CLINIC_ADMIN.value],
            )

        headers = self.get_headers(
            invalid_tenant_uuid,
            token_type=invalid_tenant_token["token_type"],
            access_token=invalid_tenant_token["access_token"],
        )

        jwks_client_mock = MockJWKSClient(rsa_key_data["public_key"])
        with patch(
            "gc_dentist_shared.core.common.jwks_client_manager.JWKSClientManager.get_instance",
            return_value=jwks_client_mock,
        ):
            response = await async_client_local.get("/protected", headers=headers)

            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            result = response.json()
            assert result["msg"] == CustomMessageCode.UNAUTHORIZED_ERROR.title
            assert result["msg_code"] == CustomMessageCode.UNAUTHORIZED_ERROR.code

    @pytest.mark.asyncio
    async def test_authentication_with_valid_jwt_not_in_db(
        self,
        async_client_local,
        async_central_db_session_object,
        async_tenant_db_session_object,
        setup_data,
        tenant_uuid,
        rsa_key_data,
    ):
        # Valid JWT Token but does not exist in DB
        jwt_token = unittest_create_jwt_token(
            user_id=setup_data["doctor_user_id_1"],
            tenant_uuid=tenant_uuid,
            role_key_ids=[UnittestRoleKeyEnum.CLINIC_ADMIN.value],
            internal_client=setup_data["internal_oauth_client"],
            token_expire=10,
            rsa_key_pair=rsa_key_data,
        )

        headers = self.get_headers(
            tenant_uuid,
            token_type=jwt_token["token_type"],
            access_token=jwt_token["access_token"],
        )

        jwks_client_mock = MockJWKSClient(rsa_key_data["public_key"])
        with patch(
            "gc_dentist_shared.core.common.jwks_client_manager.JWKSClientManager.get_instance",
            return_value=jwks_client_mock,
        ):
            response = await async_client_local.get("/protected", headers=headers)

            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            result = response.json()
            assert result["msg"] == CustomMessageCode.UNAUTHORIZED_ERROR.title
            assert result["msg_code"] == CustomMessageCode.UNAUTHORIZED_ERROR.code

    @pytest.mark.asyncio
    async def test_authentication_invalid_public_key(
        self,
        async_client_local,
        setup_data,
        async_central_db_session_object,
        async_tenant_db_session_object,
        tenant_uuid,
        rsa_key_data,
    ):
        headers = self.get_headers(
            tenant_uuid,
            token_type=setup_data["valid_token"]["token_type"],
            access_token=setup_data["valid_token"]["access_token"],
        )
        jwks_client_mock = MockJWKSClient("invalid_public_key")
        with patch(
            "gc_dentist_shared.core.common.jwks_client_manager.JWKSClientManager.get_instance",
            return_value=jwks_client_mock,
        ):
            response = await async_client_local.get("/protected", headers=headers)

            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            result = response.json()
            assert result["msg"] == CustomMessageCode.UNAUTHORIZED_ERROR.title
            assert result["msg_code"] == CustomMessageCode.UNAUTHORIZED_ERROR.code

    @pytest.mark.asyncio
    @pytest.mark.parametrize(
        "test_id, patch_configs",
        [
            (
                "tenant_db_error",
                [
                    patch(
                        "db.db_connection.TenantDatabase.get_instance_tenant_db",
                        side_effect=Exception("Database connection error"),
                    )
                ],
            ),
            (
                "central_db_error",
                [
                    patch(
                        "db.db_connection.CentralDatabase.get_instance_db",
                        side_effect=Exception("Database connection error"),
                    )
                ],
            ),
        ],
    )
    async def test_authentication_case_exceptions(
        self,
        test_id,
        patch_configs,
        async_client_local,
        async_central_db_session_object,
        setup_data,
        tenant_uuid,
        rsa_key_data,
    ):

        headers = self.get_headers(
            tenant_uuid,
            token_type=setup_data["valid_token"]["token_type"],
            access_token=setup_data["valid_token"]["access_token"],
        )

        jwks_client_mock = MockJWKSClient(rsa_key_data["public_key"])

        async with AsyncExitStack() as stack:
            stack.enter_context(
                patch(
                    "gc_dentist_shared.core.common.jwks_client_manager.JWKSClientManager.get_instance",
                    return_value=jwks_client_mock,
                )
            )

            for patch_config in patch_configs:
                stack.enter_context(patch_config)

            response = await async_client_local.get("/protected", headers=headers)

            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            result = response.json()
            assert result["msg"] == CustomMessageCode.UNAUTHORIZED_ERROR.title
            assert result["msg_code"] == CustomMessageCode.UNAUTHORIZED_ERROR.code
