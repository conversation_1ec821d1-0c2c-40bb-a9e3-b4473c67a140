import json
from typing import Op<PERSON>

from configuration.settings import configuration
from core.messages import CustomMessage<PERSON><PERSON>
from db.db_connection import TenantDatabase
from enums.opa_enum import OPACondition
from enums.permission_enum import PermissionRedisKey
from fastapi import Depends, Request, status
from sqlalchemy import select

from gc_dentist_shared.core.common.http_request import <PERSON>ton<PERSON><PERSON><PERSON><PERSON><PERSON>
from gc_dentist_shared.core.common.redis import <PERSON>is<PERSON><PERSON>
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models.doctor_roles import <PERSON><PERSON><PERSON>
from gc_dentist_shared.tenant_models.permissions import Permission
from gc_dentist_shared.tenant_models.role_permissions import RolePermission


class RolePermissionDependWithOPA:
    def __init__(
        self, permission_keys: list[str], condition: str = OPACondition.ANY.value
    ):
        self.permission_keys = permission_keys
        self.condition = condition

    async def __call__(self, request: Request):
        """
        Validates user permissions for a requested action using Open Policy Agent (OPA).

        This method supports two types of permission checks:
        1. ALL Condition: Requires ALL requested permissions to match
        2. ANY Condition: Requires AT LEAST ONE requested permission to match

        Parameters:
            permission_keys (list): List of permission keys to check
            condition (str, optional): Permission matching condition
                - 'ALL': All requested permissions must exist (default)
                - 'ANY': At least one requested permission must exist

        Examples:
            # ANY Condition (Strict Match)
            Input: {
                "input": {
                    "all_permissions": {
                        "role_scope": 1,
                        "permissions": [
                            "Dashboard:VIEW",
                            "Companies.ListCompany:VIEW",
                            "Companies.CreateCompany:CREATE"
                        ]
                    },
                    "requested_permission_key": {
                        "role_scope": 1,
                        "permissions": [
                            "Dashboard:VIEW",
                            "Dashboard:CREATE"
                        ],
                        "condition": "ALL"
                    }
                }
            }
            # Result: False (not all requested permissions exist)

            # OR Condition (Relaxed Match)
            Input: {
                "input": {
                    "all_permissions": {
                        "role_scope": 1,
                        "permissions": [
                            "Dashboard:VIEW",
                            "Companies.ListCompany:VIEW",
                            "Companies.CreateCompany:CREATE"
                        ]
                    },
                    "requested_permission_key": {
                        "role_scope": 1,
                        "permissions": [
                            "Dashboard:VIEW",
                            "Dashboard:CREATE"
                        ],
                        "condition": "ANY"
                    }
                }
            }
            # Result: True (at least one requested permission exists)

        Returns:
            dict: OPA decision result with 'decision_id' and 'result' boolean
            - 'result': True if permission is granted, False otherwise
        """

        if not self.permission_keys:
            raise CustomValueError(
                status_code=status.HTTP_403_FORBIDDEN,
                message_code=CustomMessageCode.FORBIDDEN_ERROR.code,
                message=CustomMessageCode.FORBIDDEN_ERROR.title,
            )

        user_id = request.user.id
        if not user_id:
            raise CustomValueError(
                status_code=status.HTTP_403_FORBIDDEN,
                message_code=CustomMessageCode.FORBIDDEN_ERROR.code,
                message=CustomMessageCode.FORBIDDEN_ERROR.title,
            )

        permission_request_data = await self._build_permission_request(user_id=user_id)

        response = await self._check_permissions_with_opa(permission_request_data)

        if not response.get("result", False):  # Simplified validation condition
            raise CustomValueError(
                status_code=status.HTTP_403_FORBIDDEN,
                message_code=CustomMessageCode.FORBIDDEN_ERROR.code,
                message=CustomMessageCode.FORBIDDEN_ERROR.title,
            )

    async def _get_cache_key_for_user_role_permissions(self, user_id: int) -> str:
        """Generate cache key for specific user's role permissions"""
        cache_key = f"{PermissionRedisKey.PERMISSION_CACHE_PREFIX.value}:{user_id}"
        return cache_key

    async def _get_cached_user_role_permissions(self, cache_key: str) -> dict | None:
        """Get cached permissions from Redis"""
        try:
            redis_client = await RedisCli.get_instance(configuration)
            cached_data = await redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            return None
        except Exception as e:
            log.error(f"❌ Exception get_cached_user_role_permissions: {e}")
            return None

    async def _set_cached_permissions(
        self, cache_key: str, permissions_data: list, ttl: int = 3600
    ):
        """Cache permissions data in Redis with TTL (default 60 minutes)"""
        try:
            redis_client = await RedisCli.get_instance(configuration)
            await redis_client.setex(cache_key, ttl, json.dumps(permissions_data))
            log.info(f"✅ Cached permissions for user: {cache_key}")
        except Exception as e:
            log.error(f"❌ Exception set_cached_permissions: {e}")
            return None

    async def _build_permission_data_from_role_permissions(self, permissions):
        """Build permission_data from role_permissions to avoid code duplication"""

        all_permissions = {
            "permissions": permissions,
        }
        requested_permission_key = {
            "permissions": [permission_key for permission_key in self.permission_keys],
            "condition": self.condition,
        }

        permission_data = {
            "input": {
                "all_permissions": all_permissions,
                "requested_permission_key": requested_permission_key,
            }
        }

        return permission_data

    async def _build_permission_request(self, user_id: int):
        cache_key = await self._get_cache_key_for_user_role_permissions(user_id)
        cached_user_role_permissions = await self._get_cached_user_role_permissions(
            cache_key
        )

        if cached_user_role_permissions:
            # Use cached role_permissions to build permission request
            return await self._build_permission_data_from_role_permissions(
                cached_user_role_permissions
            )

        # If not in cache, query database
        async with TenantDatabase().get_instance_tenant_db() as db_session:
            # Step 1: Get roles from doctor_roles for specific user
            user_roles_result = await db_session.execute(
                select(DoctorRole).where(
                    DoctorRole.doctor_user_id == user_id,
                    DoctorRole.delete_flag.is_(False),
                )
            )
            user_roles = user_roles_result.scalars().all()

            if not user_roles:
                raise CustomValueError(
                    status_code=status.HTTP_403_FORBIDDEN,
                    message_code=CustomMessageCode.FORBIDDEN_ERROR.code,
                    message=CustomMessageCode.FORBIDDEN_ERROR.title,
                )

            # Step 2: Get role permissions from the user's roles
            role_key_ids = [role.role_key_id for role in user_roles]
            role_permissions_result = await db_session.execute(
                select(Permission.permission_key)
                .select_from(RolePermission)
                .join(
                    Permission,
                    RolePermission.permission_id == Permission.id,
                )
                .where(
                    RolePermission.role_key_id.in_(role_key_ids),
                    RolePermission.delete_flag.is_(False),
                    Permission.delete_flag.is_(False),
                )
            )

            role_permissions = role_permissions_result.mappings().all()

            if not role_permissions:
                raise CustomValueError(
                    status_code=status.HTTP_403_FORBIDDEN,
                    message_code=CustomMessageCode.FORBIDDEN_ERROR.code,
                    message=CustomMessageCode.FORBIDDEN_ERROR.title,
                )

            permission_keys = [row.permission_key for row in role_permissions]

            # Cache only the permission_keys for this user
            await self._set_cached_permissions(cache_key, permission_keys)

            # Build permission_data using the same helper method
            return await self._build_permission_data_from_role_permissions(
                permission_keys
            )

    async def _check_permissions_with_opa(self, obj_request) -> dict:
        opa_service_base_url = configuration.OPA_SERVICE_BASE_URL
        opa_service_api_version = "/v1/data/"
        opa_noda_permission = "noda/permission/allow"
        url = opa_service_base_url + opa_service_api_version + opa_noda_permission

        api_client = await SingletonRequestApi.get_instance()

        try:
            status_code, response_data = await api_client.request(
                method="POST",
                url=url,
                json=obj_request,
            )
            log.info(
                f"📡 OPA Response: status_code={status_code}, response={response_data}"
            )

        except Exception as e:
            log.error(f"❌  OPA Connection Error: {str(e)}")
            raise CustomValueError(
                message_code=CustomMessageCode.CALL_OPA_ERROR.code,
                message=CustomMessageCode.CALL_OPA_ERROR.title,
            )

        if status_code != status.HTTP_200_OK or not response_data:
            log.error(f"❌  Call Opa Error: str{response_data}")
            raise CustomValueError(
                message_code=CustomMessageCode.CALL_OPA_ERROR.code,
                message=CustomMessageCode.CALL_OPA_ERROR.title,
            )

        return response_data


def DependsPermissionWithOPA(
    permission_keys: Optional[list[str]] = None,
    condition: str | None = OPACondition.ANY.value,
):
    if permission_keys is None:
        permission_keys = []
    return Depends(
        RolePermissionDependWithOPA(
            [p.value if hasattr(p, "value") else p for p in permission_keys],
            condition=condition,
        )
    )
