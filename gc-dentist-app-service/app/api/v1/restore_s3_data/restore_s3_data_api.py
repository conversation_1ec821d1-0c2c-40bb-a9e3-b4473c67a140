from typing import Annotated

from configuration.settings import configuration
from core.common.api_response import Api<PERSON><PERSON>ponse
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase, TenantDatabase
from fastapi import APIRouter, Depends, status
from fastapi_pagination import Page, Params
from schemas.requests.restore_s3_data_schema import (
    DeleteExpiredRestoredDataRequest,
    RestoreS3DocumentRequest,
    SearchRestoreS3DataRequest,
    UpdateRestoredStatusRequest,
)
from schemas.responses.restore_s3_data_schema import RestoreS3DataResponse
from services.restore_s3_data_service import S3DataRestorationService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common.cloudfront import CloudFront
from gc_dentist_shared.core.common.s3_bucket import S3Client
from gc_dentist_shared.core.constants import LambdaXRequestValue
from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.dependencies.api_verify_key_depend import (
    DependsAPIVerifyKey,
)
from gc_dentist_shared.core.exception_handler.custom_exception import (
    CustomValueError,
    S3BucketExceptionError,
)
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post("/restore-document")
@version(1, 0)
@measure_time
async def restore_document_from_s3(
    request_data: RestoreS3DocumentRequest,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    """
    Initiates restoration of archived document files from S3 Glacier or S3 Intelligent.
    """
    try:
        s3_client = await S3Client.get_instance(configuration=configuration)
        restore_service = S3DataRestorationService(
            tenant_db_session=db_session, s3_client=s3_client
        )
        await restore_service.restore_document_from_s3(request_data)
        return ApiResponse.success(
            message=CustomMessageCode.RESTORE_S3_DATA_SUCCESS.title,
        )

    except (S3BucketExceptionError, CustomValueError) as e:
        log.error(f"❌ Document restoration error: {str(e)}")
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )

    except Exception as e:
        log.error(f"❌ Document restoration unexpedted error: {str(e)}")
        return ApiResponse.error(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=CustomMessageCode.RESTORE_S3_DATA_FAILED.title,
            message_code=CustomMessageCode.RESTORE_S3_DATA_FAILED.code,
        )


@router.post(
    path="/update-restored-status",
    summary="[For Lambda] Update Restore S3 Status",
    dependencies=[
        DependsAPIVerifyKey(
            configuration.LAMBDA_SECRET_KEY,
            LambdaXRequestValue.LAMBDA_UPDATE_RESTORED_STATUS,
        )
    ],
)
@version(1, 0)
@measure_time
async def update_restored_status(
    request_data: UpdateRestoredStatusRequest,
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    try:
        s3_client = await S3Client.get_instance(configuration=configuration)
        cloudfront_client = CloudFront.get_instance(configuration)
        restore_service = S3DataRestorationService(
            central_db_session=db_session,
            s3_client=s3_client,
            cloudfront_client=cloudfront_client,
        )
        await restore_service.update_restored_status(request_data)
        return ApiResponse.success(
            message=CustomMessageCode.PROCESS_UPDATE_RESTORE_STATUS_SUCCESS.title,
            message_code=CustomMessageCode.PROCESS_UPDATE_RESTORE_STATUS_SUCCESS.code,
        )

    except CustomValueError as e:
        log.error(f"❌ Update restored s3 status error: {str(e)}")
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )

    except Exception as e:
        log.error(f"❌ Update restored s3 status unexpected error: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.PROCESS_UPDATE_RESTORE_STATUS_FAILED.title,
            message_code=CustomMessageCode.PROCESS_UPDATE_RESTORE_STATUS_FAILED.code,
        )


@router.post(
    path="/remove-expired-restored-data",
    summary="[For Lambda] Delete expired restored S3 data",
    dependencies=[
        DependsAPIVerifyKey(
            configuration.LAMBDA_SECRET_KEY,
            LambdaXRequestValue.LAMBDA_REMOVE_EXPIRED_RESTORED_DATA,
        )
    ],
)
@version(1, 0)
@measure_time
async def remove_expired_restored_data(
    request_data: DeleteExpiredRestoredDataRequest,
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    try:
        restore_service = S3DataRestorationService(
            central_db_session=db_session,
        )
        await restore_service.remove_expired_restored_data(request_data)
        return ApiResponse.success(
            message=CustomMessageCode.PROCESS_DELETE_EXPIRED_RESTORE_STATUS_SUCCESS.title,
            message_code=CustomMessageCode.PROCESS_DELETE_EXPIRED_RESTORE_STATUS_SUCCESS.code,
        )

    except CustomValueError as e:
        log.error(f"❌ Delete expired restored data error: {str(e)}")
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )

    except Exception as e:
        log.error(f"❌ Delete expired restored data unexpected error: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.PROCESS_DELETE_EXPIRED_RESTORE_STATUS_FAILED.title,
            message_code=CustomMessageCode.PROCESS_DELETE_EXPIRED_RESTORE_STATUS_FAILED.code,
        )


@router.post(
    path="/update-document-s3-status",
    summary="[For Lambda] Update document S3 status",
    dependencies=[
        DependsAPIVerifyKey(
            configuration.LAMBDA_SECRET_KEY,
            LambdaXRequestValue.LAMBDA_UPDATE_DOCUMENT_S3_STATUS,
        )
    ],
)
@version(1, 0)
@measure_time
async def update_document_s3_status(
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
):
    try:
        s3_client = await S3Client.get_instance(configuration=configuration)
        restore_service = S3DataRestorationService(
            central_db_session=db_session,
            s3_client=s3_client,
        )
        await restore_service.update_document_s3_status()
        return ApiResponse.success(
            message=CustomMessageCode.PROCESS_UPDATE_DOCUMENT_S3_STATUS_SUCCESS.title,
            message_code=CustomMessageCode.PROCESS_UPDATE_DOCUMENT_S3_STATUS_SUCCESS.code,
        )

    except CustomValueError as e:
        log.error(f"❌ Update document S3 status error: {str(e)}")
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )

    except Exception as e:
        log.error(f"❌ Update document S3 status unexpected error: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.PROCESS_UPDATE_DOCUMENT_S3_STATUS_FAILED.title,
            message_code=CustomMessageCode.PROCESS_UPDATE_DOCUMENT_S3_STATUS_FAILED.code,
        )


@router.get("/restore-document", response_model=Page[RestoreS3DataResponse])
@version(1, 0)
async def search_restore_document_data(
    params: Annotated[Params, Depends()],
    search_request: Annotated[
        SearchRestoreS3DataRequest, Depends(SearchRestoreS3DataRequest)
    ],
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        restore_service = S3DataRestorationService(
            tenant_db_session=db_session,
        )
        result = await restore_service.search_restore_document_data(
            search_request, params
        )
        return ApiResponse.success(data=result.model_dump(mode="json"))
    except CustomValueError as e:
        log.error(f"❌ Get document management data error: {str(e)}")
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )


@router.get("/restore-document/{id:int}")
@version(1, 0)
async def get_detail_document_management_data(
    id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        restore_service = S3DataRestorationService(
            tenant_db_session=db_session,
        )
        document_data = await restore_service.get_detail_restore_document_data(
            restore_s3_data_id=id
        )
        return ApiResponse.success(
            data=document_data.model_dump(mode="json"),
        )
    except CustomValueError as e:
        log.error(f"❌ Get document management data error: {str(e)}")
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
