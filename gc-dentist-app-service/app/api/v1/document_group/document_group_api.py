from typing import Annotated

from core.common.api_response import ApiResponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends  # type: ignore
from fastapi_pagination import Page
from schemas.requests.document_group_requests import (
    CreateDocumentGroupPayload,
    UpdateDocumentGroupPayload,
)
from schemas.responses.document_group_schema import ListDocumentGroupSchema
from services.document_group_service import DocumentGroupService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post("", summary="Create Document Group")
@version(1, 0)
async def create_document_group(
    payload: CreateDocumentGroupPayload,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        document_group_service = DocumentGroupService(db_session)
        document_group_id = await document_group_service.create_document_group(payload)
        return ApiResponse.success(
            data={"document_group_id": document_group_id},
            message=CustomMessageCode.DOCUMENT_GROUP_CREATED_SUCCESS.title,
            message_code=CustomMessageCode.DOCUMENT_GROUP_CREATED_SUCCESS.code,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error create_document_group CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f" ❌ Error create_document_group: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.DOCUMENT_GROUP_CREATED_FAILED.title,
            message_code=CustomMessageCode.DOCUMENT_GROUP_CREATED_FAILED.code,
        )


@router.get("/{document_group_id:int}", summary="Get Document Group")
@version(1, 0)
async def get_document_group(
    document_group_id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        document_group_service = DocumentGroupService(db_session)
        document_group = await document_group_service.get_document_group(
            document_group_id
        )
        return ApiResponse.success(
            data=document_group.model_dump(mode="json"),
            message=CustomMessageCode.DOCUMENT_GROUP_GET_SUCCESS.title,
            message_code=CustomMessageCode.DOCUMENT_GROUP_GET_SUCCESS.code,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error get_document_group CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error get_document_group: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.DOCUMENT_GROUP_GET_FAILED.title,
            message_code=CustomMessageCode.DOCUMENT_GROUP_GET_FAILED.code,
        )


@router.put("/{document_group_id:int}", summary="Update Document Group")
@version(1, 0)
async def update_document_group(
    document_group_id: int,
    payload: UpdateDocumentGroupPayload,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        document_group_service = DocumentGroupService(db_session)
        document_group = await document_group_service.update_document_group(
            document_group_id, payload
        )
        return ApiResponse.success(
            data={"document_group_id": document_group},
            message=CustomMessageCode.DOCUMENT_GROUP_UPDATED_SUCCESS.title,
            message_code=CustomMessageCode.DOCUMENT_GROUP_UPDATED_SUCCESS.code,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error update_document_group CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error update_document_group: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.DOCUMENT_GROUP_UPDATED_FAILED.title,
            message_code=CustomMessageCode.DOCUMENT_GROUP_UPDATED_FAILED.code,
        )


@router.get(
    "",
    response_model=Page[ListDocumentGroupSchema],
    summary="Get List Document Group",
)
@version(1, 0)
async def get_list_document_group(
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
    search: str | None = None,
):
    try:
        document_group_service = DocumentGroupService(db_session)
        document_group = await document_group_service.get_list_document_group(
            search=search
        )
        return ApiResponse.success(
            data=document_group.model_dump(mode="json"),
            message=CustomMessageCode.DOCUMENT_GROUP_GET_LIST_SUCCESS.title,
            message_code=CustomMessageCode.DOCUMENT_GROUP_GET_LIST_SUCCESS.code,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error get_list_document_group CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error get_list_document_group: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.DOCUMENT_GROUP_GET_LIST_FAILED.title,
            message_code=CustomMessageCode.DOCUMENT_GROUP_GET_LIST_FAILED.code,
        )
