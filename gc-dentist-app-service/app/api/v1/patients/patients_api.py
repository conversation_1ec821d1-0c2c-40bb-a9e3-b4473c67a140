from typing import Annotated

from core.common.api_response import ApiResponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends, Request, status  # type: ignore
from fastapi_pagination import Page
from schemas.requests.patient_requests import (
    CreatePatientPayloads,
    UpdatePatientPayloads,
)
from schemas.responses.patient_schema import PatientListSchema
from services.patient_service import PatientService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.constants import X_TENANT_UUID
from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.get("/patient-no")
@version(1, 0)
async def get_patient_no(
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        patient_service = PatientService(db_session)
        patient_no_data = await patient_service.auto_generate_patient_no()
        return ApiResponse.success(data=patient_no_data)
    except Exception as e:
        log.error(f"Error in method get_patient_no: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.PATIENT_NO_GENERATION_FAILED.title,
            message_code=CustomMessageCode.PATIENT_NO_GENERATION_FAILED.code,
        )


@router.post("", summary="Create Patient and Profile")
@version(1, 0)
async def create_patient_profile(
    request: Request,
    data: CreatePatientPayloads,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        patient_service = PatientService(db_session)
        tenant_uuid = request.headers.get(X_TENANT_UUID)
        patient_user_id = await patient_service.create_patient(data, tenant_uuid)
        return ApiResponse.success(
            data={"patient_user_id": patient_user_id},
            message="Create patient successfully.",
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error create_patient_profile CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f" ❌ Error create_patient_profile: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.PATIENT_CREATED_FAILED.title,
            message_code=CustomMessageCode.PATIENT_CREATED_FAILED.code,
        )


@router.get("/{patient_user_id:int}", summary="Get detail Patient")
@version(1, 0)
async def get_patient_profile(
    patient_user_id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        patient_service = PatientService(db_session)
        result = await patient_service.get_patient_profile(patient_user_id)
        return ApiResponse.success(data=result.model_dump(mode="json"))
    except CustomValueError as e:
        log.error(
            f"❌ Error get_patient_profile CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error in method get_patient_profile: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.PATIENT_DETAIL_FAILED.title,
            message_code=CustomMessageCode.PATIENT_DETAIL_FAILED.code,
        )


@router.put("/{patient_user_id:int}", summary="Edit Patient User and Patient Profile")
@version(1, 0)
async def update_patient_profile(
    patient_user_id: int,
    data: UpdatePatientPayloads,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        patient_service = PatientService(db_session)
        patient_user_id = await patient_service.update_patient_profile(
            patient_user_id, data
        )
        return ApiResponse.success(
            data={"patient_user_id": patient_user_id},
            message="Update patient successfully.",
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error update_patient_profile CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error in method update_patient_profile: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.PATIENT_UPDATED_FAILED.title,
            message_code=CustomMessageCode.PATIENT_UPDATED_FAILED.code,
        )


@router.delete("/{patient_user_id:int}")
@version(1, 0)
async def deactivate_patient_profile(
    patient_user_id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        patient_service = PatientService(db_session)
        await patient_service.deactivate_patient_profile(patient_user_id)
        return ApiResponse.success(message="Delete patient successfully.")
    except CustomValueError as e:
        log.error(
            f"❌ Error deactivate_patient_profile CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error in method deactivate_patient_profile: {str(e)}")
        return ApiResponse.error(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=CustomMessageCode.PATIENT_DELETED_FAILED.title,
            message_code=CustomMessageCode.PATIENT_DELETED_FAILED.code,
        )


@router.get("", response_model=Page[PatientListSchema])
@version(1, 0)
async def get_list_patient(
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
    search: str | None = None,
):
    try:
        patient_service = PatientService(db_session)
        paginated_result = await patient_service.list_patient(search=search)
        return ApiResponse.success(data=paginated_result.model_dump(mode="json"))
    except Exception as e:
        log.error(f"Error in method list_patient: {str(e)}")
        return ApiResponse.error(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=CustomMessageCode.PATIENT_GET_LIST_FAILED.title,
            message_code=CustomMessageCode.PATIENT_GET_LIST_FAILED.code,
        )
