from typing import Annotated

from core.common.api_response import ApiResponse
from core.constants import X_TENANT_UUID
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends, Request
from services.auth_service import AuthServices
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post("/me")
@version(1, 0)
async def get_user_information(
    request: Request,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        tenant_uuid = request.headers.get(X_TENANT_UUID)
        user_id = request.user.id

        auth_service = AuthServices()
        response = await auth_service.get_doctor_profile(
            db_session=db_session,
            tenant_uuid=tenant_uuid,
            user_id=user_id,
        )
        return ApiResponse.success(data=response.model_dump(mode="json"))
    except Exception as e:
        log.error(f"Error in me endpoint: {str(e)}")
        return ApiResponse.error(message=str(e))
