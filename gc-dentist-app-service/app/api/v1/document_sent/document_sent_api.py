"""
API endpoints for documents sent.
"""

from typing import Annotated

from core.common.api_response import ApiResponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends, Request
from schemas.requests.document_sent_schema import DocumentSentRequest
from services.document_sent_service import DocumentSentService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post("/")
@version(1, 0)
async def create_document_sent(
    request: Request,
    payload: DocumentSentRequest,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    """Create a new document sent record."""
    try:
        service = DocumentSentService(db_session)
        document_sent = await service.create_document_sent(
            payload=payload,
            sent_by=request.user.id,
        )

        return ApiResponse.success(
            data=document_sent.model_dump(mode="json"),
            message=CustomMessageCode.DOCUMENT_SENT_CREATED_SUCCESS.title,
            message_code=CustomMessageCode.DOCUMENT_SENT_CREATED_SUCCESS.code,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error create document sent CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error("❌ Error in create document sent endpoint: {}".format(str(e)))
        return ApiResponse.error(message=str(e))
