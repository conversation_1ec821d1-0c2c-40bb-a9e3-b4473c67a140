from pathlib import Path
from typing import ClassVar, Literal

from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    BASE_DIR: ClassVar[Path] = Path(__file__).resolve().parent

    model_config = SettingsConfigDict(
        env_file=str(BASE_DIR / ".env"),
        env_file_encoding="utf-8",
        extra="ignore",
    )

    PROJECT_NAME: str
    ENVIRONMENT: Literal["unittest", "develop", "testing", "staging", "production"]

    BACKEND_CORS_ORIGINS: list[str] = []
    BACKEND_CORS_METHODS: list[str] = [
        "GET",
        "POST",
        "PUT",
        "OPTIONS",
        "PATCH",
        "DELETE",
    ]

    POSTGRES_SERVER: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_PORT: int
    DB_ECHO: bool = False
    DB_INIT: bool = False

    READ_ONLY_POSTGRES_SERVER: str
    READ_ONLY_POSTGRES_USER: str
    READ_ONLY_POSTGRES_PASSWORD: str
    # READ_ONLY_POSTGRES_TENANT_DB_NAME: str
    READ_ONLY_POSTGRES_PORT: int

    # Redis configuration
    REDIS_HOST: str
    REDIS_PORT: int
    REDIS_PASSWORD: str | None = None
    REDIS_DATABASE: int
    REDIS_TIMEOUT: int
    REDIS_SSL: bool = False

    TOKEN_REFRESH_REDIS_PREFIX: str | None = None

    # Firebase configuration
    FIREBASE_ENABLED: bool = False
    FIREBASE_CERT_PATH: str | None = None
    FIREBASE_DRY_RUN: bool = True

    # Hmac configuration
    COMMUNICATE_SECRET_KEY: str

    AES_SECRET_ID_ROTATION: str
    AWS_SECRET_ROTATION_KEY_MAPPING: dict = {}
    AWS_SECRET_CURRENT_VERSION: str
    AES_SECRET_KEY_MAPPING: str

    AWS_REGION_NAME: str | None = None
    AWS_ACCESS_KEY_ID: str | None = None
    AWS_SECRET_ACCESS_KEY: str | None = None

    # S3 configuration
    S3_BUCKET_NAME: str | None = None
    S3_FOLDER_NAME: str | None = None
    EXPIRED_GENERATE_PRESIGNED_URL: int = 60 * 60 * 1  # Expiration time, unit: seconds
    S3_TEMP_FILE_TTL: int = 1  # unit: days
    S3_GLACIER_TRANSITION_DAYS: int = 90  # unit: days

    SES_REGION_NAME: str | None = "ap-northeast-1"
    SES_FROM_MAIL: str | None = None

    POSTGRES_GLOBAL_DB_NAME: str

    # Twilio configuration
    TWILIO_ACCOUNT_SID: str
    TWILIO_AUTH_TOKEN: str
    TWILIO_SERVICE_SID: str
    TWILIO_MESSAGE_SERVICE_SID: str

    IAPO_ACCESS_TOKEN: str | None = None
    IAPO_URL: str | None = None

    # Auth configuration
    AUTH_SERVICE_JWKS_URL: str = "http://127.0.0.1:8000/.well-known/jwks.json"
    AUTH_SERVICE_JWKS_LIFESPAN: int = 300  # in seconds
    JWT_ALGORITHM: str = "HS256"

    TOKEN_EXCLUDE_URLS: list[str] = [
        "/docs",
        "/openapi.json",
    ]

    TENANT_EXCLUDE_URLS: list[str] = [
        "/v1_0/health",
        "/v1_0/auth/login",
        "/docs",
        "/openapi.json",
    ]

    # Cloudfront
    CLOUDFRONT_URL: str | None = None
    CLOUDFRONT_PRIVATE_KEY_PATH: str | None = None
    CLOUDFRONT_PRIVATE_KEY: str | None = None
    CLOUDFRONT_PUBLIC_KEY_ID: str | None = None
    CLOUDFRONT_SIGNED_URL_EXPIRE_MINUTES: int | None = 10

    # Lambda API keys
    LAMBDA_SECRET_KEY: str | None = None

    # OPA (Open Policy Agent) configuration
    OPA_SERVICE_BASE_URL: str = "http://localhost:8181"


def get_settings() -> Settings:
    """Read configuration optimization writing method"""
    return Settings()


configuration = get_settings()
