from enums.base import StrEnum

from gc_dentist_shared.tenant_models import (
    MasterEra,
    MasterOralExamination,
    MasterOralExaminationMatrix,
    MasterPostalCode,
    MasterPrefecture,
)


class MasterModelEnum(StrEnum):
    """Enumeration for master model names."""

    M_ERA = "m_era"
    M_ORAL_EXAMINATIONS = "m_oral_examinations"
    M_ORAL_EXAMINATIONS_MATRIX = "m_oral_examinations_matrix"
    M_PREFECTURE = "m_prefecture"
    M_POSTAL_CODE = "m_postal_code"


class MappingModel:
    MASTER_MODEL_MAPPING = {  # noqa: RUF012
        MasterModelEnum.M_ERA.value: MasterEra,
        MasterModelEnum.M_ORAL_EXAMINATIONS.value: MasterOralExamination,
        MasterModelEnum.M_ORAL_EXAMINATIONS_MATRIX.value: MasterOralExaminationMatrix,
        MasterModelEnum.M_PREFECTURE.value: MasterPrefecture,
        MasterModelEnum.M_POSTAL_CODE.value: MasterPostalCode,
    }

    def get_model_fields(self, model_name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>):
        """Get the model class based on the model name."""
        model = self.MASTER_MODEL_MAPPING.get(model_name.value, None)
        if model:
            return model.__table__.c
        return None

    def get_pk_column_model(self, model_name: MasterModelEnum):
        """Get the primary key column of the model."""
        model = self.MASTER_MODEL_MAPPING.get(model_name.value, None)
        if model:
            return next(iter(model.__table__.primary_key.columns.values()), None)
        return None
