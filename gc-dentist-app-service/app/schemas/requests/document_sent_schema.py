from __future__ import annotations

from typing import Any

from pydantic import BaseModel, Field, HttpUrl, field_validator


class DocumentSentRequest(BaseModel):
    """Request schema to create (or upsert) a document_sent record."""

    document_uuid: str = Field(..., description="UUID of the document")
    patient_user_id: int = Field(..., ge=1, description="Patient user ID")
    document_url: HttpUrl | str = Field(..., max_length=500, description="Document URL")
    preview_document_data: dict[str, Any] | list[dict[str, Any]] | None = Field(
        default=None,
        description="Optional preview data (thumbnail/metadata)",
    )

    @field_validator("document_url")
    @classmethod
    def strip_url(cls, v: str) -> str:
        return v.strip() if isinstance(v, str) else v
