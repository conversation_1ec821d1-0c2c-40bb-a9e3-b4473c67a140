from datetime import date
from typing import Optional

from core.messages import CustomMessageCode
from pydantic import BaseModel, Field, field_validator, model_validator

from gc_dentist_shared.core.common.utils import ValidateDateString
from gc_dentist_shared.core.enums.document import (
    DocumentDataType,
    DocumentDisplayMode,
    DocumentExtension,
    DocumentStatus,
)


class DocumentQueryParams(BaseModel):
    tag: Optional[str] = None
    document_group_ids: Optional[list[int]] = None


class DocumentPreviewQueryParams(BaseModel):
    document_ids: Optional[list[int]] = None
    examination_date: Optional[date] = None

    @model_validator(mode="after")
    def validate_at_least_one_param(self):
        if not self.document_ids and not self.examination_date:
            raise ValueError(
                CustomMessageCode.DOCUMENT_MANAGEMENT_GET_PREVIEW_INVALID_PARAMS.title
            )
        return self


class DocumentDataSchema(BaseModel):
    file_index: str = Field(..., description="File index")
    file_key: str = Field(..., description="File key")
    file_size: int = Field(..., description="File size")


class DocumentManagementCreateSchema(BaseModel):
    patient_user_id: int = Field(..., description="Patient user ID")
    name: str = Field(
        ..., description="Name of the document", min_length=1, max_length=255
    )
    status: DocumentStatus = Field(
        default=DocumentStatus.DRAFT.value, description="Status of the document"
    )
    document_group_id: int = Field(..., description="Source of the document")
    data_type: DocumentDataType = Field(
        default=DocumentDataType.ORIGINAL.value, description="Type of the document"
    )
    document_data: list[DocumentDataSchema] = Field(
        ..., description="Data of the document"
    )
    preview_document_data: list[DocumentDataSchema] = Field(
        ..., description="Data of the document"
    )
    display_mode: int = Field(
        default=DocumentDisplayMode.LIST_IMAGE.value,
        description="Display mode of the document",
    )
    examination_date: Optional[str] = Field(..., description="Examination date")
    extra_data: Optional[dict] = Field({}, description="Extra data of the document")
    document_extension: DocumentExtension = Field(
        default=DocumentExtension.PAGE.value, description="Extension of the document"
    )

    @field_validator("examination_date", mode="before")
    @classmethod
    def validate_examination_date(cls, v: str) -> str:
        if v is not None:
            ValidateDateString.validate(v)
        return v

    @field_validator("status")
    @classmethod
    def validate_status(cls, v: DocumentStatus) -> DocumentStatus:
        if v not in [DocumentStatus.DRAFT, DocumentStatus.ACTIVATED]:
            raise ValueError(CustomMessageCode.DOCUMENT_MANAGEMENT_INVALID_STATUS.title)
        return v

    @field_validator("document_data")
    @classmethod
    def check_not_empty_document_data(cls, v):
        if not v:
            raise ValueError(
                CustomMessageCode.DOCUMENT_MANAGEMENT_ERROR_EMPTY_DOCUMENT_DATA.title
            )
        return v

    @field_validator("preview_document_data")
    @classmethod
    def check_not_empty_preview_document_data(cls, v):
        if not v:
            raise ValueError(
                CustomMessageCode.DOCUMENT_MANAGEMENT_ERROR_EMPTY_PREVIEW_DOCUMENT_DATA.title
            )
        return v


class DocumentManagementUpdateSchema(BaseModel):
    patient_user_id: int = Field(..., description="Patient user ID")
    name: Optional[str] = Field(
        None, description="Name of the document", min_length=1, max_length=255
    )
    status: Optional[DocumentStatus] = Field(None, description="Status of the document")
    document_data: Optional[list[DocumentDataSchema]] = Field(
        None, description="Data of the document"
    )
    preview_document_data: Optional[list[DocumentDataSchema]] = Field(
        None, description="Data of the document"
    )
    examination_date: Optional[str] = Field(None, description="Medical history ID")
    display_mode: Optional[int] = Field(
        None, description="Display mode of the document"
    )
    extra_data: Optional[dict] = Field(None, description="Extra data of the document")
    override_flag: Optional[bool] = Field(
        True,
        description="Override Flag - True to update existing version, False to create new version",
    )
    data_type: Optional[DocumentDataType] = Field(
        None, description="Type of the document"
    )

    @field_validator("examination_date", mode="before")
    @classmethod
    def validate_examination_date(cls, v: str) -> str:
        if v is not None:
            ValidateDateString.validate(v)
        return v

    @field_validator("status")
    @classmethod
    def validate_status(cls, v: DocumentStatus) -> DocumentStatus:
        if v not in [DocumentStatus.DRAFT, DocumentStatus.ACTIVATED]:
            raise ValueError(CustomMessageCode.DOCUMENT_MANAGEMENT_INVALID_STATUS.title)
        return v

    @field_validator("document_data")
    @classmethod
    def check_not_empty_document_data(cls, v):
        if not v:
            raise ValueError(
                CustomMessageCode.DOCUMENT_MANAGEMENT_ERROR_EMPTY_DOCUMENT_DATA.title
            )
        return v

    @field_validator("preview_document_data")
    @classmethod
    def check_not_empty_preview_document_data(cls, v):
        if not v:
            raise ValueError(
                CustomMessageCode.DOCUMENT_MANAGEMENT_ERROR_EMPTY_PREVIEW_DOCUMENT_DATA.title
            )
        return v
