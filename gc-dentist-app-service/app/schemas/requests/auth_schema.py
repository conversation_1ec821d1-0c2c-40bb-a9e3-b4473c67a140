from core.messages import CustomMessageCode
from pydantic import BaseModel, field_validator

from gc_dentist_shared.core.enums.s3_enums import S3PrefixNameEnum, S3RoleEnum


class S3GeneratedPresignedUrlRequest(BaseModel):
    file_names: list[str]
    prefix_name: S3PrefixNameEnum
    role: S3RoleEnum
    id: int

    @field_validator("file_names")
    @classmethod
    def validate_file_names(cls, value):
        if not value or not all(
            isinstance(name, str)
            and name.strip()
            and "." in name
            and not name.startswith(".")
            and not name.endswith(".")
            for name in value
        ):
            raise ValueError(CustomMessageCode.VALUE_ERROR_INVALID_FILE_NAMES.title)
        return value
