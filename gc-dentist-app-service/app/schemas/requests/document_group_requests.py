from typing import Optional

from pydantic import BaseModel, Field


class CreateDocumentGroupPayload(BaseModel):
    name: str = Field(..., min_length=1, description="Name of the document group")
    group_parent_id: Optional[int] = Field(default=None, description="Group parent id")


class UpdateDocumentGroupPayload(BaseModel):
    name: Optional[str] = Field(default=None, description="Name of the document group")
    group_parent_id: Optional[int] = Field(default=None, description="Group parent id")
