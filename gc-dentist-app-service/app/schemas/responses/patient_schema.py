from datetime import datetime
from typing import Optional

from configuration.settings import configuration
from core.constants import DISPLAY_DATE_FORMAT, PATIENT_FIELDS_ENCRYPTED
from pydantic import (
    BaseModel,
    ConfigDict,
    computed_field,
    field_serializer,
    model_validator,
)

from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.common.utils import (
    ValidateDateString,
    convert_datetime_with_timezone,
)


class PatientUserResponseSchema(BaseModel):
    id: int
    patient_no: str
    username: str


class PatientProfileDetailSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    last_name: Optional[str]
    first_name: Optional[str]
    full_name: Optional[str]
    last_name_kana: Optional[str]
    first_name_kana: Optional[str]
    full_name_kana: Optional[str]
    home_phone: str | None = None
    phone: str | None = None
    country_code: str | None = None
    email: str | None = None
    gender: int | None = None
    date_of_birth: ValidateDateString
    prefecture_id: int | None = None
    postal_code: str | None = None
    address_1: str | None = None
    address_2: str | None = None
    address_3: str | None = None
    parent_name: str | None = None

    @field_serializer("date_of_birth")
    def serialize_dob(self, v) -> str:
        return v.strftime(DISPLAY_DATE_FORMAT)

    @model_validator(mode="before")
    @classmethod
    def validate(cls, values):
        aes_gcm = AesGCMRotation(configuration)
        for field in PATIENT_FIELDS_ENCRYPTED:
            if values.get(field):
                values[field] = aes_gcm.decrypt_data(values.get(field))

        return values


class PatientProfileResponseSchema(BaseModel):
    username: str
    patient_no: str
    is_adult: bool
    profile: PatientProfileDetailSchema


class ListItemPatientProfileSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    last_name: str | None = None
    first_name: str | None = None
    full_name: str | None = None
    last_name_kana: str | None = None
    first_name_kana: str | None = None
    full_name_kana: str | None = None
    gender: int | None = None
    date_of_birth: ValidateDateString

    @computed_field
    @property
    def age(self) -> int:
        if not self.date_of_birth:
            return 0
        if isinstance(self.date_of_birth, str):
            try:
                dob = datetime.strptime(self.date_of_birth, DISPLAY_DATE_FORMAT).date()
            except ValueError:
                return 0
        else:
            dob = self.date_of_birth

        now_japan = convert_datetime_with_timezone(datetime.now().astimezone())
        today = now_japan.date()
        return today.year - dob.year - ((today.month, today.day) < (dob.month, dob.day))

    @field_serializer("date_of_birth")
    def serialize_dob(self, v) -> str:
        return v.strftime(DISPLAY_DATE_FORMAT)

    @model_validator(mode="before")
    @classmethod
    def validate(cls, values):
        aes_gcm = AesGCMRotation(configuration)
        for field in PATIENT_FIELDS_ENCRYPTED:
            if values.get(field):
                values[field] = aes_gcm.decrypt_data(values.get(field))
        return values


class PatientListSchema(BaseModel):
    id: int
    patient_no: str
    profile: ListItemPatientProfileSchema
