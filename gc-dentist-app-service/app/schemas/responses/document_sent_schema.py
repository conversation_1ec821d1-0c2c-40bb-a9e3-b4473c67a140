from __future__ import annotations

from datetime import datetime
from typing import Any
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


class DocumentSentResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int = Field(..., description="Record ID")
    document_uuid: UUID = Field(..., description="Document UUID")
    patient_user_id: int = Field(..., description="Patient user ID")
    document_url: str = Field(..., description="Document URL")
    preview_document_data: dict[str, Any] | list[dict[str, Any]] | None = Field(
        default=None, description="Preview metadata"
    )
    read_status: int = Field(..., description="Read status")
    read_at: datetime | None = Field(default=None, description="Read at")
    sent_at: datetime = Field(..., description="Sent at")
    sent_by: int = Field(..., description="Sent by")
