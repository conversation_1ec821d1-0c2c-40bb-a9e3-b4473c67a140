from typing import Any, Optional

from pydantic import BaseModel, Field

from gc_dentist_shared.core.common.utils import (
    CustomEmailStr,
    PhoneNumberExistCountryCode,
    ValidateDateString,
    calculate_age,
)


class LoginResponseSchema(BaseModel):
    tenant_uuid: str
    token: str
    refresh_token: str
    # TODO - Add details response


class ClinicInfoResponseSchema(BaseModel):
    tenant_uuid: str = Field(
        ..., strip_whitespace=True, description="UUID of the clinic"
    )
    clinic_slug: str = Field(
        ..., strip_whitespace=True, description="Slug of the clinic"
    )
    clinic_name: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="Name of the clinic",
    )
    phone_number: PhoneNumberExistCountryCode = Field(
        ..., strip_whitespace=True, description="Phone number of the clinic"
    )
    email: CustomEmailStr = Field(
        ..., strip_whitespace=True, description="Email address of the clinic"
    )
    address_1: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="Address_1 of the clinic",
    )
    address_2: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="Address_2 of the clinic",
    )
    address_3: str = Field(
        ...,
        min_length=1,
        strip_whitespace=True,
        description="Address_3 of the clinic",
    )
    latitude: Optional[str] = Field(
        None,
        strip_whitespace=True,
        description="Latitude of the clinic location",
    )
    longitude: Optional[str] = Field(
        None,
        strip_whitespace=True,
        description="Longitude of the clinic location",
    )
    logo_url: Optional[str] = Field(
        None, strip_whitespace=True, description="Logo URL of the clinic"
    )
    opening_hours: Optional[dict[str, Any]] = Field(
        None, description="Opening hours of the clinic in JSON format"
    )


class DoctorProfileResponseSchema(BaseModel):
    id: int
    first_name: str
    last_name: str

    address_1: Optional[str | None] = None
    address_2: Optional[str | None] = None
    address_3: Optional[str | None] = None

    date_of_birth: Optional[ValidateDateString | None] = None
    gender: Optional[int | None] = None
    phone: Optional[str | None] = None
    country_code: Optional[str | None] = None
    email: Optional[str | None] = None
    avatar_url: Optional[str | None] = None
    extra_data: Optional[dict[str, Any]] = None

    @property
    def age(self) -> Optional[int]:
        if self.date_of_birth:
            return calculate_age(self.date_of_birth)
        return None

    clinic_info: ClinicInfoResponseSchema

    def model_dump(self, *args, **kwargs):
        data = super().model_dump(*args, **kwargs)
        data["age"] = self.age
        return data
