from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict


class RestoreS3DataResponse(BaseModel):
    id: int
    s3_url: str
    s3_url_temp: Optional[str] = None
    document_uuid: str
    version_id: int
    status: int
    requested_at: Optional[datetime] = None
    restored_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None


class ListRestoreS3DataResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    items: list[RestoreS3DataResponse] = None
