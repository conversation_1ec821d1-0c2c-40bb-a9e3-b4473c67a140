from typing import Optional

from pydantic import BaseModel, Field


class DocumentGroupDetailSchema(BaseModel):
    id: int = Field(..., description="Primary key ID")
    name: str = Field(..., description="Name of the document template")
    is_parent: bool = Field(..., description="Is parent of the document group")
    group_parent_id: Optional[int] = Field(default=None, description="Group parent id")


class DocumentGroupSchema(BaseModel):
    id: int = Field(..., description="Primary key ID")
    name: str = Field(..., description="Name of the document template")


class ListDocumentGroupSchema(BaseModel):
    """Grouped list item by document group with aggregated templates."""

    id: int
    name: str
    group_children: list[DocumentGroupSchema] = Field(
        default_factory=list,
    )
