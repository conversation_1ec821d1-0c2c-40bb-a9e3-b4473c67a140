from datetime import date, time
from typing import Optional

from enums.patient_enum import Gender
from pydantic import BaseModel, ConfigDict, field_validator


class PatientProfileSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    patient_user_id: int
    last_name: str | None = None
    first_name: str | None = None
    full_name: str | None = None
    last_name_kana: str | None = None
    first_name_kana: str | None = None
    full_name_kana: str | None = None
    gender: int
    date_of_birth: str | None = None
    is_adult: bool
    patient_no: str
    uuid: str | None = None
    phone: str | None = None
    home_phone: str | None = None
    parent_name: str | None = None
    address_1: str | None = None
    address_2: str | None = None
    address_3: str | None = None

    @field_validator("gender", mode="before")
    @classmethod
    def default_gender_if_none(cls, v):
        return Gender.MALE.value if v is None else v

    @field_validator("is_adult", mode="before")
    @classmethod
    def default_is_adult__if_none(cls, v):
        return False if v is None else v


class PatientWaitingResponseSchema(BaseModel):
    id: int
    room_number: int
    assigned_doctors: Optional[list[int]]
    visit_start_date: date
    visit_start_time: time
    visit_end_date: Optional[date]
    visit_end_time: Optional[time]
    status: int
    emergency_flag: int
    patient_profile: PatientProfileSchema


class ListPatientWaitingResponseSchema(BaseModel):
    room_number: int
    patient_list: list[PatientWaitingResponseSchema]


class PatientWaitingResponse(BaseModel):
    patient_waiting_id: int
    medical_history_id: Optional[int] = None
