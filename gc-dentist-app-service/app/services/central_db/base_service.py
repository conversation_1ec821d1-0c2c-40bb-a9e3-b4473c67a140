from core.constants import TenantClinicStatus
from sqlalchemy import select
from sqlalchemy.exc import <PERSON><PERSON><PERSON>rror, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import TenantClinic
from gc_dentist_shared.core.decorators.retry import retry_on_failure


class BaseCentralService:
    def __init__(self, central_db_session: AsyncSession):
        self.central_db_session = central_db_session

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="get_tenant_db_names",
    )
    async def get_tenant_db_names(self) -> list[str]:
        async with self.central_db_session.begin():
            query = select(TenantClinic.db_name).where(
                TenantClinic.status == TenantClinicStatus.SUCCESS.value
            )
            result = await self.central_db_session.execute(query)
            return result.scalars().all()
