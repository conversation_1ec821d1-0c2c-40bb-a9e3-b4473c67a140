"""
Service for managing documents sent to patients.
"""

# from datetime import datetime, timezone
from typing import Optional

from core.messages import CustomMessageCode
from schemas.requests.document_sent_schema import DocumentSentRequest
from schemas.responses.document_sent_schema import DocumentSentResponse
from sqlalchemy import exists, func
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.exc import DB<PERSON>IError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.enums.document_sent_enum import DocumentReadStatus
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models.document.document_managements import (
    DocumentManagement,
)
from gc_dentist_shared.tenant_models.document.document_sent import DocumentSent


class DocumentSentService:
    """Service class for documents sent operations."""

    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_document_sent_by_uuid_and_patient_user_id(
        self, document_uuid: str, patient_user_id: int
    ) -> Optional[DocumentSent]:
        query = select(DocumentSent).where(
            DocumentSent.document_uuid == document_uuid,
            DocumentSent.patient_user_id == patient_user_id,
        )
        result = await self.session.execute(query)
        document_sent = result.scalar_one_or_none()
        return document_sent

    async def validate_document_exists(
        self, document_uuid: str, patient_user_id: int
    ) -> bool:
        """
        Check if a document with the given document_uuid and patient_user_id exists in document_managements.
        """
        query = select(
            exists(DocumentManagement).where(
                DocumentManagement.document_uuid == document_uuid,
                DocumentManagement.patient_user_id == patient_user_id,
            )
        )
        result = await self.session.execute(query)
        return result.scalar()

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_document_sent",
    )
    async def create_document_sent(
        self,
        payload: DocumentSentRequest,
        sent_by: int,
    ) -> DocumentSentResponse:
        """
        Upsert a document_sent record and return response schema.
        """

        async with self.session.begin():
            # Validate that the document exists in document_managements
            if not await self.validate_document_exists(
                payload.document_uuid, payload.patient_user_id
            ):
                raise CustomValueError(
                    message=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.title,
                    message_code=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.code,
                )
            document_sent = await self._upsert_document_sent(payload, sent_by)

            return DocumentSentResponse.model_validate(document_sent)

    # region Private Methods
    async def _upsert_document_sent(
        self, payload: DocumentSentRequest, sent_by: int
    ) -> DocumentSent:

        # Build base insert values
        insert_values = {
            "document_uuid": payload.document_uuid,
            "patient_user_id": payload.patient_user_id,
            "document_url": str(payload.document_url),
            "preview_document_data": payload.preview_document_data,
            "read_status": DocumentReadStatus.UNREAD,
            "read_at": None,
            "sent_at": func.now(),
            "sent_by": sent_by,
        }

        insert_stmt = insert(DocumentSent).values(insert_values)

        immutable_cols = [
            "id",
            "document_uuid",
            "patient_user_id",
            "document_url",
            "preview_document_data",
        ]
        conflict_columns = ["document_uuid", "patient_user_id"]

        # Build update values excluding immutable columns
        update_values = {
            key: getattr(insert_stmt.excluded, key)
            for key in insert_values.keys()
            if key not in immutable_cols
        }

        upsert_stmt = insert_stmt.on_conflict_do_update(
            index_elements=conflict_columns,
            set_=update_values,
        ).returning(DocumentSent)

        result = await self.session.execute(upsert_stmt)
        return result.scalars().one()
