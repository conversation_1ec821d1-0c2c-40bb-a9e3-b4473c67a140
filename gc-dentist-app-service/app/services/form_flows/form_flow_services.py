from dataclasses import dataclass, field
from datetime import datetime, timezone
from typing import Any, Optional
from uuid import uuid4

from core.messages import CustomMessageCode
from enums.form_flows import EditActionEnum
from fastapi import status
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from schemas.requests.form_flow_requests import (
    FormFlowsCreate,
    FormFlowUpdate,
    FormItemUpdate,
    FormUpdate,
)
from schemas.responses.form_flow_responses import (
    FormFlowDetailResponse,
    ListFormFlowSchema,
)
from sqlalchemy import insert, select, text, update
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.log_time import measure_time
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models import Form, Form<PERSON>low, FormItem, FormItemGroup


@dataclass
class BulkEditForm<PERSON>low:
    """A data structure to hold raw data for bulk database data_edit_form_flow."""

    update_form_flow: dict[str, Any] = field(default_factory=dict)

    inserts_forms: list[dict[str, Any]] = field(default_factory=list)
    updates_forms: list[dict[str, Any]] = field(default_factory=list)

    inserts_groups: list[dict[str, Any]] = field(default_factory=list)
    updates_groups: list[dict[str, Any]] = field(default_factory=list)

    inserts_items: list[dict[str, Any]] = field(default_factory=list)
    updates_items: list[dict[str, Any]] = field(default_factory=list)


class FormFlowService:
    def __init__(self, session: AsyncSession):
        self.session = session

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_form_flows",
    )
    async def create_form_flows(self, obj_requests: FormFlowsCreate):
        await self._validate_create_form_flows(obj_requests=obj_requests)

        form_flow_uuid = str(uuid4())
        data_insert = self._build_bulk_insert_data(obj_requests, form_flow_uuid)

        await self._insert_form_flow_to_db(data_insert)

        return form_flow_uuid

    async def _validate_create_form_flows(self, obj_requests: FormFlowsCreate) -> None:
        async with self.session as db:
            result = await db.execute(
                select(FormFlow).where(
                    FormFlow.flow_name == obj_requests.flow_name,
                    FormFlow.is_active.is_(True),
                )
            )
            if result.scalar_one_or_none():
                raise CustomValueError(
                    status_code=status.HTTP_409_CONFLICT,
                    message_code=CustomMessageCode.FORM_FLOW_ERROR_NAME_EXISTS.code,
                    message=CustomMessageCode.FORM_FLOW_ERROR_NAME_EXISTS.title,
                )

    def _build_bulk_insert_data(self, obj_requests, form_flow_uuid) -> dict:
        form_flow = {
            "uuid": form_flow_uuid,
            "flow_name": obj_requests.flow_name,
            "flow_type": obj_requests.flow_type,
            "description": obj_requests.description,
            "version": obj_requests.version or "1.0",
        }
        forms_bulk, groups_bulk, items_bulk = [], [], []

        for form in obj_requests.forms:
            form_uuid = str(uuid4())

            forms_bulk.append(
                {
                    "uuid": form_uuid,
                    "form_flow_uuid": form_flow_uuid,
                    "form_name": form.form_name,
                    "description": form.description,
                    "order_index": form.order_index,
                }
            )

            for group in form.groups:
                group_uuid = str(uuid4())
                groups_bulk.append(
                    {
                        "uuid": group_uuid,
                        "form_uuid": form_uuid,
                        "title": group.title,
                        "description": group.description,
                        "display_type": group.display_type,
                        "order_index": group.order_index,
                    }
                )

                items_bulk.extend(
                    [
                        {
                            "uuid": str(uuid4()),
                            "form_uuid": form_uuid,
                            "form_item_group_uuid": group_uuid,
                            "label": item.label,
                            "sub_label": item.sub_label,
                            "field_type": item.field_type,
                            "item_side": item.item_side,
                            "required": item.required,
                            "is_favorite": item.is_favorite,
                            "extra_data": item.extra_data,
                            "order_index": item.order_index,
                        }
                        for item in group.items
                    ]
                )

            items_bulk.extend(
                [
                    {
                        "uuid": str(uuid4()),
                        "form_uuid": form_uuid,
                        "form_item_group_uuid": None,
                        "label": item.label,
                        "sub_label": item.sub_label,
                        "field_type": item.field_type,
                        "item_side": item.item_side,
                        "required": item.required,
                        "is_favorite": item.is_favorite,
                        "extra_data": item.extra_data,
                        "order_index": item.order_index,
                    }
                    for item in form.items
                ]
            )

        return dict(
            form_flow=form_flow,
            forms_bulk=forms_bulk,
            groups_bulk=groups_bulk,
            items_bulk=items_bulk,
        )

    async def _insert_form_flow_to_db(self, data_insert):
        async with self.session.begin():
            await self.session.execute(
                insert(FormFlow).values(**data_insert.get("form_flow"))
            )

            await self.session.execute(insert(Form), data_insert.get("forms_bulk"))
            if data_insert.get("groups_bulk"):
                await self.session.execute(
                    insert(FormItemGroup), data_insert.get("groups_bulk")
                )
            if data_insert.get("items_bulk"):
                await self.session.execute(
                    insert(FormItem), data_insert.get("items_bulk")
                )

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_detail_form_flow",
    )
    async def get_detail_form_flow(self, form_flow_uuid: str) -> dict:
        raw_sql = text(
            """
                WITH active_forms AS (
                    SELECT
                        f.uuid,
                        f.form_name,
                        f.description,
                        f.order_index,
                        f.is_deletable
                    FROM forms f
                    WHERE f.form_flow_uuid = :form_flow_uuid AND f.is_active
                ),
                form_items_data AS (
                    SELECT
                        i.form_uuid,
                        i.form_item_group_uuid,
                        jsonb_build_object(
                            'uuid',         i.uuid,
                            'label',        i.label,
                            'sub_label',    i.sub_label,
                            'field_type',   i.field_type,
                            'item_side',    i.item_side,
                            'required',     i.required,
                            'is_favorite',  i.is_favorite,
                            'extra_data',   i.extra_data,
                            'order_index',  i.order_index,
                            'is_deletable', i.is_deletable
                        ) AS item_json,
                        i.order_index
                    FROM form_items i
                    JOIN active_forms af ON af.uuid = i.form_uuid
                    WHERE i.is_active
                ),
                grouped_items AS (
                    SELECT
                        fid.form_item_group_uuid,
                        jsonb_agg(fid.item_json ORDER BY fid.order_index) AS items
                    FROM form_items_data fid
                    WHERE fid.form_item_group_uuid IS NOT NULL
                    GROUP BY fid.form_item_group_uuid
                ),
                groups_with_items AS (
                    SELECT
                        g.form_uuid,
                        jsonb_agg(
                            jsonb_build_object(
                                'uuid',         g.uuid,
                                'title',        g.title,
                                'description',  g.description,
                                'display_type', g.display_type,
                                'order_index',  g.order_index,
                                'is_deletable', g.is_deletable,
                                'items',        COALESCE(gi.items, '[]'::jsonb)
                            ) ORDER BY g.order_index
                        ) AS groups
                    FROM form_item_groups g
                    LEFT JOIN grouped_items gi ON gi.form_item_group_uuid = g.uuid
                    WHERE g.is_active AND g.form_uuid IN (SELECT uuid FROM active_forms)
                    GROUP BY g.form_uuid
                ),
                ungrouped_items AS (
                    SELECT
                        fid.form_uuid,
                        jsonb_agg(fid.item_json ORDER BY fid.order_index) AS items
                    FROM form_items_data fid
                    WHERE fid.form_item_group_uuid IS NULL
                    GROUP BY fid.form_uuid
                ),
                final_forms AS (
                    SELECT
                        jsonb_agg(
                            jsonb_build_object(
                                'uuid',         af.uuid,
                                'form_name',    af.form_name,
                                'description',  af.description,
                                'order_index',  af.order_index,
                                'is_deletable', af.is_deletable,
                                'groups',       COALESCE(gwi.groups, '[]'::jsonb),
                                'items',        COALESCE(ui.items, '[]'::jsonb)
                            ) ORDER BY af.order_index
                        ) AS forms
                    FROM active_forms af
                    LEFT JOIN groups_with_items gwi ON gwi.form_uuid = af.uuid
                    LEFT JOIN ungrouped_items ui ON ui.form_uuid = af.uuid
                )
                SELECT
                    ff.uuid,
                    ff.flow_name,
                    ff.flow_type,
                    ff.description,
                    ff.version,
                    ff.is_deletable,
                    COALESCE(fforms.forms, '[]'::jsonb) AS forms
                FROM form_flows ff
                LEFT JOIN final_forms fforms ON TRUE
                WHERE ff.uuid = :form_flow_uuid AND ff.is_active;
        """
        )
        async with self.session:
            result = await self.session.execute(
                raw_sql, {"form_flow_uuid": form_flow_uuid}
            )
            row = result.mappings().first()
            if not row:
                raise CustomValueError(
                    status_code=status.HTTP_404_NOT_FOUND,
                    message_code=CustomMessageCode.FORM_FLOW_ERROR_NOT_FOUND.code,
                    message=CustomMessageCode.FORM_FLOW_ERROR_NOT_FOUND.title,
                )

            return FormFlowDetailResponse(**row).model_dump()

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="list_form_flow",
    )
    @measure_time
    async def list_form_flow(self) -> Page[ListFormFlowSchema]:
        query = (
            select(
                FormFlow.uuid,
                FormFlow.flow_name,
                FormFlow.flow_type,
                FormFlow.created_at,
            )
            .where(FormFlow.is_active.is_(True))
            .order_by(FormFlow.created_at.desc())
        )

        return await paginate(self.session, query)

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="edit_form_flow",
    )
    @measure_time
    async def edit_form_flow(
        self, obj_requests: FormFlowUpdate, form_flow_uuid: str
    ) -> str:
        data_delete = await self._validate_edit_form_flow(
            obj_requests=obj_requests, form_flow_uuid=form_flow_uuid
        )

        data_edit_form_flow = self._seperator_edit_data_form_flow(
            obj_requests=obj_requests,
            form_flow_uuid=form_flow_uuid,
        )

        await self._execute_bulk_edit_form_flow(
            form_flow_uuid, data_edit_form_flow, data_delete=data_delete
        )

        return form_flow_uuid

    @measure_time
    async def _validate_edit_form_flow(
        self, obj_requests: FormFlowUpdate, form_flow_uuid: str
    ):
        # Step 1: Fetch the complete current state from the database.
        db_form_flow_data = await self.get_detail_form_flow(form_flow_uuid)

        # Step 2: Validate flow name uniqueness.
        if obj_requests.flow_name != db_form_flow_data["flow_name"]:
            async with self.session:
                result = await self.session.execute(
                    select(FormFlow).where(
                        FormFlow.flow_name == obj_requests.flow_name,
                        FormFlow.uuid != form_flow_uuid,
                        FormFlow.is_active.is_(True),
                    )
                )
                if result.scalar_one_or_none():
                    raise CustomValueError(
                        status_code=status.HTTP_409_CONFLICT,
                        message_code=CustomMessageCode.FORM_FLOW_ERROR_NAME_EXISTS.code,
                        message=CustomMessageCode.FORM_FLOW_ERROR_NAME_EXISTS.title,
                    )

        # Step 3: Prepare initial state by creating lookup maps from the DB data
        mapping_data = self.mapping_data_uuid(db_form_flow_data=db_form_flow_data)

        # Step 4: Validate uuid exists and deletable
        delete_data = self._validate_uuid_exists_and_deletable(
            obj_requests, mapping_data
        )

        return delete_data

    def mapping_data_uuid(self, db_form_flow_data: dict) -> dict[str, Any]:
        db_forms_map = {}
        # Example: {'form_uuid_1': {'uuid': 'form_uuid_1', 'name': 'Form A', ...}}
        db_groups_map = {}
        # Example: {'group_uuid_1': {'uuid': 'group_uuid_1', 'title': 'Group A', ...}}
        db_items_map = {}
        # Example: {'item_uuid_1': {'uuid': 'item_uuid_1', 'label': 'Item A', ...}}

        for form_data in db_form_flow_data["forms"]:  # noqa
            form_uuid = form_data["uuid"]
            # Populate form-level data
            db_forms_map[form_uuid] = form_data

            # Process groups within the form
            for group_data in form_data.get("groups", []):
                group_uuid = group_data["uuid"]
                # Populate group-level data
                db_groups_map[group_uuid] = group_data

                # Process items within the group
                for item_data in group_data.get("items", []):
                    item_uuid = item_data["uuid"]
                    db_items_map[item_uuid] = item_data

            # Process standalone items within the form
            for item_data in form_data.get("items", []):
                item_uuid = item_data["uuid"]
                db_items_map[item_uuid] = item_data

        return {
            "db_forms_map": db_forms_map,
            "db_groups_map": db_groups_map,
            "db_items_map": db_items_map,
        }

    def _validate_uuid_exists_and_deletable(
        self, obj_requests: FormFlowUpdate, data_mapping
    ):
        def __validate_uuid(
            data_item, db_map, data_uuids, error_code_not_found, error_code_conflict
        ):
            if data_item.action == EditActionEnum.CREATE:
                return

            if data_item.uuid in data_uuids:
                raise CustomValueError(
                    status_code=status.HTTP_409_CONFLICT,
                    message_code=error_code_conflict.code,
                    message=error_code_conflict.title.format(uuid=data_item.uuid),
                )

            if not data_item.uuid or data_item.uuid not in db_map:
                raise CustomValueError(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    message_code=error_code_not_found.code,
                    message=error_code_not_found.title.format(uuid=data_item.uuid),
                )

            data_uuids.add(data_item.uuid)

        def __validate_deletable(db_map, data_uuids_to_delete, error_code):
            for data_uuid in data_uuids_to_delete:
                if not db_map[data_uuid].get("is_deletable"):
                    raise CustomValueError(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        message_code=error_code.code,
                        message=error_code.title.format(uuid=data_uuid),
                    )

        db_forms_map = data_mapping["db_forms_map"]
        db_groups_map = data_mapping["db_groups_map"]
        db_items_map = data_mapping["db_items_map"]
        form_data_uuids, group_data_uuids, item_data_uuids = set(), set(), set()

        # Step 1: Validate uuid
        for form_data in obj_requests.forms:  # noqa
            __validate_uuid(
                form_data,
                db_forms_map,
                form_data_uuids,
                error_code_not_found=CustomMessageCode.FORM_FLOW_ERROR_FORM_NOT_FOUND,
                error_code_conflict=CustomMessageCode.FORM_FLOW_ERROR_FORM_CONFLICT,
            )

            for group_data in form_data.groups:
                __validate_uuid(
                    group_data,
                    db_groups_map,
                    group_data_uuids,
                    error_code_not_found=CustomMessageCode.FORM_FLOW_ERROR_GROUP_NOT_FOUND,
                    error_code_conflict=CustomMessageCode.FORM_FLOW_ERROR_GROUP_CONFLICT,
                )

                for item_data in group_data.items:
                    __validate_uuid(
                        item_data,
                        db_items_map,
                        item_data_uuids,
                        error_code_not_found=CustomMessageCode.FORM_FLOW_ERROR_ITEM_NOT_FOUND,
                        error_code_conflict=CustomMessageCode.FORM_FLOW_ERROR_ITEM_CONFLICT,
                    )

            for item_data in form_data.items:
                __validate_uuid(
                    item_data,
                    db_items_map,
                    item_data_uuids,
                    error_code_not_found=CustomMessageCode.FORM_FLOW_ERROR_ITEM_NOT_FOUND,
                    error_code_conflict=CustomMessageCode.FORM_FLOW_ERROR_ITEM_CONFLICT,
                )

        # Step 2: Validate deletable
        deletes_forms_uuids = set(db_forms_map.keys()) - form_data_uuids
        deletes_groups_uuids = set(db_groups_map.keys()) - group_data_uuids
        deletes_items_uuids = set(db_items_map.keys()) - item_data_uuids

        # When a parent entity is deletion -> Delete all children
        for form_uuid in deletes_forms_uuids:
            form_data = db_forms_map.get(form_uuid, {})
            for group_data in form_data.get("groups", []):
                deletes_groups_uuids.add(group_data["uuid"])

            for item_data in form_data.get("items", []):
                deletes_items_uuids.add(item_data["uuid"])

        for group_uuid in deletes_groups_uuids:
            group_data = db_groups_map.get(group_uuid, {})
            for item_data in group_data.get("items", []):
                deletes_items_uuids.add(item_data["uuid"])

        __validate_deletable(
            db_forms_map,
            deletes_forms_uuids,
            CustomMessageCode.FORM_FLOW_ERROR_FORM_NOT_DELETABLE,
        )
        __validate_deletable(
            db_groups_map,
            deletes_groups_uuids,
            CustomMessageCode.FORM_FLOW_ERROR_GROUP_NOT_DELETABLE,
        )
        __validate_deletable(
            db_items_map,
            deletes_items_uuids,
            CustomMessageCode.FORM_FLOW_ERROR_ITEM_NOT_DELETABLE,
        )

        return dict(
            deletes_forms_uuids=deletes_forms_uuids,
            deletes_groups_uuids=deletes_groups_uuids,
            deletes_items_uuids=deletes_items_uuids,
        )

    @measure_time
    def _seperator_edit_data_form_flow(
        self, obj_requests: FormFlowUpdate, form_flow_uuid: str
    ) -> BulkEditFormFlow:
        data_edit_form_flow = BulkEditFormFlow()

        # Prepare FormFlow update
        data_edit_form_flow.update_form_flow = obj_requests.model_dump(
            exclude={"forms"}
        )

        for form_data in obj_requests.forms:
            if form_data.action == EditActionEnum.UPDATE:
                update_data = form_data.model_dump(
                    exclude={"items", "groups", "action"}
                )
                if update_data:
                    data_edit_form_flow.updates_forms.append(update_data)

                self._seperator_data_form(
                    data_edit_form_flow=data_edit_form_flow,
                    parent_form_uuid=form_data.uuid,
                    form_data=form_data,
                )

            elif form_data.action == EditActionEnum.CREATE:
                new_form_uuid = str(uuid4())
                insert_data = form_data.model_dump(
                    exclude={"items", "groups", "action"}
                )
                insert_data["uuid"] = new_form_uuid
                insert_data["form_flow_uuid"] = form_flow_uuid
                data_edit_form_flow.inserts_forms.append(insert_data)
                self._seperator_data_form(
                    data_edit_form_flow=data_edit_form_flow,
                    parent_form_uuid=new_form_uuid,
                    form_data=form_data,
                )

        return data_edit_form_flow

    def _seperator_data_form(
        self,
        data_edit_form_flow: BulkEditFormFlow,
        parent_form_uuid: str,
        form_data: FormUpdate,
    ):
        """Helper to recursively prepare data_edit_form_flow for groups and items."""
        # Process groups
        for group_data in form_data.groups:
            if group_data.action == EditActionEnum.UPDATE:
                update_data = group_data.model_dump(exclude={"items", "action"})
                update_data["form_uuid"] = parent_form_uuid
                if update_data:
                    data_edit_form_flow.updates_groups.append(update_data)

                for item_data in group_data.items:
                    self._seperator_data_item(
                        data_edit_form_flow,
                        item_data,
                        parent_form_uuid=parent_form_uuid,
                        parent_group_uuid=group_data.uuid,
                    )

            elif group_data.action == EditActionEnum.CREATE:
                new_group_uuid = str(uuid4())
                insert_data = group_data.model_dump(exclude={"items", "action"})
                insert_data["uuid"] = new_group_uuid
                insert_data["form_uuid"] = parent_form_uuid
                data_edit_form_flow.inserts_groups.append(insert_data)
                for item_data in group_data.items:
                    self._seperator_data_item(
                        data_edit_form_flow,
                        item_data,
                        parent_form_uuid=parent_form_uuid,
                        parent_group_uuid=new_group_uuid,
                    )

        # Process standalone items
        for item_data in form_data.items:
            self._seperator_data_item(
                data_edit_form_flow, item_data, parent_form_uuid=parent_form_uuid
            )

    def _seperator_data_item(
        self,
        data_edit_form_flow: BulkEditFormFlow,
        item_data: FormItemUpdate,
        parent_form_uuid: str,
        parent_group_uuid: Optional[str] = None,
    ):
        data = item_data.model_dump(exclude={"action"})
        data["form_uuid"] = parent_form_uuid
        data["form_item_group_uuid"] = parent_group_uuid

        if item_data.action == EditActionEnum.UPDATE:
            data_edit_form_flow.updates_items.append(data)

        elif item_data.action == EditActionEnum.CREATE:
            data["uuid"] = str(uuid4())
            data_edit_form_flow.inserts_items.append(data)

    @measure_time
    async def _execute_bulk_edit_form_flow(
        self, form_flow_uuid, data_edit_form_flow: BulkEditFormFlow, data_delete
    ):
        async with self.session.begin():
            # --- DELETE (Soft delete) ---
            if data_delete.get("deletes_forms_uuids"):
                await self.session.execute(
                    update(Form)
                    .where(Form.uuid.in_(data_delete.get("deletes_forms_uuids")))
                    .values(is_active=False, deleted_at=datetime.now(timezone.utc))
                )

            if data_delete.get("deletes_groups_uuids"):
                await self.session.execute(
                    update(FormItemGroup)
                    .where(
                        FormItemGroup.uuid.in_(data_delete.get("deletes_groups_uuids"))
                    )
                    .values(is_active=False, deleted_at=datetime.now(timezone.utc))
                )

            if data_delete.get("deletes_items_uuids"):
                await self.session.execute(
                    update(FormItem)
                    .where(FormItem.uuid.in_(data_delete.get("deletes_items_uuids")))
                    .values(is_active=False, deleted_at=datetime.now(timezone.utc))
                )
            # --- INSERT ---
            if data_edit_form_flow.inserts_forms:
                await self.session.execute(
                    insert(Form), data_edit_form_flow.inserts_forms
                )

            if data_edit_form_flow.inserts_groups:
                await self.session.execute(
                    insert(FormItemGroup), data_edit_form_flow.inserts_groups
                )

            if data_edit_form_flow.inserts_items:
                await self.session.execute(
                    insert(FormItem), data_edit_form_flow.inserts_items
                )

            # --- UPDATE ---
            if data_edit_form_flow.update_form_flow:
                await self.session.execute(
                    update(FormFlow)
                    .where(FormFlow.uuid == form_flow_uuid)
                    .values(**data_edit_form_flow.update_form_flow)
                )

            if data_edit_form_flow.updates_forms:
                await self.session.run_sync(
                    lambda s: s.bulk_update_mappings(
                        Form, data_edit_form_flow.updates_forms
                    )
                )

            if data_edit_form_flow.updates_groups:
                await self.session.run_sync(
                    lambda s: s.bulk_update_mappings(
                        FormItemGroup, data_edit_form_flow.updates_groups
                    )
                )

            if data_edit_form_flow.updates_items:
                await self.session.run_sync(
                    lambda s: s.bulk_update_mappings(
                        FormItem, data_edit_form_flow.updates_items
                    )
                )

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="delete_form_flow",
    )
    async def delete_form_flow(self, form_flow_uuid: str) -> str:
        delete_data = await self._get_and_validate_data_delete_form_flow(form_flow_uuid)

        await self._execute_delete_form_flow(delete_data, form_flow_uuid)

        return form_flow_uuid

    async def _get_and_validate_data_delete_form_flow(self, form_flow_uuid):
        async with self.session:
            query = select(FormFlow.is_deletable).where(
                FormFlow.uuid == form_flow_uuid, FormFlow.is_active.is_(True)
            )
            result = await self.session.execute(query)
            row = result.mappings().first()
            if not row:
                raise CustomValueError(
                    status_code=status.HTTP_404_NOT_FOUND,
                    message_code=CustomMessageCode.FORM_FLOW_ERROR_NOT_FOUND.code,
                    message=CustomMessageCode.FORM_FLOW_ERROR_NOT_FOUND.title,
                )

            if not row["is_deletable"]:
                raise CustomValueError(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    message_code=CustomMessageCode.FORM_FLOW_ERROR_FORM_FLOW_NOT_DELETABLE.code,
                    message=CustomMessageCode.FORM_FLOW_ERROR_FORM_FLOW_NOT_DELETABLE.title,
                )

        raw_sql = text(
            """
                WITH active_forms AS (
                    SELECT f.uuid
                    FROM forms f
                    WHERE f.form_flow_uuid = :form_flow_uuid AND f.is_active
                )
                SELECT
                    COALESCE((SELECT jsonb_agg(af.uuid) FROM active_forms af), '[]'::jsonb) AS form_uuids,
                    COALESCE((
                        SELECT jsonb_agg(g.uuid)
                        FROM form_item_groups g
                        JOIN active_forms af ON g.form_uuid = af.uuid
                        WHERE g.is_active
                    ), '[]'::jsonb) AS group_uuids,
                    COALESCE((
                        SELECT jsonb_agg(i.uuid)
                        FROM form_items i
                        JOIN active_forms af ON i.form_uuid = af.uuid
                        WHERE i.is_active
                    ), '[]'::jsonb) AS item_uuids;
            """
        )
        async with self.session:
            result = await self.session.execute(
                raw_sql, {"form_flow_uuid": form_flow_uuid}
            )
            row = result.mappings().first()

        return dict(
            deletes_forms_uuids=row.get("form_uuids", []),
            deletes_groups_uuids=row.get("group_uuids", []),
            deletes_items_uuids=row.get("item_uuids", []),
        )

    async def _execute_delete_form_flow(self, delete_data, form_flow_uuid):
        async with self.session.begin():
            await self.session.execute(
                update(FormFlow)
                .where(FormFlow.uuid == form_flow_uuid)
                .values(is_active=False, deleted_at=datetime.now(timezone.utc))
            )

            if delete_data.get("deletes_forms_uuids"):
                await self.session.execute(
                    update(Form)
                    .where(Form.uuid.in_(delete_data.get("deletes_forms_uuids")))
                    .values(is_active=False, deleted_at=datetime.now(timezone.utc))
                )

            if delete_data.get("deletes_groups_uuids"):
                await self.session.execute(
                    update(FormItemGroup)
                    .where(
                        FormItemGroup.uuid.in_(delete_data.get("deletes_groups_uuids"))
                    )
                    .values(is_active=False, deleted_at=datetime.now(timezone.utc))
                )

            if delete_data.get("deletes_items_uuids"):
                await self.session.execute(
                    update(FormItem)
                    .where(FormItem.uuid.in_(delete_data.get("deletes_items_uuids")))
                    .values(is_active=False, deleted_at=datetime.now(timezone.utc))
                )
