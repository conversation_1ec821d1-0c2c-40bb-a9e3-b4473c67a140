from datetime import datetime, timezone

from configuration.settings import configuration
from core.constants import PATIENT_FIELDS_ENCRYPTED, PATIENT_FIELDS_HASHED
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase
from fastapi import status
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from schemas.requests.patient_requests import (
    CreatePatientPayloads,
    UpdatePatientPayloads,
)
from schemas.responses.patient_schema import (
    PatientListSchema,
    PatientProfileResponseSchema,
)
from sqlalchemy import func, select, update
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import GlobalUser, TenantPatientUserMapping
from gc_dentist_shared.core.common import utils
from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.common.strings import format_phone_number
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models.patient_profiles import PatientProfile
from gc_dentist_shared.tenant_models.patient_users import PatientUser


class PatientService:
    def __init__(self, session: AsyncSession):
        self.session = session

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="auto_generate_patient_no",
    )
    async def auto_generate_patient_no(self) -> str:
        async with self.session.begin():
            query = select(func.nextval("patient_users_patient_no_seq"))
            result = await self.session.execute(query)
            patient_no = result.scalar_one_or_none()
            return str(patient_no).zfill(11)

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_patient",
    )
    async def create_patient(
        self, data: CreatePatientPayloads, tenant_uuid: str
    ) -> int:
        await self.validate_patient_data(data)
        patient_no = data.patient_no
        password = utils.generate_password()

        patient_user = PatientUser(
            patient_no=patient_no,
            is_adult=data.is_adult,
            username=patient_no,
        )
        patient_user.set_password(password, tenant_uuid)
        data_profile = data.profile.model_dump(mode="json")

        aes_gcm = AesGCMRotation(configuration)
        encrypted_result = aes_gcm.encrypt_and_hash_selected_fields(
            data_profile, PATIENT_FIELDS_ENCRYPTED, PATIENT_FIELDS_HASHED
        )

        patient_profile = PatientProfile(**encrypted_result)

        async with self.session.begin():
            self.session.add(patient_user)
            await self.session.flush()

            patient_profile.patient_user_id = patient_user.id
            self.session.add(patient_profile)

            if patient_profile.phone:
                await self.create_global_user(
                    phone=data.profile.phone,
                    country_code=data.profile.country_code,
                    password=password,
                    day_of_birth=data.profile.date_of_birth.isoformat(),
                    patient_no=patient_no,
                    patient_user_id=patient_user.id,
                    tenant_uuid=tenant_uuid,
                )

        return patient_user.id

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_global_user",
    )
    async def create_global_user(
        self,
        phone,
        country_code,
        password,
        day_of_birth,
        patient_no,
        patient_user_id,
        tenant_uuid,
    ):
        aes_gcm = AesGCMRotation(configuration)
        phone_number = format_phone_number(
            phone=phone,
            country_code=country_code,
        )
        async with CentralDatabase.get_instance_db() as central_session:
            async with central_session.begin():
                global_user = await central_session.scalar(
                    select(GlobalUser).where(
                        GlobalUser.username_hash == aes_gcm.sha256_hash(phone_number)
                    )
                )

                if not global_user:
                    global_user = GlobalUser(
                        username=aes_gcm.encrypt_data(phone_number),
                        username_hash=aes_gcm.sha256_hash(phone_number),
                        day_of_birth=aes_gcm.encrypt_data(day_of_birth),
                        day_of_birth_hash=aes_gcm.sha256_hash(day_of_birth),
                    )
                    global_user.set_password(password, tenant_uuid)
                    central_session.add(global_user)
                    await central_session.flush()

                global_user_id = None
                if aes_gcm.sha256_hash(day_of_birth) == global_user.day_of_birth_hash:
                    global_user_id = global_user.id

                user_mapping = TenantPatientUserMapping(
                    global_user_id=global_user_id,
                    tenant_uuid=tenant_uuid,
                    patient_user_id=patient_user_id,
                    patient_no=aes_gcm.encrypt_data(patient_no),
                )
                central_session.add(user_mapping)
                await central_session.flush()

    async def validate_patient_data(self, data: CreatePatientPayloads):
        async with self.session.begin():
            query = select(PatientUser.id).where(
                PatientUser.patient_no == data.patient_no
            )
            result = await self.session.execute(query)
            exists = result.scalar_one_or_none()
            if exists:
                raise CustomValueError(
                    status_code=status.HTTP_409_CONFLICT,
                    message_code=CustomMessageCode.PATIENT_CLINICAL_EXISTS.code,
                    message=CustomMessageCode.PATIENT_CLINICAL_EXISTS.title,
                )

            aes_gcm = AesGCMRotation(configuration)
            query = select(PatientProfile.id).where(
                PatientProfile.phone_hash == aes_gcm.sha256_hash(data.profile.phone),
                PatientProfile.country_code == data.profile.country_code,
            )
            result = await self.session.execute(query)
            phone_exists = result.scalar_one_or_none()
            if phone_exists:
                raise CustomValueError(
                    status_code=status.HTTP_409_CONFLICT,
                    message_code=CustomMessageCode.PATIENT_PHONE_EXISTS.code,
                    message=CustomMessageCode.PATIENT_PHONE_EXISTS.title,
                )
        return True

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="validate_patient_user",
    )
    async def validate_patient_user(self, patient_user_id: int) -> PatientUser:
        """Validate if the patient user exists"""
        async with self.session.begin():
            query = (
                select(PatientUser.id)
                .join(PatientProfile, PatientUser.id == PatientProfile.patient_user_id)
                .where(PatientUser.id == patient_user_id, PatientUser.status.is_(True))
            )
            result = await self.session.execute(query)
            patient_user = result.scalar_one_or_none()
            if not patient_user:
                raise CustomValueError(
                    message_code=CustomMessageCode.PATIENT_NOT_FOUND.code,
                    message=CustomMessageCode.PATIENT_NOT_FOUND.title,
                )
            return patient_user

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_patient_profile",
    )
    async def get_patient_profile(
        self, patient_user_id: int
    ) -> PatientProfileResponseSchema:
        """Get patient profile by ID"""

        patient_profile = func.json_build_object(
            "last_name",
            PatientProfile.last_name,
            "first_name",
            PatientProfile.first_name,
            "full_name",
            PatientProfile.full_name,
            "last_name_kana",
            PatientProfile.last_name_kana,
            "first_name_kana",
            PatientProfile.first_name_kana,
            "full_name_kana",
            PatientProfile.full_name_kana,
            "home_phone",
            PatientProfile.home_phone,
            "phone",
            PatientProfile.phone,
            "email",
            PatientProfile.email,
            "gender",
            PatientProfile.gender,
            "date_of_birth",
            PatientProfile.date_of_birth,
            "prefecture_id",
            PatientProfile.prefecture_id,
            "postal_code",
            PatientProfile.postal_code,
            "address_1",
            PatientProfile.address_1,
            "address_2",
            PatientProfile.address_2,
            "address_3",
            PatientProfile.address_3,
            "parent_name",
            PatientProfile.parent_name,
            "country_code",
            PatientProfile.country_code,
        ).label("profile")

        query = (
            select(
                PatientUser.username,
                PatientUser.patient_no,
                PatientUser.is_adult,
                patient_profile,
            )
            .join(PatientProfile, PatientUser.id == PatientProfile.patient_user_id)
            .where(PatientUser.id == patient_user_id, PatientUser.status.is_(True))
        )
        async with self.session.begin():
            result = await self.session.execute(query)
            row = result.mappings().first()

        if not row:
            raise CustomValueError(
                status_code=status.HTTP_404_NOT_FOUND,
                message_code=CustomMessageCode.PATIENT_NOT_FOUND.code,
                message=CustomMessageCode.PATIENT_NOT_FOUND.title,
            )

        return PatientProfileResponseSchema(**row)

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="update_patient_profile",
    )
    async def update_patient_profile(
        self, patient_user_id: int, data: UpdatePatientPayloads
    ) -> int:

        await self.validate_patient_user(patient_user_id)
        update_data = data.model_dump(exclude_unset=True, mode="json")
        if not update_data:
            return patient_user_id

        user_data_to_update = {
            key: value for key, value in update_data.items() if key != "profile"
        }
        profile_data_to_update = update_data.get("profile", {})

        async with self.session.begin():
            if user_data_to_update:
                stmt_user = (
                    update(PatientUser)
                    .where(PatientUser.id == patient_user_id)
                    .values(**user_data_to_update)
                )
                await self.session.execute(stmt_user)

            if profile_data_to_update:
                encrypted_result = AesGCMRotation(
                    configuration
                ).encrypt_and_hash_selected_fields(
                    profile_data_to_update,
                    PATIENT_FIELDS_ENCRYPTED,
                    PATIENT_FIELDS_HASHED,
                )

                stmt_profile = (
                    update(PatientProfile)
                    .where(PatientProfile.patient_user_id == patient_user_id)
                    .values(**encrypted_result)
                )
                await self.session.execute(stmt_profile)

            return patient_user_id

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="deactivate_patient_profile",
    )
    async def deactivate_patient_profile(self, patient_user_id: int) -> bool:
        """Deactivate patient profile by ID"""
        await self.validate_patient_user(patient_user_id)

        stmt = (
            update(PatientUser)
            .where(PatientUser.id == patient_user_id, PatientUser.status.is_(True))
            .values(status=False, deleted_at=datetime.now(timezone.utc))
        )
        async with self.session.begin():
            await self.session.execute(stmt)

        return True

    async def list_patient(self, search: str | None = None) -> Page[PatientListSchema]:
        profile_json = func.json_build_object(
            "last_name",
            PatientProfile.last_name,
            "first_name",
            PatientProfile.first_name,
            "full_name",
            PatientProfile.full_name,
            "last_name_kana",
            PatientProfile.last_name_kana,
            "first_name_kana",
            PatientProfile.first_name_kana,
            "full_name_kana",
            PatientProfile.full_name_kana,
            "gender",
            PatientProfile.gender,
            "date_of_birth",
            PatientProfile.date_of_birth,
        ).label("profile")

        query = (
            select(PatientUser.id, PatientUser.patient_no, profile_json)
            .select_from(PatientUser)
            .join(PatientProfile, PatientUser.id == PatientProfile.patient_user_id)
            .where(PatientUser.status.is_(True))
            .order_by(PatientUser.created_at.desc())
        )

        # Filter by search if provided
        if search:
            search_pattern = f"%{search}%"
            query = query.where(
                (PatientUser.patient_no.ilike(search_pattern))
                | (PatientProfile.last_name.ilike(search_pattern))
                | (PatientProfile.first_name.ilike(search_pattern))
                | (PatientProfile.last_name_kana.ilike(search_pattern))
                | (PatientProfile.first_name_kana.ilike(search_pattern))
            )

        return await paginate(self.session, query, unique=False)
