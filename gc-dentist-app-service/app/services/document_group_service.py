from typing import Optional

from core.messages import CustomMessageCode
from enums.document_group_enum import DocumentGroupKeyName
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from schemas.requests.document_group_requests import (
    CreateDocumentGroupPayload,
    UpdateDocumentGroupPayload,
)
from schemas.responses.document_group_schema import (
    DocumentGroupDetailSchema,
    DocumentGroupSchema,
)
from sqlalchemy import and_, func, select
from sqlalchemy.exc import DB<PERSON>IError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import aliased

from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models import DocumentGroup


class DocumentGroupService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def _find_document_group(
        self,
        session: AsyncSession,
        document_group_name: str | None = None,
        exclude_id: int | None = None,
        filters: dict | None = None,
    ):
        conditions = []

        if document_group_name:
            conditions.append(DocumentGroup.name == document_group_name.strip())

        if exclude_id is not None:
            conditions.append(DocumentGroup.id != exclude_id)

        if filters:
            field_map = {
                "id": DocumentGroup.id,
                "key_name": DocumentGroup.key_name,
                "is_parent": DocumentGroup.is_parent,
                "group_parent_id": DocumentGroup.group_parent_id,
                "deleted_at": DocumentGroup.deleted_at,
            }
            for key, value in filters.items():
                model_field = field_map.get(key)
                if model_field is not None:
                    if value is None:  # noqa
                        conditions.append(model_field.is_(None))
                    else:
                        conditions.append(model_field == value)

        stmt = select(DocumentGroup).where(and_(*conditions))

        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_document_group",
    )
    async def create_document_group(self, data: CreateDocumentGroupPayload) -> int:
        async with self.session.begin():
            document_group = await self._find_document_group(
                session=self.session,
                document_group_name=data.name,
                filters={"deleted_at": None},
            )

            if document_group:
                raise CustomValueError(
                    message_code=CustomMessageCode.DOCUMENT_GROUP_EXISTS.code,
                    message=CustomMessageCode.DOCUMENT_GROUP_EXISTS.title,
                )

            # Create New
            if not document_group:
                parent_id = await self.get_parent_document_group_id(
                    data.group_parent_id
                )
                new_document_group = DocumentGroup(
                    name=data.name.strip(),
                    group_parent_id=parent_id,
                    key_name=await self.create_key_name(data.name.strip()),
                )
                self.session.add(new_document_group)
                await self.session.flush()
                return new_document_group.id

            # update if have
            for field, value in data.model_dump(exclude_unset=True).items():
                setattr(document_group, field, value)
            document_group.deleted_at = None

            await self.session.flush()
            return document_group.id

    async def create_key_name(self, name: str) -> str:
        key_name = name.lower().strip()
        key_name = key_name.replace(" ", "_")
        key_name = key_name.replace("%", "")
        return key_name

    async def get_parent_document_group_id(
        self, group_parent_id: int | None
    ) -> int | None:
        if group_parent_id:
            group_parent = await self._find_document_group(
                self.session,
                filters={"id": group_parent_id, "deleted_at": None},
            )
        else:
            group_parent = await self._find_document_group(
                self.session,
                filters={
                    "key_name": DocumentGroupKeyName.OTHER.value,
                    "deleted_at": None,
                },
            )
        return group_parent.id if group_parent else None

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_document_group",
    )
    async def get_document_group(
        self, document_group_id: int
    ) -> DocumentGroupDetailSchema:
        """Get a document group by id"""
        async with self.session:
            query = select(
                DocumentGroup.id,
                DocumentGroup.name,
                DocumentGroup.is_parent,
                DocumentGroup.group_parent_id,
            ).where(
                DocumentGroup.id == document_group_id,
                DocumentGroup.deleted_at.is_(None),
            )
            result = await self.session.execute(query)
            row = result.mappings().first()

            if not row:
                raise CustomValueError(
                    message_code=CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.code,
                    message=CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.title,
                )
            return DocumentGroupDetailSchema(**row)

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="update_document_group",
    )
    async def update_document_group(
        self, document_group_id: int, data: UpdateDocumentGroupPayload
    ) -> int:
        """Update a document group by id"""
        async with self.session.begin():
            query = select(DocumentGroup).where(
                DocumentGroup.id == document_group_id,
                DocumentGroup.deleted_at.is_(None),
            )
            result = await self.session.execute(query)
            document_group = result.scalar_one_or_none()

            if not document_group:
                raise CustomValueError(
                    message_code=CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.code,
                    message=CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.title,
                )

            if data.name and data.name != document_group.name:
                exists_document_group = await self._find_document_group(
                    session=self.session,
                    document_group_name=data.name,
                    exclude_id=document_group_id,
                )
                if exists_document_group:
                    raise CustomValueError(
                        message_code=CustomMessageCode.DOCUMENT_GROUP_EXISTS.code,
                        message=CustomMessageCode.DOCUMENT_GROUP_EXISTS.title,
                    )

            update_fields = data.model_dump(exclude_unset=True)
            if "name" in update_fields:
                document_group.key_name = await self.create_key_name(
                    update_fields["name"].strip()
                )
            for field, value in update_fields.items():
                setattr(document_group, field, value)

            await self.session.flush()
            await self.session.refresh(document_group)

            return document_group.id

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_list_document_group",
    )
    async def get_list_document_group(
        self, search: Optional[str] = None
    ) -> Page[DocumentGroupSchema]:
        parent = aliased(DocumentGroup)
        child = aliased(DocumentGroup)

        group_children = (
            select(
                func.coalesce(
                    func.json_agg(
                        func.json_build_object(
                            "id",
                            child.id,
                            "name",
                            child.name,
                            "children",
                            func.json_build_array(),
                        )
                    ),
                    func.json_build_array(),
                )
            )
            .where(child.group_parent_id == parent.id)
            .correlate(parent)
            .scalar_subquery()
        )

        query = (
            select(
                parent.id.label("id"),
                parent.name.label("name"),
                group_children.label("group_children"),
            )
            .select_from(parent)
            .where(parent.is_parent.is_(True), parent.deleted_at.is_(None))
            .order_by(parent.id.asc())
        )

        if search:
            query = query.where(parent.name.ilike(f"%{search}%"))

        return await paginate(self.session, query, unique=False)
