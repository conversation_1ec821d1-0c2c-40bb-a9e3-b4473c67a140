from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.tenant_models import ClinicSourceMapping


class ClinicService:
    def __init__(self, session: AsyncSession = None):
        self.session = session

    async def get_clinic_source_mapping(self, source: str):
        stmt = select(ClinicSourceMapping).where(ClinicSourceMapping.source == source)

        async with self.session:
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()
