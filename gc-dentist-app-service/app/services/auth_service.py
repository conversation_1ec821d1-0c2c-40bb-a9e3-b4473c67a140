from configuration.settings import configuration
from core.constants import FIELDS_ENCRYPTED
from core.messages import CustomMessageCode
from schemas.responses.auth_schema import (
    ClinicInfoResponseSchema,
    DoctorProfileResponseSchema,
)
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.tenant_models import ClinicInformation, DoctorProfile


class AuthServices:
    async def get_doctor_profile(
        self, db_session: AsyncSession, user_id: int, tenant_uuid: str
    ) -> DoctorProfileResponseSchema:
        async with db_session:
            result = await db_session.execute(
                select(DoctorProfile).where(
                    DoctorProfile.doctor_user_id == user_id,
                )
            )

            user = result.scalar_one_or_none()
            if not user:
                raise ValueError(CustomMessageCode.USER_NOT_FOUND.title)

            result = await db_session.execute(
                select(ClinicInformation).where(
                    ClinicInformation.tenant_uuid == tenant_uuid,
                    ClinicInformation.is_active.is_(True),
                )
            )
            clinic = result.scalar_one_or_none()
            if not clinic:
                raise ValueError(CustomMessageCode.CLINIC_INFO_NOT_FOUND.title)

            doctor_data = user.__dict__.copy()
            doctor_data["id"] = user_id

            doctor_data = AesGCMRotation(configuration).decrypt_selected_fields(
                data=doctor_data,
                fields=FIELDS_ENCRYPTED,
            )

            return DoctorProfileResponseSchema(
                **doctor_data,
                clinic_info=ClinicInfoResponseSchema(**clinic.__dict__),
            )
