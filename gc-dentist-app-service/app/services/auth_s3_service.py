import uuid

from configuration.settings import configuration
from schemas.requests.auth_schema import S3GeneratedPresignedUrlRequest
from services.common.validate_service import ValidateService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common.s3_bucket import S3Client
from gc_dentist_shared.core.common.utils import sanitize_filename
from gc_dentist_shared.core.enums.s3_enums import S3RoleEnum, S3TypeObject


class S3AuthService:
    async def generate_presigned_url(
        self,
        db_session: AsyncSession,
        obj: S3GeneratedPresignedUrlRequest,
        tenant_uuid: str,
    ):
        """
        Generate a presigned URL for S3 object access.
        """
        async with db_session:
            role_validators = {
                S3RoleEnum.DOCTOR.value: lambda: ValidateService().validate_doctor_users(
                    db_session=db_session,
                    doctor_ids=[obj.id],
                ),
                S3RoleEnum.PATIENT.value: lambda: ValidateService().validate_patient_user(
                    db_session=db_session,
                    patient_id=obj.id,
                ),
            }

            validator = role_validators.get(obj.role)
            await validator()
        return await self.s3_create_presigned_url(
            tenant_uuid=tenant_uuid,
            role=obj.role.value,
            id=obj.id,
            prefix_name=obj.prefix_name.value,
            file_names=obj.file_names,
        )

    async def s3_create_presigned_url(
        self,
        tenant_uuid: str,
        role: str,
        id: int,
        prefix_name: str,
        file_names: list[str],
    ):
        """
        Create a presigned URL for S3 object upload.
        """
        results = []
        s3_client = await S3Client.get_instance(configuration)
        for name in file_names:
            data_name = sanitize_filename(name, max_len=120)
            object_name = f"{data_name['file_name']}_{uuid.uuid4()}.{data_name['ext']}"

            prefix = f"{tenant_uuid}/{role}/{id}/{prefix_name}"

            key = s3_client.generate_key_s3(
                prefix=prefix,
                object_name=object_name,
            )
            presigned_url = await s3_client.generate_presigned_url(
                key=key,
                type_object=S3TypeObject.PUT_OBJECT.value,
            )
            results.append(
                {
                    "file_name": f"{data_name['file_name']}.{data_name['ext']}",
                    "presigned_url": presigned_url,
                    "object_name": key,
                }
            )
        return results
