from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from sqlalchemy import select
from sqlalchemy.exc import <PERSON><PERSON><PERSON><PERSON>r, OperationalError

from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import ClinicConfiguration


@retry_on_failure(
    exceptions=(OperationalError, DBAPIError),
    log_prefix="get_clinic_storage_by_tenant_uuid",
)
async def get_clinic_storage_by_tenant_uuid(tenant_uuid: str) -> int:
    try:
        async with TenantDatabase.get_instance_tenant_db() as tenant_session:
            async with tenant_session.begin():
                query = select(ClinicConfiguration).where(
                    ClinicConfiguration.tenant_uuid == tenant_uuid
                )

                result = await tenant_session.execute(query)
                clinic_config = result.scalar_one_or_none()

                if not clinic_config:
                    raise CustomValueError(
                        message=CustomMessageCode.CLINIC_CONFIGURATION_NOT_FOUND.title,
                        message_code=CustomMessageCode.CLINIC_CONFIGURATION_NOT_FOUND.code,
                    )

                default_storage = clinic_config.default_storage or 0
                extra_storage = clinic_config.extra_storage or 0
                total_storage = default_storage + extra_storage

                log.info(
                    f"📊 Clinic storage from tenant DB for {tenant_uuid}: {total_storage}GB"
                )

                return total_storage

    except CustomValueError:
        raise
    except Exception as e:
        log.error(
            f"❌ Database error getting storage limit for clinic {tenant_uuid}: {e}"
        )
        raise CustomValueError(
            message=CustomMessageCode.CLINIC_CONFIGURATION_GET_FAILED.title,
            message_code=CustomMessageCode.CLINIC_CONFIGURATION_GET_FAILED.code,
        )
