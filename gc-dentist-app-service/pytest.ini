[pytest]
asyncio_mode = strict
asyncio_default_fixture_loop_scope = function
pythonpath = .
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short
usefixtures = mock_authentication
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    skip_middleware_auth: marks tests that should skip the global mock_authentication fixture
env =
    PYTHONPATH = .
filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning
    ignore::RuntimeWarning
    ignore::FutureWarning
    ignore::PendingDeprecationWarning
    ignore::ImportWarning
    ignore::ResourceWarning
    ignore::UnicodeWarning
    ignore::BytesWarning
    ignore::SyntaxWarning
    ignore::DeprecationWarning:.*
    ignore::UserWarning:.*
    ignore::RuntimeWarning:.*
    ignore::FutureWarning:.*
    ignore::PendingDeprecationWarning:.*
    ignore::ImportWarning:.*
    ignore::ResourceWarning:.*
    ignore::UnicodeWarning:.*
    ignore::BytesWarning:.*
    ignore::SyntaxWarning:.*
